class CreateApWalletShareItems < ActiveRecord::Migration[7.0]
  def change
    create_table :ap_wallet_share_items do |t|
      t.references :ap_table, :null => false
      t.string :shared_type_analysis
      t.string :item_type, :default => 'addressable_wallet_share'
      t.string :product_service_name
      t.string :description
      t.datetime :discarded_at, :index => true
      
      t.timestamps
    end
  end
end
