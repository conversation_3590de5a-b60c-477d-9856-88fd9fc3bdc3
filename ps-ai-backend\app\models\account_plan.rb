# frozen_string_literal: true

class AccountPlan < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  belongs_to :organization
  belongs_to :account_plan_group
  belongs_to :model_template, optional: true
  belongs_to :owner_organization_user,
             optional: true,
             class_name: 'OrganizationUser',
             foreign_key: 'owner_organization_user_id'
  belongs_to :last_updated_by_organization_user,
             optional: true,
             class_name: 'OrganizationUser',
             foreign_key: 'last_updated_by_organization_user_id'

  has_many :ap_tables

  enum status: string_enum('active', 'inactive')

  before_create :set_version_number
  after_commit :increase_version_number, on: :create

  after_create :populate_ap_tables

  def set_version_number
    plan_group = self.account_plan_group
    return if self.version.present? || plan_group.blank?

    plan_group.with_lock do
      to_be_used = plan_group.latest_version_number + 1
      self.version = "New Version #{to_be_used}"
      @used_parent_tracker = true
    end
  end

  def increase_version_number
    return unless @used_parent_tracker
    
    plan_group = self.account_plan_group

    plan_group.with_lock do
      to_be_used = plan_group.latest_version_number + 1
      new_version = "New Version #{to_be_used}"

      if self.version == new_version
        plan_group.increment!(:latest_version_number)
      end
    end
  end

  def populate_ap_tables
    # positions
    ApTable.positions_list.each do |t|
      ap_table = ApTable.find_or_create_by!(
        table_category: 'position',
        table_type: t,
        account_plan_id: self.id
      )
    end

    # revenues
    ApTable.revenues_list.each do |t|
      ap_table = ApTable.find_or_create_by!(
        table_category: 'revenue',
        table_type: t,
        account_plan_id: self.id
      )
    end

    # strategies
    ApTable.strategies_list.each do |t|
      ap_table = ApTable.find_or_create_by!(
        table_category: 'strategy',
        table_type: t,
        account_plan_id: self.id
      )
    end
  end
end
