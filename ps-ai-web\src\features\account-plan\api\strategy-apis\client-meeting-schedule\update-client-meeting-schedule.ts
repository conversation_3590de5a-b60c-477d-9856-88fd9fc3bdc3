import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  APClientMeetingSchedule,
  APClientMeetingScheduleBaseData,
} from "@/features/account-plan/types/strategy-types";

export const updateClientMeetingSchedule = ({
  accountId,
  id,
  data,
}: {
  id: number;
  accountId: number;
  data?: APClientMeetingScheduleBaseData;
}): ApiResponse<APClientMeetingSchedule> => {
  return api.put(
    API_ROUTES.ACCOUNT_PLANS_CLIENT_MEETING_SCHEDULE_DETAIL(accountId, id),
    data
  );
};

type UseUpdateClientMeetingScheduleOptions = {
  mutationConfig?: MutationConfig<typeof updateClientMeetingSchedule>;
};

export const useUpdateClientMeetingSchedule = ({
  mutationConfig,
}: UseUpdateClientMeetingScheduleOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      if (invalidate) {
        await queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS,
            args[1].accountId,
            QUERY_KEYS.ACCOUNT_PLANS_CLIENT_MEETING_SCHEDULE,
          ],
        });
      }

      onSuccess?.(...args);
    },

    ...restConfig,
    mutationFn: updateClientMeetingSchedule,
  });
};
