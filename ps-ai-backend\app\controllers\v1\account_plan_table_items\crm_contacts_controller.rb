module V1
  class AccountPlanTableItems::CrmContactsController < ApiController
    authorize_auth_token! :all

    def index
        crm_type = params[:crm_type]
        raise ArgumentError, "CRM type is required" if crm_type.blank?

        account_plan = AccountPlan.find(params[:account_plan_id])
        organization = current_user_data[:organization]

        verify_account_plan_ownership(account_plan, current_user_data[:organization_user])

        contacts = CrmFetcherService.new(organization, crm_type).fetch_contacts

        formatted_contacts = contacts.map { |contact| crm_contact_format(contact) }

        render_json_array formatted_contacts
      rescue => e
        render_error(message: e.respond_to?(:message) ? e.message : e.to_s, status: :unprocessable_entity)
      end

      private

      def crm_contact_format(contact)
        {
          name: contact['properties']&.dig('firstname') || contact['FirstName'] || contact['firstname'],
          job_title: contact['properties']&.dig('jobtitle') || contact['Title'] || contact['jobtitle'],
          location: contact['properties']&.dig('city') || contact['City'] || contact['address1_city']
        }
      end

      def verify_account_plan_ownership(account_plan, organization_user)
        unless account_plan.organization_id == organization_user.organization_id
          raise ActiveRecord::RecordNotFound, "Not authorized to access this account plan"
        end
      end    
    end
end