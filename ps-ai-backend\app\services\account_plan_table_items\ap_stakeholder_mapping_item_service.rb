# frozen_string_literal: true

class AccountPlanTableItems::ApStakeholderMappingItemService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
  end

  def create(account_plan_id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApStakeholderMappingItem.new

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: 'position',
        table_type: 'stakeholder_mapping',
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item = ::AccountPlanTableItems::ApStakeholderMappingItem.create!(params)
    end

    OpenStruct.new(
      ap_stakeholder_mapping_item: ap_item
    )
  end

  def update(account_plan_id, id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApStakeholderMappingItem.find(id)

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: 'position',
        table_type: 'stakeholder_mapping',
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item.update!(params)
    end

    OpenStruct.new(
      ap_stakeholder_mapping_item: ap_item
    )
  end

  def index(account_plan_id, query_params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_table = ApTable.find_by(
      table_category: 'position',
      table_type: 'stakeholder_mapping',
      account_plan_id: account_plan_id
    )

    ap_item_reposity = ::AccountPlanTableItems::ApStakeholderMappingItems.new

    filter = query_params.slice(
      :search, :page, :per_page, :disable_pagination
    )
    filter = filter.merge(
      ap_table_id: ap_table&.id || -1,
      sort_column: 'id',
      sort_direction: 'asc'
    )

    filtered_items = ap_item_reposity.filter(filter)

    OpenStruct.new(
      ap_stakeholder_mapping_items: filtered_items
    )
  end

  def show(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApStakeholderMappingItem.find(id)
    
    OpenStruct.new(
      ap_stakeholder_mapping_item: ap_item
    ) 
  end

  def destroy(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApStakeholderMappingItem.find(id)

    ActiveRecord::Base.transaction do
      ::AccountPlanTableItems::ApTargetedPerceptionDevelopmentItem
        .where(ap_stakeholder_mapping_item_id: ap_item.id)
        .update(ap_stakeholder_mapping_item_id: nil)
      ::AccountPlanTableItems::ApInsightAndPerspectiveItem
        .where(ap_stakeholder_mapping_item_id: ap_item.id)
        .update(ap_stakeholder_mapping_item_id: nil)
      ::AccountPlanTableItems::ApClientMeetingScheduleItem
        .where(ap_stakeholder_mapping_item_id: ap_item.id)
        .update(ap_stakeholder_mapping_item_id: nil)
      ap_item.discard!
    end
  end
end
