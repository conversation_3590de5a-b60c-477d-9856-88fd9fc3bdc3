import { STORAGE_KEYS } from "@/constants/storage-keys";
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface AuthStore {
  isLogin: boolean;
  refreshToken: string | null;
  setRefreshToken: (token: string) => void;
  resetToken: () => void;
  _hasHydrated: boolean;
  setHasHydrated: (state: boolean) => void;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, _) => ({
      refreshToken: null,
      isLogin: false,
      setRefreshToken: (token: string) =>
        set({ refreshToken: token, isLogin: true }),
      resetToken: () => set({ refreshToken: null, isLogin: false }),
      _hasHydrated: false,
      setHasHydrated: (state) => {
        set({
          _hasHydrated: state,
        });
      },
    }),
    {
      name: STORAGE_KEYS.AUTH_STORE,
      onRehydrateStorage: (state) => {
        return () => state.setHasHydrated(true);
      },
      partialize: (state) => ({
        refreshToken: state.refreshToken,
        isLogin: state.isLogin,
      }),
    }
  )
);
