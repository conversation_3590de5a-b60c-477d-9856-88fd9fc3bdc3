# frozen_string_literal: true

module V1
  class ModelTemplateVariablesController < Api<PERSON>ontroller
    authorize_auth_token! :all

    def create
      input = ::V1::ModelTemplateVariableCreationInput.new(request_body)
      validate! input, capture_failure: true

      template_variable = service.create(input.output)

      render_json template_variable, ::V1::ModelTemplateVariableOutput, use: :format, status: :created
    end

    def update
      input = ::V1::ModelTemplateVariableUpdateInput.new(request_body)
      validate! input, capture_failure: true

      template_variable = service.update(params[:id], input.output)

      render_json template_variable, ::V1::ModelTemplateVariableOutput, use: :format, status: :ok
    end

    def index
      template_variables = service.index(query_params)

      render_json_array template_variables, ::V1::ModelTemplateVariableOutput, use: :format, status: :ok
    end

    def destroy
      service.destroy(params[:id])

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::ModelTemplateVariableOutput
    end

    def service
      @service ||= ::ModelTemplateVariableService.new(current_user_data)
    end
  end
end
