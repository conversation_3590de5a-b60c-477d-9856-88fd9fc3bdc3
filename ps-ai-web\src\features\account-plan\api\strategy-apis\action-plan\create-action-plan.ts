import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APActionPlan,
  APActionPlanBaseData,
} from "@/features/account-plan/types/strategy-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createActionPlan = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APActionPlanBaseData;
}): ApiResponse<APActionPlan> => {
  return api.post(API_ROUTES.ACCOUNT_PLANS_ACTION_PLAN(accountId), data);
};

type UseCreateActionPlanOptions = {
  mutationConfig?: MutationConfig<typeof createActionPlan>;
};

export const useCreateActionPlan = ({
  mutationConfig,
}: UseCreateActionPlanOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_ACTION_PLAN,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createActionPlan,
  });
};
