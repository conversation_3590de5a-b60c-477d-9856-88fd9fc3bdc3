# frozen_string_literal: true

class AccountPlanGroupService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
    @openai_service = OpenaiService.new(user_data)
    @user_data = user_data
  end

  def create(params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    # verify_roles(['owner', 'super_admin'], organization_user)

    organization_id = organization_user.organization_id

    params.delete(:account_plan_unique_id)

    company = params[:company]
    account_addressable_area = params[:account_addressable_area]
    location = params[:location]
    unique_id = (company.scan(/\b\w/) + account_addressable_area.scan(/\b\w/) + location.scan(/\b\w/)).join.upcase

    # sort by largest number from any account plan groups (include discarded) with similar unique code
    order_str = Arel.sql("COALESCE(NULLIF(split_part(account_plan_groups.account_plan_unique_id, '-', 2), '')::integer, NULL) DESC")
    plan_group = AccountPlanGroup
                  .unscoped
                  .where(organization_id: organization_id)
                  .where('account_plan_unique_id ilike ?', "#{unique_id}%")
                  .order(order_str)
                  .first
                  
    if plan_group.present?
      curr_number = plan_group.account_plan_unique_id.gsub("#{unique_id}-", '')
      curr_number = curr_number.to_i + 1
      unique_id += "-#{curr_number}"
    else
      unique_id += '-0'
    end

    params[:account_plan_unique_id] = unique_id
    params[:organization_id] = organization_id

    created_account_plan = AccountPlan.new
    org_users = []

    ActiveRecord::Base.transaction do
      plan_group = AccountPlanGroup.create!(params)

      account_plan_service = AccountPlanService.new(@user_data)
      params_create = {
        account_plan_group_id: plan_group.id,
        company: company,
        account_addressable_area: account_addressable_area,
        location: location,
        status: 'active'
      }
      result_ap = account_plan_service.create(params_create)
      created_account_plan = result_ap.account_plan
      org_users = result_ap.organization_users
    end

    OpenStruct.new(
      account_plan_group: plan_group.reload,
      account_plan: created_account_plan,
      organization_users: org_users
    )
  end

  def update(id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    # verify_roles(['owner', 'super_admin'], organization_user)

    organization_id = organization_user.organization_id
    
    if params.has_key?(:account_plan_unique_id)
      assert! params[:account_plan_unique_id].present?, on_error: 'Account ID cannot be empty'

      params[:account_plan_unique_id] = params[:account_plan_unique_id].upcase
      exist_unique = AccountPlanGroup
                      .where('account_plan_unique_id ilike ?', params[:account_plan_unique_id])
                      .where(organization_id: organization_id)
      assert! exist_unique.blank?, on_error: 'Account ID already exists. Please try another account ID name.'
    end

    plan_group_by_id = AccountPlanGroup.find_by(id: id)
    exist! account_plan_group_ownership(plan_group_by_id, organization_user), on_error: 'Plan ID not found'

    params[:organization_id] = organization_id

    ActiveRecord::Base.transaction do
      plan_group_by_id.update!(params)
    end

    OpenStruct.new(
      account_plan_group: plan_group_by_id
    )
  end

  def index(query_params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)

    plan_groups = ::AccountPlanGroups.new

    filter = query_params.slice(
      :ap_name, :ap_owner, :currency, :industry_id, :company, :account_plan_unique_id,
      :status, :search, :disable_pagination, :page, :per_page
    )

    if query_params.has_key?(:sort_column)
      sort = {
        sort_column: query_params[:sort_column],
        # TODO validate sort direction  
        sort_direction: query_params[:sort_direction] || 'ASC'
      }
      filter = filter.merge(sort: sort)
    end

    filter = filter.merge(
      organization_id: organization_user.organization_id
    )

    filtered = plan_groups.include(:account_plan_filter).filter(filter)

    if filter[:status].present?
      account_plans = AccountPlan.where(account_plan_group_id: filtered.pluck(:id).compact.uniq, status: filter[:status])
    else
      account_plans = AccountPlan.where(account_plan_group_id: filtered.pluck(:id).compact.uniq)
    end
    owner_organization_users = OrganizationUser.where(id: account_plans.pluck(:owner_organization_user_id).compact.uniq)
    industries = Industry.where(id: filtered.pluck(:industry_id).compact.uniq)

    OpenStruct.new(
      account_plan_groups: filtered,
      account_plans: account_plans,
      organization_users: owner_organization_users,
      industries: industries
    )
  end

  def show(id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)

    plan_group_by_id = AccountPlanGroup.find_by(id: id)
    exist! account_plan_group_ownership(plan_group_by_id, organization_user), on_error: 'Plan ID not found'

    account_plans = AccountPlan.where(account_plan_group_id: plan_group_by_id.id)
    owner_organization_users = OrganizationUser.where(id: account_plans.pluck(:owner_organization_user_id).compact.uniq)
    industries = Industry.where(id: plan_group_by_id.industry_id)



    OpenStruct.new(
      account_plan_group: plan_group_by_id,
      account_plans: account_plans,
      organization_users: owner_organization_users,
      industries: industries
    )
  end

  def destroy(id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    # verify_roles(['owner', 'super_admin'], organization_user)

    plan_group_by_id = AccountPlanGroup.find_by(id: id)
    exist! account_plan_group_ownership(plan_group_by_id, organization_user), on_error: 'Plan ID not found'

    ActiveRecord::Base.transaction do
      AccountPlan.where(account_plan_group_id: plan_group_by_id.id).update_all(
        discarded_at: Time.current,
        last_updated_by_organization_user_id: organization_user.id,
        status: 'inactive'
      )
      plan_group_by_id.discard!
    end
  end

  private

  def account_plan_group_ownership(plan_group, organization_user)
    plan_group&.organization_id == organization_user.organization_id
  end
end
