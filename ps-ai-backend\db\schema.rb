# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_04_22_125826) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "account_plan_groups", force: :cascade do |t|
    t.string "account_plan_unique_id"
    t.integer "latest_version_number", default: 0, null: false
    t.bigint "organization_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "status", default: "active", null: false
    t.string "currency", default: "US Dollar"
    t.text "account_addressable_area"
    t.string "location"
    t.string "company"
    t.bigint "industry_id"
    t.index ["discarded_at"], name: "index_account_plan_groups_on_discarded_at"
    t.index ["industry_id"], name: "index_account_plan_groups_on_industry_id"
    t.index ["organization_id", "account_plan_unique_id"], name: "plan_unique_id_per_org_index"
    t.index ["organization_id"], name: "index_account_plan_groups_on_organization_id"
  end

  create_table "account_plans", force: :cascade do |t|
    t.string "name"
    t.string "version"
    t.string "status"
    t.string "priority"
    t.datetime "review_date"
    t.integer "next_review_date"
    t.text "account_addressable_area"
    t.bigint "owner_organization_user_id"
    t.bigint "organization_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "model_template_id"
    t.boolean "generated_analysis", default: false
    t.datetime "last_generated_analysis_at"
    t.string "currency"
    t.string "company"
    t.string "location"
    t.string "industry"
    t.string "function"
    t.bigint "last_updated_by_organization_user_id"
    t.bigint "account_plan_group_id"
    t.index ["account_plan_group_id"], name: "index_account_plans_on_account_plan_group_id"
    t.index ["discarded_at"], name: "index_account_plans_on_discarded_at"
    t.index ["model_template_id"], name: "index_account_plans_on_model_template_id"
    t.index ["next_review_date"], name: "index_account_plans_on_next_review_date"
    t.index ["organization_id"], name: "index_account_plans_on_organization_id"
    t.index ["owner_organization_user_id"], name: "index_account_plans_on_owner_organization_user_id"
    t.index ["review_date"], name: "index_account_plans_on_review_date"
  end

  create_table "ap_action_plan_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.text "description"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ap_table_id"], name: "index_ap_action_plan_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_action_plan_items_on_discarded_at"
  end

  create_table "ap_circumstantial_analysis_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.string "item_type", default: "macro"
    t.string "description"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ap_table_id"], name: "index_ap_circumstantial_analysis_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_circumstantial_analysis_items_on_discarded_at"
  end

  create_table "ap_client_meeting_schedule_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.string "client"
    t.datetime "meeting_date"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "notes", default: ""
    t.bigint "ap_stakeholder_mapping_item_id"
    t.index ["ap_stakeholder_mapping_item_id"], name: "ap_cms_sm_item_index"
    t.index ["ap_table_id"], name: "index_ap_client_meeting_schedule_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_client_meeting_schedule_items_on_discarded_at"
  end

  create_table "ap_current_opportunity_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.datetime "close_date"
    t.string "product_service_name"
    t.integer "value"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "currency"
    t.index ["ap_table_id"], name: "index_ap_current_opportunity_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_current_opportunity_items_on_discarded_at"
  end

  create_table "ap_current_revenue_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.datetime "renewal_date"
    t.string "product_service_name"
    t.integer "value"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "currency"
    t.index ["ap_table_id"], name: "index_ap_current_revenue_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_current_revenue_items_on_discarded_at"
  end

  create_table "ap_historic_revenue_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.string "time_month"
    t.string "product_service_name"
    t.integer "value"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "currency"
    t.index ["ap_table_id"], name: "index_ap_historic_revenue_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_historic_revenue_items_on_discarded_at"
  end

  create_table "ap_insight_and_perspective_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.string "target_name"
    t.string "description"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "ap_stakeholder_mapping_item_id"
    t.index ["ap_stakeholder_mapping_item_id"], name: "ap_iap_sm_item_index"
    t.index ["ap_table_id"], name: "index_ap_insight_and_perspective_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_insight_and_perspective_items_on_discarded_at"
  end

  create_table "ap_missing_information_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.string "description"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ap_table_id"], name: "index_ap_missing_information_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_missing_information_items_on_discarded_at"
  end

  create_table "ap_potential_opportunity_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.datetime "close_date"
    t.string "product_service_name"
    t.integer "value"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "currency"
    t.index ["ap_table_id"], name: "index_ap_potential_opportunity_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_potential_opportunity_items_on_discarded_at"
  end

  create_table "ap_revenue_forecast_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.string "timespan"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "low_scenario"
    t.integer "realistic_scenario"
    t.integer "high_scenario"
    t.index ["ap_table_id"], name: "index_ap_revenue_forecast_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_revenue_forecast_items_on_discarded_at"
  end

  create_table "ap_stakeholder_mapping_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.string "name"
    t.string "job_title"
    t.string "location"
    t.string "influence"
    t.string "role", array: true
    t.string "perception"
    t.string "advocacy"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "coverage"
    t.index ["ap_table_id"], name: "index_ap_stakeholder_mapping_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_stakeholder_mapping_items_on_discarded_at"
  end

  create_table "ap_svot_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.string "item_type", default: "strength"
    t.string "description"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ap_table_id"], name: "index_ap_svot_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_svot_items_on_discarded_at"
  end

  create_table "ap_tables", force: :cascade do |t|
    t.bigint "account_plan_id", null: false
    t.string "table_type", null: false
    t.string "table_category", null: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_plan_id"], name: "index_ap_tables_on_account_plan_id"
    t.index ["discarded_at"], name: "index_ap_tables_on_discarded_at"
  end

  create_table "ap_targeted_perception_development_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.bigint "ap_stakeholder_mapping_item_id"
    t.text "action"
    t.text "result"
    t.text "leverage"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ap_stakeholder_mapping_item_id"], name: "ap_tpd_sm_item_index"
    t.index ["ap_table_id"], name: "index_ap_targeted_perception_development_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_targeted_perception_development_items_on_discarded_at"
  end

  create_table "ap_top_action_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.text "description"
    t.string "action_target"
    t.datetime "action_date"
    t.integer "order"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "how", default: ""
    t.boolean "completed", default: false
    t.index ["ap_table_id"], name: "index_ap_top_action_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_top_action_items_on_discarded_at"
  end

  create_table "ap_wallet_share_items", force: :cascade do |t|
    t.bigint "ap_table_id", null: false
    t.integer "shared_type_analysis"
    t.string "item_type", default: "addressable_wallet_share"
    t.string "product_service_name"
    t.string "description"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "currency"
    t.index ["ap_table_id"], name: "index_ap_wallet_share_items_on_ap_table_id"
    t.index ["discarded_at"], name: "index_ap_wallet_share_items_on_discarded_at"
  end

  create_table "chats", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.bigint "model_id", null: false
    t.bigint "account_plan_id"
    t.bigint "ap_table_id"
    t.bigint "creator_organization_user_id", null: false
    t.string "openai_thread_id"
    t.string "chat_type"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_plan_id"], name: "index_chats_on_account_plan_id"
    t.index ["ap_table_id"], name: "index_chats_on_ap_table_id"
    t.index ["discarded_at"], name: "index_chats_on_discarded_at"
    t.index ["model_id"], name: "index_chats_on_model_id"
    t.index ["organization_id"], name: "index_chats_on_organization_id"
  end

  create_table "currency_exchange_rates", force: :cascade do |t|
    t.string "base_currency"
    t.string "exchange_currency"
    t.integer "rates"
    t.string "rates_decimal"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["base_currency", "exchange_currency"], name: "base_exhange_currency_unique_index", unique: true
    t.index ["base_currency"], name: "index_currency_exchange_rates_on_base_currency"
    t.index ["discarded_at"], name: "index_currency_exchange_rates_on_discarded_at"
    t.index ["exchange_currency"], name: "index_currency_exchange_rates_on_exchange_currency"
  end

  create_table "industries", force: :cascade do |t|
    t.string "name"
    t.text "description", default: ""
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_industries_on_discarded_at"
  end

  create_table "llm_considerations", force: :cascade do |t|
    t.bigint "organization_id"
    t.string "usage_on"
    t.jsonb "result"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_llm_considerations_on_discarded_at"
    t.index ["organization_id"], name: "index_llm_considerations_on_organization_id"
  end

  create_table "messages", force: :cascade do |t|
    t.bigint "chat_id"
    t.string "sender"
    t.text "content"
    t.integer "tokens_used"
    t.string "openai_thread_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["chat_id"], name: "index_messages_on_chat_id"
    t.index ["discarded_at"], name: "index_messages_on_discarded_at"
  end

  create_table "model_template_input_sets", force: :cascade do |t|
    t.bigint "model_template_id", null: false
    t.text "rules"
    t.text "reference_output"
    t.string "reference_output_url"
    t.string "input_type"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_model_template_input_sets_on_discarded_at"
    t.index ["model_template_id"], name: "index_model_template_input_sets_on_model_template_id"
  end

  create_table "model_template_variables", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.bigint "model_template_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "variable_reference_url"
    t.string "ap_table_type"
    t.string "input_type"
    t.index ["discarded_at"], name: "index_model_template_variables_on_discarded_at"
    t.index ["model_template_id"], name: "index_model_template_variables_on_model_template_id"
  end

  create_table "model_templates", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.integer "max_tokens"
    t.float "temperature"
    t.text "instruction"
    t.text "rules"
    t.text "reference_output"
    t.bigint "organization_id"
    t.bigint "template_category_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "reference_output_url"
    t.bigint "creator_organization_user_id"
    t.string "model", default: "gpt-4o-mini", null: false
    t.string "status", default: "active", null: false
    t.bigint "last_updated_by_organization_user_id"
    t.bigint "account_plan_group_id"
    t.string "inputs", default: [], array: true
    t.index ["account_plan_group_id"], name: "index_model_templates_on_account_plan_group_id"
    t.index ["creator_organization_user_id"], name: "index_model_templates_on_creator_organization_user_id"
    t.index ["discarded_at"], name: "index_model_templates_on_discarded_at"
    t.index ["last_updated_by_organization_user_id"], name: "index_model_templates_on_last_updated_by_organization_user_id"
    t.index ["organization_id"], name: "index_model_templates_on_organization_id"
    t.index ["template_category_id"], name: "index_model_templates_on_template_category_id"
  end

  create_table "models", force: :cascade do |t|
    t.bigint "model_template_id"
    t.string "model"
    t.string "response_format_type"
    t.string "response_format_json_schema"
    t.string "openai_assistant_id"
    t.text "structured_prompt"
    t.string "openai_assistant_vector_store_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_models_on_discarded_at"
    t.index ["model_template_id"], name: "index_models_on_model_template_id"
  end

  create_table "openai_files", force: :cascade do |t|
    t.integer "object_id"
    t.string "object_class"
    t.string "object_class_column"
    t.string "openai_file_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_openai_files_on_discarded_at"
  end

  create_table "organization_crms", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.string "crm_type", null: false
    t.string "access_token", null: false
    t.string "refresh_token"
    t.string "instance_url"
    t.datetime "access_token_expires_at"
    t.boolean "active", default: true
    t.jsonb "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_id"], name: "index_organization_crms_on_organization_id"
  end

  create_table "organization_users", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "organization_id", null: false
    t.string "organization_identifier_id", default: ""
    t.boolean "prompt_tuning_permissions", default: false, null: false
    t.datetime "discarded_at"
    t.integer "team_leader_organization_user_id"
    t.string "country_code"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "role_id"
    t.string "status", default: "active", null: false
    t.index ["discarded_at"], name: "index_organization_users_on_discarded_at"
    t.index ["organization_id", "organization_identifier_id"], name: "organization_users_organization_identifier_index", unique: true
    t.index ["organization_id"], name: "index_organization_users_on_organization_id"
    t.index ["role_id"], name: "index_organization_users_on_role_id"
    t.index ["team_leader_organization_user_id"], name: "index_organization_users_on_team_leader_organization_user_id"
    t.index ["user_id"], name: "index_organization_users_on_user_id"
  end

  create_table "organizations", force: :cascade do |t|
    t.string "name"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "organization_code"
    t.bigint "last_used_identifier_id", default: 0, null: false
    t.integer "last_used_model_template_version_number", default: 0
    t.text "tagline"
    t.text "message"
    t.text "image_url"
    t.string "subdomain"
    t.string "primary_color", default: "#203864"
    t.string "secondary_color", default: "#ed7d31"
    t.string "ap_wallet_share_color", default: "#4287f5"
    t.string "ap_current_revenue_color", default: "#4287f5"
    t.string "ap_current_opportunity_color", default: "#4287f5"
    t.string "ap_potential_opportunity_color", default: "#4287f5"
    t.text "logo_url"
    t.string "unique_id"
    t.string "subdirectory"
    t.string "tier", default: "free", null: false
    t.string "primary_extra_light", default: "#d3dff1"
    t.string "primary_light", default: "#2f5292"
    t.index ["discarded_at"], name: "index_organizations_on_discarded_at"
    t.index ["subdirectory"], name: "organizations_subdirectory_unique_index", unique: true
    t.index ["unique_id"], name: "organizations_unique_id_index", unique: true
  end

  create_table "role_permissions", force: :cascade do |t|
    t.bigint "role_id"
    t.string "name"
    t.boolean "status", default: false
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_role_permissions_on_discarded_at"
    t.index ["role_id"], name: "index_role_permissions_on_role_id"
  end

  create_table "roles", force: :cascade do |t|
    t.string "name"
    t.bigint "organization_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_roles_on_discarded_at"
    t.index ["organization_id"], name: "index_roles_on_organization_id"
  end

  create_table "rss_feeds", force: :cascade do |t|
    t.bigint "rss_id"
    t.string "url"
    t.string "thumbnail"
    t.string "title"
    t.text "description"
    t.datetime "date_published"
    t.jsonb "authors", array: true
    t.string "status", default: "new_feed"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_rss_feeds_on_discarded_at"
    t.index ["rss_id"], name: "index_rss_feeds_on_rss_id"
    t.index ["status"], name: "index_rss_feeds_on_status"
  end

  create_table "rsses", force: :cascade do |t|
    t.string "rss_id"
    t.string "source_url"
    t.string "rss_feed_url"
    t.string "title"
    t.text "description"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "industry_id"
    t.index ["discarded_at"], name: "index_rsses_on_discarded_at"
    t.index ["industry_id"], name: "index_rsses_on_industry_id"
  end

  create_table "template_categories", force: :cascade do |t|
    t.string "name"
    t.boolean "validated", default: true
    t.boolean "general_category", default: false
    t.bigint "organization_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_template_categories_on_discarded_at"
    t.index ["organization_id"], name: "index_template_categories_on_organization_id"
  end

  create_table "token_requests", force: :cascade do |t|
    t.string "email"
    t.string "user_id"
    t.string "purpose"
    t.string "request_status"
    t.string "request_code"
    t.datetime "request_expiry_date"
    t.datetime "requested_at"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["discarded_at"], name: "index_token_requests_on_discarded_at"
    t.index ["request_expiry_date"], name: "index_token_requests_on_request_expiry_date"
    t.index ["requested_at"], name: "index_token_requests_on_requested_at"
    t.index ["user_id"], name: "index_token_requests_on_user_id"
  end

  create_table "user_invitations", force: :cascade do |t|
    t.string "email"
    t.string "invitation_status"
    t.string "invitation_code"
    t.datetime "invitation_expiry_date"
    t.bigint "organization_id"
    t.bigint "invited_by_organization_user_id"
    t.bigint "assigned_role_id"
    t.datetime "discarded_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "organization_identifier_id"
    t.index ["assigned_role_id"], name: "index_user_invitations_on_assigned_role_id"
    t.index ["discarded_at"], name: "index_user_invitations_on_discarded_at"
    t.index ["invitation_code"], name: "index_user_invitations_on_invitation_code", unique: true
    t.index ["invited_by_organization_user_id"], name: "index_user_invitations_on_invited_by_organization_user_id"
    t.index ["organization_id"], name: "index_user_invitations_on_organization_id"
  end

  create_table "users", force: :cascade do |t|
    t.text "name"
    t.text "email"
    t.text "photo_url"
    t.text "password_digest"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "last_name"
    t.string "contact"
    t.datetime "discarded_at"
    t.datetime "last_login_at"
    t.string "status", default: "active", null: false
    t.index ["discarded_at"], name: "index_users_on_discarded_at"
    t.index ["last_login_at"], name: "index_users_on_last_login_at"
  end

end
