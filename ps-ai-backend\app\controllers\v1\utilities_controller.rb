# frozen_string_literal: true

module V1
  class UtilitiesController < ApiController
    authorize_auth_token! :all

    def currency_exchange_rates
      currency_exchange_rates = service.currency_exchange_rates(query_params)

      render_json currency_exchange_rates
    end

    private

    def default_output
      ::V1::UtilityOutput
    end

    def service
      @service ||= ::UtilityService.new(current_user_data)
    end
  end
end
