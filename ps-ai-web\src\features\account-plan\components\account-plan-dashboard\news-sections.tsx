"use client";

import React, { useMemo } from "react";

import { useRssList } from "@/features/account-plan/api/rss/get-rss-list";
import { RSSData } from "@/features/account-plan/types/rss-types";
import { useIndustryList } from "../../api/get-industry-list";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { parseAsString, useQueryState } from "next-usequerystate";
import { Skeleton } from "@/components/ui/skeleton";
import Image from "next/image";
import NewIcon from "@/assets/news.png";

export const NewsSection = () => {
  const [filter, setFilter] = useQueryState(
    "news_filter",
    parseAsString.withDefault("all")
  );

  const { industryList } = useIndustryList({
    params: {
      disable_pagination: true,
      ap_active: true,
    },
  });

  const { rssList, isPending: isPendingRssList } = useRssList({
    params: {
      disable_pagination: true,
      ap_active: true,
      ...(filter !== "all" && {
        industry_id: industryList.find((v) => v.name.toLowerCase() === filter)
          ?.id,
      }),
    },
  });

  const filterOptions = useMemo(() => {
    const industries = industryList.map((v) => ({
      label: v.name,
      value: v.name.toLowerCase(),
    }));

    return [
      {
        label: "All",
        value: "all",
      },
      ...industries,
    ];
  }, [industryList]);

  const rssFeeds = useMemo(() => {
    const feeds = rssList.reduce(
      (acc, curr) => [...acc, ...curr.rss_feeds],
      [] as RSSData["rss_feeds"]
    );

    return feeds;
  }, [rssList]);

  return (
    <section className="h-[50vh] w-[35vw] shrink-0 overflow-y-hidden rounded-xl bg-white px-res-x-base py-res-y-lg">
      <div className="flex justify-between">
        <h1 className="mb-res-y-sm text-2xl font-bold text-primary-500">
          News
        </h1>
        <Select value={filter} onValueChange={setFilter}>
          <SelectTrigger
            className="h-[3.5vh] w-[11vw] px-res-x-xs text-left text-xs"
            iconClassName="!ml-[0]"
          >
            <SelectValue placeholder="Select Industry" />
          </SelectTrigger>
          <SelectContent>
            {filterOptions.map((v, idx) => (
              <SelectItem key={idx} value={v.value} className="text-xs">
                {v.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid max-h-[41.5vh] gap-res-y-base overflow-y-auto pr-res-x-sm pt-res-y-base scrollbar-thin">
        {rssFeeds.map((v, idx) => (
          <a
            key={idx}
            className="group"
            target="_blank"
            href={v.url ?? ""}
            rel="noreferrer"
          >
            <p className="mb-res-y-sm text-primary-500 group-hover:underline">
              {v.title}
            </p>
            <p className="text-xs text-neutral-400">{v.description}</p>
          </a>
        ))}
        {isPendingRssList ? (
          <>
            <Skeleton className="h-[10vh]" />
            <Skeleton className="h-[10vh]" />
            <Skeleton className="h-[10vh]" />
          </>
        ) : (
          !rssFeeds?.length && (
            <div className="mx-auto mt-[5vw] flex flex-col items-center text-primary-400">
              <Image src={NewIcon} width={80} height={80} alt="news icon" /> No
              news matching this category
            </div>
          )
        )}
      </div>
    </section>
  );
};
