# frozen_string_literal: true

class AccountPlanTableItems::ApInsightAndPerspectiveItems < ::ApplicationRepository
  def default_scope
    ::AccountPlanTableItems::ApInsightAndPerspectiveItem
  end

  def filter_by_ap_table_id(ap_table_id)
    @scope.where(ap_table_id: ap_table_id)
  end

  def filter_by_search(search)
    @scope.where('target_name ilike ?', "%#{search}%")
  end

  def filter_by_exist_stakeholder(bool)
    if bool
      @scope.select(
        "ap_insight_and_perspective_items.*",
        "CASE WHEN sm.discarded_at is not null THEN null ELSE ap_insight_and_perspective_items.ap_stakeholder_mapping_item_id END as new_sm_id"
      ).joins("left join ap_stakeholder_mapping_items as sm ON sm.id = ap_insight_and_perspective_items.ap_stakeholder_mapping_item_id")
    else
      @scope
    end
  end
end
