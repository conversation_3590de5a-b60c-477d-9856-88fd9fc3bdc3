import _ from "lodash";

import {
  PdfTable,
  PdfTableCell,
  PdfTableHeader,
  PdfTableRow,
  PdfTableTitle,
} from "..";
import { AccountPlanAnalysisPDFProps } from "../../download-analysis";
import { formatDecimalValue } from "@/lib/utils/table-utils";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { AccountPlanTableType } from "@/features/account-plan/types";

export const HistoricRevenuePDFTable = ({
  data,
  currency,
}: {
  data?: AccountPlanAnalysisPDFProps["historicRevenueList"];
  currency?: string;
}) => {
  return (
    <PdfTable>
      <PdfTableTitle>
        {" "}
        {getAccountPlanTableName(AccountPlanTableType.HISTORIC_REVENUE)}
      </PdfTableTitle>

      <PdfTableRow>
        <PdfTableHeader>Previous</PdfTableHeader>
        <PdfTableHeader style={{ flex: 3 }}>
          Service and Product Description
        </PdfTableHeader>
        <PdfTableHeader>{currency}</PdfTableHeader>
      </PdfTableRow>

      {data?.map((row, idx) => (
        <PdfTableRow key={idx}>
          <PdfTableCell>{row.time_month} months</PdfTableCell>
          <PdfTableCell style={{ flex: 3 }}>
            {row.product_service_name}
          </PdfTableCell>
          <PdfTableCell>
            {formatDecimalValue({ value: row.value })}
          </PdfTableCell>
        </PdfTableRow>
      ))}
    </PdfTable>
  );
};
