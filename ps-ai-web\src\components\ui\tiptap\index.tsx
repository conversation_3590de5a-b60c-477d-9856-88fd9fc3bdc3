import React, { useCallback } from "react";
import Mention from "@tiptap/extension-mention";
import Placeholder from "@tiptap/extension-placeholder";
import { EditorContent, mergeAttributes, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import BulletList from "@tiptap/extension-bullet-list";
import ListItem from "@tiptap/extension-list-item";
import OrderedList from "@tiptap/extension-ordered-list";
import ListKeymap from "@tiptap/extension-list-keymap";
import { Markdown } from "tiptap-markdown";
import _ from "lodash";

import {
  mentionSuggestionOptions,
  MentionSuggestion,
} from "./mention-suggestion";
import { cn } from "@/lib/utils";

export const mentionClassName =
  "bg-slate-200 box-decoration-clone py-1 px-0.5 rounded-md text-sm";
export const mentionRegex =
  /<span[^>]*data-type="mention"(?:[^>]*data-id="([^"]*)")?[^>]*data-idx="([^"]*)".*?<\/span>/g;

const CustomMention = Mention.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      idx: {
        default: null,
        parseHTML: (element: HTMLElement) => element.getAttribute("data-idx"),
        renderHTML: (attributes: MentionSuggestion) => {
          if (!attributes.idx) {
            return {};
          }
          return {
            "data-idx": attributes.idx,
          };
        },
      },
    };
  },
});

const Tiptap = ({
  value,
  onChange,
  onBlur,
  onFocus,
  mentionSuggestion = [],
  placeholder,
  className,
  placeholderClassName,
  editable = true,
}: {
  value: string;
  onChange: (val: string) => void;
  onBlur?: (val: string) => void;
  onFocus?: () => void;
  mentionSuggestion?: MentionSuggestion[];
  placeholder?: string;
  className?: string;
  placeholderClassName?: string;
  editable?: boolean;
}) => {
  const restoreMention = useCallback(
    (markdown: string) => {
      const listRegex = /\n(?=\d)/g;
      const breakRegex = /(?<!\n)\n(?!\n)/g;
      const mentionRestoreRegex = /<@V(\d+)>/g;
      const inputRestoreRegex = /<@I-([^>]+)>/g;

      const restoredMarkdown = markdown
        ?.replace(/\\n\\n/g, (_) => {
          return "\n\n";
        })
        ?.replace(listRegex, (_) => {
          return "\n\n";
        })
        ?.replace(breakRegex, (_) => {
          return "\n";
        })
        ?.replace(mentionRestoreRegex, (_, id) => {
          const matchedSuggestion = mentionSuggestion.find((v) => v.id == id);

          return `<span 
            data-type="mention" 
            class="${mentionClassName}" 
            data-id="${matchedSuggestion?.id}"
            data-idx="${matchedSuggestion?.idx}"  
            data-label="${matchedSuggestion?.label}" 
            contenteditable="false"> 
            @${matchedSuggestion?.label} 
          </span>`;
        })
        .replace(inputRestoreRegex, (__, label) => {
          const matchedSuggestion = mentionSuggestion.find(
            (v) => _.snakeCase(v.label) == label
          );

          return `<span 
            data-type="mention" class="${mentionClassName}" 
            data-id="${matchedSuggestion?.id}"  
            data-idx="${matchedSuggestion?.idx}" 
            data-label="${matchedSuggestion?.label}" 
            contenteditable="false">
            @${matchedSuggestion?.label}
          </span>`;
        });

      return restoredMarkdown;
    },
    [mentionSuggestion]
  );

  const editor = useEditor(
    {
      editable,
      extensions: [
        StarterKit.configure({
          bulletList: false,
          orderedList: false,
          listItem: false,
        }),
        Markdown,
        BulletList.configure({
          HTMLAttributes: {
            class:
              "list-disc list-outside pl-res-x-lg mb-res-y-sm px-res [&_li]:mb-res-y-xl [&_ul]:mt-res-y-lg [&_ul]:list-[circle]",
          },
        }),
        ListItem,
        OrderedList.configure({
          HTMLAttributes: {
            class:
              "list-decimal list-outside pl-res-x-lg mb-res-y-sm px-res [&_li]:mb-res-y-xl [&_ul]:mt-res-y-lg",
          },
        }),
        ListKeymap,
        CustomMention.configure({
          HTMLAttributes: {
            class: mentionClassName,
          },
          suggestion: {
            items: ({ query }): MentionSuggestion[] =>
              mentionSuggestion.filter((item) =>
                item.label.toLowerCase().startsWith(query.toLowerCase())
              ),
            ...mentionSuggestionOptions,
          },
          renderHTML({ options, node }) {
            return [
              "span",
              mergeAttributes(options.HTMLAttributes, {
                "data-idx": node.attrs.idx,
              }),
              `${options.suggestion.char}${node.attrs.label ?? node.attrs.id}`,
            ];
          },
        }),
        Placeholder.configure({
          emptyEditorClass: cn(
            "before:content-[attr(data-placeholder)] before:float-left before:text-neutral-500 before:font-normal before:h-0 before:pointer-events-none",
            placeholderClassName
          ),
          placeholder: placeholder ?? "Write something …",
        }),
      ],
      immediatelyRender: false,
      content: restoreMention(value),
      onUpdate: ({ editor }) => {
        try {
          const markdown: string = editor.storage.markdown.getMarkdown();
          onChange(markdown);
        } catch (e) {
          console.log(e);
        }
      },
      onBlur: ({ editor }) => {
        try {
          const markdown: string = editor.storage.markdown.getMarkdown();
          onBlur?.(markdown);
        } catch (e) {
          console.log(e);
        }
      },
      onFocus,
      editorProps: {
        attributes: {
          class: cn(
            "prose focus-visible:outline-none border focus-visible:border-primary-500 px-3 py-2 rounded-md w-full max-w-none text-sm text-foreground h-[15vh] overflow-y-auto scrollbar-thin bg-white",
            "[&_p]:mb-res-x-lg [&_h1]:mb-res-x-lg [&_h2]:mb-res-x-lg [&_h1]:font-bold",
            className
          ),
        },
      },
    },
    [mentionSuggestion, placeholder]
  );

  if (!editor) {
    return null;
  }

  return <EditorContent editor={editor} contentEditable={false} />;
};

export { Tiptap };
