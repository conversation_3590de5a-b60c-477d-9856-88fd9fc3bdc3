# frozen_string_literal: true

class UtilityService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
  end

  def currency_exchange_rates(query_params)
    base_currency = query_params.delete(:currency)
    
    assert! base_currency.present?, on_error: 'Currency is not specified'

    params = {
      base_currency: base_currency,
      exchange_currency: query_params.delete(:exchange_currency)
    }.compact

    rates = CurrencyExchangeRate.where(params)

    output = {}

    rates.each do |r|
      output[r.exchange_currency] = r.rates + r.rates_decimal.to_f
    end

    output
  end
end
