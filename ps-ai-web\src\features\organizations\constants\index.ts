import { OrganizationTier } from "../types";

export const PUBLIC_ORGANIZATION_ID = 0;
export const DEFAULT_ORGANIZATION_NAME = "Perception Selling";
export const DEFAULT_ORGANIZATION_TAGLINE =
  "Lead Your Future with AI-Driven Strategies";

export const organizationTierList = [
  {
    name: "Free",
    value: OrganizationTier.FREE,
    assignable: true,
  },
  {
    name: "Premium",
    value: OrganizationTier.PREMIUM,
    assignable: true,
  },
  {
    name: "Public",
    value: OrganizationTier.PUBLIC,
    assignable: false,
  },
  {
    name: "Superuser",
    value: OrganizationTier.SUPERUSER,
    assignable: false,
  },
];

export const getOrganizationTierAlias = (tier?: OrganizationTier) => {
  return organizationTierList.find((v) => v.value === tier)?.name ?? "-";
};
