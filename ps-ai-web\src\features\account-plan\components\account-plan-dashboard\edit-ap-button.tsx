"use client";

import { <PERSON>con<PERSON>ircle<PERSON><PERSON><PERSON>, IconDatabaseCog } from "@tabler/icons-react";
import React, { useState } from "react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { PATH } from "@/constants/path";
import {
  AccountPlanGroupsPayload,
  AccountPlanStatus,
} from "@/features/account-plan/types";

import { GridDeleteButton } from "@/components/ui/grid/grid-actions";
import { useRouter } from "next/navigation";
import { StatusBadge } from "@/components/ui/badge/status-badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useAccountPlanGroups } from "@/features/account-plan/api/account-plan-group/get-account-plan-group";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useDeleteAccountPlan } from "@/features/account-plan/api/delete-account-plan";
import { useQueryClient } from "@tanstack/react-query";
import { QUERY_KEYS } from "@/constants/query-keys";
import { useUpdateAccountPlan } from "@/features/account-plan/api/update-account-plan";
import { isRequestError } from "@/lib/api-client";
import { useCreateNewAccountPlanTables } from "./create-ap-button";
import { AccountMetadataInput } from "../account-metadata-fields";
import { useUpdateAccountPlanGroups } from "../../api/account-plan-group/update-account-plan-group";

export default function EditAccountPlanVersionButton({
  accountGroupId,
}: {
  accountGroupId: number;
}) {
  const [openModal, setOpenModal] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);

  const router = useRouter();
  const queryClient = useQueryClient();

  const { onCreateNewAccountPlan, isLoading } = useCreateNewAccountPlanTables();
  const { accountPlanGroups } = useAccountPlanGroups({
    accountGroupId,
  });

  const updateAccountPlanGroup = useUpdateAccountPlanGroups({});
  const deleteAccountPlan = useDeleteAccountPlan({
    mutationConfig: {
      invalidate: true,
    },
  });
  const updateAccountPlan = useUpdateAccountPlan({
    mutationConfig: {
      invalidate: true,
    },
  });

  const onSetActive = async (accountId: number) => {
    await updateAccountPlan.mutateAsync({
      accountId,
      data: {
        status: AccountPlanStatus.ACTIVE,
      },
    });

    setIsUpdate(true);
  };

  const onUpdateGroup = async (
    data: AccountPlanGroupsPayload,
    onError?: () => void
  ) => {
    if (!accountPlanGroups?.id) return;

    try {
      await updateAccountPlanGroup.mutateAsync({
        accountGroupId: accountPlanGroups?.id,
        data,
      });

      setIsUpdate(true);
      toast("Your changes has been saved");
    } catch (e) {
      if (isRequestError(e)) {
        const errorMessage = e.response?.data.errors[0].message ?? "";
        toast.error(errorMessage);
      } else {
        toast.error("An error occured while updating the data");
      }

      onError?.();
    }
  };

  const onDeleteAccountPlan = async (accountId: number) => {
    await deleteAccountPlan.mutateAsync({
      accountId,
    });

    await queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.ACCOUNT_PLANS_GROUPS, accountGroupId],
    });

    setIsUpdate(true);
  };

  const onOpenModal = () => {
    setOpenModal(true);
  };

  const onCloseModal = () => {
    setOpenModal(false);

    if (isUpdate) {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ACCOUNT_PLANS_GROUPS],
      });
    }
  };

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="icon" size="icon" onClick={onOpenModal}>
              <IconDatabaseCog className="size-icon-res-base" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Manage Versions</TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <Dialog
        open={openModal}
        onOpenChange={(val) => {
          setOpenModal(val);
          if (isUpdate) {
            queryClient.invalidateQueries({
              queryKey: [QUERY_KEYS.ACCOUNT_PLANS_GROUPS],
            });
          }
        }}
      >
        <DialogContent autoFocus={false} forceMount>
          <DialogHeader>
            <DialogTitle>Account Plan Version Management</DialogTitle>
            <DialogDescription>
              Manage Active Versions of Account Plans
            </DialogDescription>
          </DialogHeader>

          {/* Dummy tooltip since there is an autofocus issue */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger></TooltipTrigger>
            </Tooltip>
          </TooltipProvider>

          <AccountMetadataInput
            title="Account ID"
            tooltip="The unique internal refence number for this account plan."
            defaultValue={accountPlanGroups?.account_plan_unique_id ?? ""}
            onSave={(account_plan_unique_id, { reset }) => {
              if (
                account_plan_unique_id ===
                accountPlanGroups?.account_plan_unique_id
              )
                return;

              onUpdateGroup({ account_plan_unique_id }, () => reset());
            }}
            titleProps={{
              className: "text-sm font-semibold",
            }}
          />

          <p className="mt-res-y-base text-sm font-semibold">Manage Versions</p>
          <Table>
            <TableHeader>
              <TableHead>Version Name</TableHead>
              <TableHead>Status</TableHead>
              {(accountPlanGroups?.account_plans?.length ?? 0) > 1 && (
                <TableHead className="w-[1.5vw]"></TableHead>
              )}
            </TableHeader>
            <TableBody>
              {accountPlanGroups?.account_plans
                .sort((a, b) => a.id - b.id)
                .map((v, idx) => (
                  <TableRow
                    key={idx}
                    className="cursor-pointer"
                    onClick={() =>
                      router.push(PATH.DASHBOARD_ACCOUNT_PLAN_EDIT(v.id))
                    }
                  >
                    <TableCell>{v.version}</TableCell>
                    <TableCell>
                      <StatusBadge
                        status={v.status ?? AccountPlanStatus.INACTIVE}
                      />
                    </TableCell>
                    {accountPlanGroups?.account_plans.length > 1 && (
                      <TableCell>
                        <div className="flex w-full justify-end">
                          {v.status !== AccountPlanStatus.ACTIVE && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    className="group"
                                    variant="icon"
                                    size="icon"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      onSetActive(v.id);
                                    }}
                                  >
                                    <IconCircleCheck className="text-neutral-500 group-hover:text-green-500" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>Set as active</TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}

                          {
                            <GridDeleteButton
                              isLoading={deleteAccountPlan.isPending}
                              onDelete={() => onDeleteAccountPlan(v.id)}
                              itemName="version"
                            />
                          }
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
            </TableBody>
          </Table>
          <DialogFooter className="!justify-between">
            <Button
              isLoading={isLoading}
              onClick={() => onCreateNewAccountPlan(accountGroupId)}
            >
              Add New Version
            </Button>
            <Button variant="ghost" onClick={onCloseModal}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
