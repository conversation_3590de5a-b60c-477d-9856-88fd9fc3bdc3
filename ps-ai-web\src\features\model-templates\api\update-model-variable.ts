import { useMutation } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { CreateModelVariableData } from "./create-model-variable";
import { ModelVariable } from "../types";

type UpdateModelVariableData = Partial<CreateModelVariableData> & {
  id: ModelVariable["id"];
};

export const updateModelVariable = ({
  data: { id, ...restData },
}: {
  data: UpdateModelVariableData;
}): ApiResponse<ModelVariable> => {
  return api.put(`${API_ROUTES.MODEL_TEMPLATES_VARIABLES}/${id}`, restData);
};

type UseUpdateModelVariableOptions = {
  mutationConfig?: MutationConfig<typeof updateModelVariable>;
};

export const useUpdateModelVariable = ({
  mutationConfig,
}: UseUpdateModelVariableOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: updateModelVariable,
  });
};
