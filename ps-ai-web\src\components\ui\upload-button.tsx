"use client";

import React, { useRef } from "react";
import { Button } from "@/components/ui/button";
import { useUploadData } from "@/features/upload/api/upload-with-presigned";
import { toast } from "sonner";

type FileUploadButtonProps = {
  onChangeFile?: (url: string) => void;
};

export function UploadButton({
  onChangeFile,
  children,
  ...props
}: FileUploadButtonProps & React.HTMLAttributes<HTMLButtonElement>) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const uploadData = useUploadData({});

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];

    if (!file) return;

    try {
      const {
        data: { download_url: photoUrl },
      } = await uploadData.mutateAsync({
        data: {
          file,
        },
      });

      onChangeFile?.(photoUrl);
    } catch (_) {
      toast("An error occured while uploading the file");
    }
  };

  return (
    <>
      <Button
        className="mt-res-y-base px-res-x-base"
        {...props}
        onClick={handleClick}
      >
        {children}
      </Button>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        style={{ display: "none" }}
      />
    </>
  );
}
