# frozen_string_literal: true

class AccountPlanTableItems::ApWalletShareItemService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
  end

  def create(account_plan_id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    if params[:item_type].blank?
      params[:item_type] = 'addressable_wallet_share'
    end

    ap_item = ::AccountPlanTableItems::ApWalletShareItem.new

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: 'position',
        table_type: 'wallet_share',
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item = ::AccountPlanTableItems::ApWalletShareItem.create!(params)
    end

    OpenStruct.new(
      ap_wallet_share_item: ap_item
    )
  end

  def update(account_plan_id, id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApWalletShareItem.find(id)

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: 'position',
        table_type: 'wallet_share',
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item.update!(params)
    end

    OpenStruct.new(
      ap_wallet_share_item: ap_item
    )
  end

  def index(account_plan_id, query_params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_table = ApTable.find_by(
      table_category: 'position',
      table_type: 'wallet_share',
      account_plan_id: account_plan_id
    )

    ap_item_reposity = ::AccountPlanTableItems::ApWalletShareItems.new

    filter = query_params.slice(
      :page, :per_page, :disable_pagination
    )
    filter = filter.merge(
      ap_table_id: ap_table&.id || -1
    )

    filtered_items = ap_item_reposity.filter(filter)

    OpenStruct.new(
      ap_wallet_share_items: filtered_items
    )
  end

  def show(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApWalletShareItem.find(id)
    
    OpenStruct.new(
      ap_wallet_share_item: ap_item
    ) 
  end

  def destroy(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApWalletShareItem.find(id)

    ActiveRecord::Base.transaction do
      ap_item.discard!
    end
  end
end
