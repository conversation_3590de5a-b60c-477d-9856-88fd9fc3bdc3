import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { ModelTemplate } from "../types";

export const getModelDetail = ({
  modelId,
}: {
  modelId: number;
}): ApiResponse<ModelTemplate> => {
  return api.get(`${API_ROUTES.MODEL_TEMPLATES}/${modelId}`);
};

export const getModelDetailQueryOptions = (modelId: number) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.MODEL_TEMPLATES, modelId],
    queryFn: () => getModelDetail({ modelId }),
    enabled: !!modelId,
  });
};

type UsePromptTemplateDetailOptions = {
  modelId: number;
  queryConfig?: QueryConfig<typeof getModelDetail>;
  options?: Partial<ReturnType<typeof getModelDetailQueryOptions>>;
};

export const useModelDetail = ({
  modelId,
  queryConfig,
  options,
}: UsePromptTemplateDetailOptions) => {
  const modelDetailQuery = useQuery({
    ...getModelDetailQueryOptions(modelId),
    ...queryConfig,
    ...options,
  });

  return {
    ...modelDetailQuery,
    modelDetail: modelDetailQuery.data?.data,
  };
};
