"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-day-picker";
exports.ids = ["vendor-chunks/react-day-picker"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-day-picker/dist/index.esm.js":
/*!*********************************************************!*\
  !*** ./node_modules/react-day-picker/dist/index.esm.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   Caption: () => (/* binding */ Caption),\n/* harmony export */   CaptionDropdowns: () => (/* binding */ CaptionDropdowns),\n/* harmony export */   CaptionLabel: () => (/* binding */ CaptionLabel),\n/* harmony export */   CaptionNavigation: () => (/* binding */ CaptionNavigation),\n/* harmony export */   Day: () => (/* binding */ Day),\n/* harmony export */   DayContent: () => (/* binding */ DayContent),\n/* harmony export */   DayPicker: () => (/* binding */ DayPicker),\n/* harmony export */   DayPickerContext: () => (/* binding */ DayPickerContext),\n/* harmony export */   DayPickerProvider: () => (/* binding */ DayPickerProvider),\n/* harmony export */   Dropdown: () => (/* binding */ Dropdown),\n/* harmony export */   FocusContext: () => (/* binding */ FocusContext),\n/* harmony export */   FocusProvider: () => (/* binding */ FocusProvider),\n/* harmony export */   Footer: () => (/* binding */ Footer),\n/* harmony export */   Head: () => (/* binding */ Head),\n/* harmony export */   HeadRow: () => (/* binding */ HeadRow),\n/* harmony export */   IconDropdown: () => (/* binding */ IconDropdown),\n/* harmony export */   IconLeft: () => (/* binding */ IconLeft),\n/* harmony export */   IconRight: () => (/* binding */ IconRight),\n/* harmony export */   InternalModifier: () => (/* binding */ InternalModifier),\n/* harmony export */   Months: () => (/* binding */ Months),\n/* harmony export */   NavigationContext: () => (/* binding */ NavigationContext),\n/* harmony export */   NavigationProvider: () => (/* binding */ NavigationProvider),\n/* harmony export */   RootProvider: () => (/* binding */ RootProvider),\n/* harmony export */   Row: () => (/* binding */ Row),\n/* harmony export */   SelectMultipleContext: () => (/* binding */ SelectMultipleContext),\n/* harmony export */   SelectMultipleProvider: () => (/* binding */ SelectMultipleProvider),\n/* harmony export */   SelectMultipleProviderInternal: () => (/* binding */ SelectMultipleProviderInternal),\n/* harmony export */   SelectRangeContext: () => (/* binding */ SelectRangeContext),\n/* harmony export */   SelectRangeProvider: () => (/* binding */ SelectRangeProvider),\n/* harmony export */   SelectRangeProviderInternal: () => (/* binding */ SelectRangeProviderInternal),\n/* harmony export */   SelectSingleContext: () => (/* binding */ SelectSingleContext),\n/* harmony export */   SelectSingleProvider: () => (/* binding */ SelectSingleProvider),\n/* harmony export */   SelectSingleProviderInternal: () => (/* binding */ SelectSingleProviderInternal),\n/* harmony export */   WeekNumber: () => (/* binding */ WeekNumber),\n/* harmony export */   addToRange: () => (/* binding */ addToRange),\n/* harmony export */   isDateAfterType: () => (/* binding */ isDateAfterType),\n/* harmony export */   isDateBeforeType: () => (/* binding */ isDateBeforeType),\n/* harmony export */   isDateInterval: () => (/* binding */ isDateInterval),\n/* harmony export */   isDateRange: () => (/* binding */ isDateRange),\n/* harmony export */   isDayOfWeekType: () => (/* binding */ isDayOfWeekType),\n/* harmony export */   isDayPickerDefault: () => (/* binding */ isDayPickerDefault),\n/* harmony export */   isDayPickerMultiple: () => (/* binding */ isDayPickerMultiple),\n/* harmony export */   isDayPickerRange: () => (/* binding */ isDayPickerRange),\n/* harmony export */   isDayPickerSingle: () => (/* binding */ isDayPickerSingle),\n/* harmony export */   isMatch: () => (/* binding */ isMatch),\n/* harmony export */   useActiveModifiers: () => (/* binding */ useActiveModifiers),\n/* harmony export */   useDayPicker: () => (/* binding */ useDayPicker),\n/* harmony export */   useDayRender: () => (/* binding */ useDayRender),\n/* harmony export */   useFocusContext: () => (/* binding */ useFocusContext),\n/* harmony export */   useInput: () => (/* binding */ useInput),\n/* harmony export */   useNavigation: () => (/* binding */ useNavigation),\n/* harmony export */   useSelectMultiple: () => (/* binding */ useSelectMultiple),\n/* harmony export */   useSelectRange: () => (/* binding */ useSelectRange),\n/* harmony export */   useSelectSingle: () => (/* binding */ useSelectSingle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfMonth.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/endOfMonth.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfDay.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isSameYear.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/setMonth.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/setYear.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfYear.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/differenceInCalendarMonths.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/addMonths.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isSameMonth.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isBefore.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfISOWeek.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/startOfWeek.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/addDays.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isSameDay.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isAfter.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/subDays.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/differenceInCalendarDays.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/isDate.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/addWeeks.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/addYears.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/endOfISOWeek.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/endOfWeek.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/max.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/min.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/getUnixTime.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/getISOWeek.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/getWeek.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/getWeeksInMonth.js\");\n/* harmony import */ var date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! date-fns */ \"(ssr)/./node_modules/date-fns/parse.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! date-fns/locale */ \"(ssr)/./node_modules/date-fns/locale/en-US.js\");\n\n\n\n\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nfunction __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n/** Returns true when the props are of type {@link DayPickerMultipleProps}. */\nfunction isDayPickerMultiple(props) {\n    return props.mode === 'multiple';\n}\n\n/** Returns true when the props are of type {@link DayPickerRangeProps}. */\nfunction isDayPickerRange(props) {\n    return props.mode === 'range';\n}\n\n/** Returns true when the props are of type {@link DayPickerSingleProps}. */\nfunction isDayPickerSingle(props) {\n    return props.mode === 'single';\n}\n\n/**\n * The name of the default CSS classes.\n */\nvar defaultClassNames = {\n    root: 'rdp',\n    multiple_months: 'rdp-multiple_months',\n    with_weeknumber: 'rdp-with_weeknumber',\n    vhidden: 'rdp-vhidden',\n    button_reset: 'rdp-button_reset',\n    button: 'rdp-button',\n    caption: 'rdp-caption',\n    caption_start: 'rdp-caption_start',\n    caption_end: 'rdp-caption_end',\n    caption_between: 'rdp-caption_between',\n    caption_label: 'rdp-caption_label',\n    caption_dropdowns: 'rdp-caption_dropdowns',\n    dropdown: 'rdp-dropdown',\n    dropdown_month: 'rdp-dropdown_month',\n    dropdown_year: 'rdp-dropdown_year',\n    dropdown_icon: 'rdp-dropdown_icon',\n    months: 'rdp-months',\n    month: 'rdp-month',\n    table: 'rdp-table',\n    tbody: 'rdp-tbody',\n    tfoot: 'rdp-tfoot',\n    head: 'rdp-head',\n    head_row: 'rdp-head_row',\n    head_cell: 'rdp-head_cell',\n    nav: 'rdp-nav',\n    nav_button: 'rdp-nav_button',\n    nav_button_previous: 'rdp-nav_button_previous',\n    nav_button_next: 'rdp-nav_button_next',\n    nav_icon: 'rdp-nav_icon',\n    row: 'rdp-row',\n    weeknumber: 'rdp-weeknumber',\n    cell: 'rdp-cell',\n    day: 'rdp-day',\n    day_today: 'rdp-day_today',\n    day_outside: 'rdp-day_outside',\n    day_selected: 'rdp-day_selected',\n    day_disabled: 'rdp-day_disabled',\n    day_hidden: 'rdp-day_hidden',\n    day_range_start: 'rdp-day_range_start',\n    day_range_end: 'rdp-day_range_end',\n    day_range_middle: 'rdp-day_range_middle'\n};\n\n/**\n * The default formatter for the caption.\n */\nfunction formatCaption(month, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(month, 'LLLL y', options);\n}\n\n/**\n * The default formatter for the Day button.\n */\nfunction formatDay(day, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(day, 'd', options);\n}\n\n/**\n * The default formatter for the Month caption.\n */\nfunction formatMonthCaption(month, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(month, 'LLLL', options);\n}\n\n/**\n * The default formatter for the week number.\n */\nfunction formatWeekNumber(weekNumber) {\n    return \"\".concat(weekNumber);\n}\n\n/**\n * The default formatter for the name of the weekday.\n */\nfunction formatWeekdayName(weekday, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(weekday, 'cccccc', options);\n}\n\n/**\n * The default formatter for the Year caption.\n */\nfunction formatYearCaption(year, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(year, 'yyyy', options);\n}\n\nvar formatters = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    formatCaption: formatCaption,\n    formatDay: formatDay,\n    formatMonthCaption: formatMonthCaption,\n    formatWeekNumber: formatWeekNumber,\n    formatWeekdayName: formatWeekdayName,\n    formatYearCaption: formatYearCaption\n});\n\n/**\n * The default ARIA label for the day button.\n */\nvar labelDay = function (day, activeModifiers, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(day, 'do MMMM (EEEE)', options);\n};\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nvar labelMonthDropdown = function () {\n    return 'Month: ';\n};\n\n/**\n * The default ARIA label for next month button in navigation\n */\nvar labelNext = function () {\n    return 'Go to next month';\n};\n\n/**\n * The default ARIA label for previous month button in navigation\n */\nvar labelPrevious = function () {\n    return 'Go to previous month';\n};\n\n/**\n * The default ARIA label for the Weekday element.\n */\nvar labelWeekday = function (day, options) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(day, 'cccc', options);\n};\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nvar labelWeekNumber = function (n) {\n    return \"Week n. \".concat(n);\n};\n\n/**\n * The default ARIA label for the WeekNumber element.\n */\nvar labelYearDropdown = function () {\n    return 'Year: ';\n};\n\nvar labels = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    labelDay: labelDay,\n    labelMonthDropdown: labelMonthDropdown,\n    labelNext: labelNext,\n    labelPrevious: labelPrevious,\n    labelWeekNumber: labelWeekNumber,\n    labelWeekday: labelWeekday,\n    labelYearDropdown: labelYearDropdown\n});\n\n/**\n * Returns the default values to use in the DayPickerContext, in case they are\n * not passed down with the DayPicker initial props.\n */\nfunction getDefaultContextValues() {\n    var captionLayout = 'buttons';\n    var classNames = defaultClassNames;\n    var locale = date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.enUS;\n    var modifiersClassNames = {};\n    var modifiers = {};\n    var numberOfMonths = 1;\n    var styles = {};\n    var today = new Date();\n    return {\n        captionLayout: captionLayout,\n        classNames: classNames,\n        formatters: formatters,\n        labels: labels,\n        locale: locale,\n        modifiersClassNames: modifiersClassNames,\n        modifiers: modifiers,\n        numberOfMonths: numberOfMonths,\n        styles: styles,\n        today: today,\n        mode: 'default'\n    };\n}\n\n/** Return the `fromDate` and `toDate` prop values values parsing the DayPicker props. */\nfunction parseFromToProps(props) {\n    var fromYear = props.fromYear, toYear = props.toYear, fromMonth = props.fromMonth, toMonth = props.toMonth;\n    var fromDate = props.fromDate, toDate = props.toDate;\n    if (fromMonth) {\n        fromDate = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(fromMonth);\n    }\n    else if (fromYear) {\n        fromDate = new Date(fromYear, 0, 1);\n    }\n    if (toMonth) {\n        toDate = (0,date_fns__WEBPACK_IMPORTED_MODULE_5__.endOfMonth)(toMonth);\n    }\n    else if (toYear) {\n        toDate = new Date(toYear, 11, 31);\n    }\n    return {\n        fromDate: fromDate ? (0,date_fns__WEBPACK_IMPORTED_MODULE_6__.startOfDay)(fromDate) : undefined,\n        toDate: toDate ? (0,date_fns__WEBPACK_IMPORTED_MODULE_6__.startOfDay)(toDate) : undefined\n    };\n}\n\n/**\n * The DayPicker context shares the props passed to DayPicker within internal\n * and custom components. It is used to set the default values and perform\n * one-time calculations required to render the days.\n *\n * Access to this context from the {@link useDayPicker} hook.\n */\nvar DayPickerContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/**\n * The provider for the {@link DayPickerContext}, assigning the defaults from the\n * initial DayPicker props.\n */\nfunction DayPickerProvider(props) {\n    var _a;\n    var initialProps = props.initialProps;\n    var defaultContextValues = getDefaultContextValues();\n    var _b = parseFromToProps(initialProps), fromDate = _b.fromDate, toDate = _b.toDate;\n    var captionLayout = (_a = initialProps.captionLayout) !== null && _a !== void 0 ? _a : defaultContextValues.captionLayout;\n    if (captionLayout !== 'buttons' && (!fromDate || !toDate)) {\n        // When no from/to dates are set, the caption is always buttons\n        captionLayout = 'buttons';\n    }\n    var onSelect;\n    if (isDayPickerSingle(initialProps) ||\n        isDayPickerMultiple(initialProps) ||\n        isDayPickerRange(initialProps)) {\n        onSelect = initialProps.onSelect;\n    }\n    var value = __assign(__assign(__assign({}, defaultContextValues), initialProps), { captionLayout: captionLayout, classNames: __assign(__assign({}, defaultContextValues.classNames), initialProps.classNames), components: __assign({}, initialProps.components), formatters: __assign(__assign({}, defaultContextValues.formatters), initialProps.formatters), fromDate: fromDate, labels: __assign(__assign({}, defaultContextValues.labels), initialProps.labels), mode: initialProps.mode || defaultContextValues.mode, modifiers: __assign(__assign({}, defaultContextValues.modifiers), initialProps.modifiers), modifiersClassNames: __assign(__assign({}, defaultContextValues.modifiersClassNames), initialProps.modifiersClassNames), onSelect: onSelect, styles: __assign(__assign({}, defaultContextValues.styles), initialProps.styles), toDate: toDate });\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DayPickerContext.Provider, { value: value, children: props.children }));\n}\n/**\n * Hook to access the {@link DayPickerContextValue}.\n *\n * Use the DayPicker context to access to the props passed to DayPicker inside\n * internal or custom components.\n */\nfunction useDayPicker() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(DayPickerContext);\n    if (!context) {\n        throw new Error(\"useDayPicker must be used within a DayPickerProvider.\");\n    }\n    return context;\n}\n\n/** Render the caption for the displayed month. This component is used when `captionLayout=\"buttons\"`. */\nfunction CaptionLabel(props) {\n    var _a = useDayPicker(), locale = _a.locale, classNames = _a.classNames, styles = _a.styles, formatCaption = _a.formatters.formatCaption;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: classNames.caption_label, style: styles.caption_label, \"aria-live\": \"polite\", role: \"presentation\", id: props.id, children: formatCaption(props.displayMonth, { locale: locale }) }));\n}\n\n/**\n * Render the icon in the styled drop-down.\n */\nfunction IconDropdown(props) {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", __assign({ width: \"8px\", height: \"8px\", viewBox: \"0 0 120 120\", \"data-testid\": \"iconDropdown\" }, props, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { d: \"M4.22182541,48.2218254 C8.44222828,44.0014225 15.2388494,43.9273804 19.5496459,47.9996989 L19.7781746,48.2218254 L60,88.443 L100.221825,48.2218254 C104.442228,44.0014225 111.238849,43.9273804 115.549646,47.9996989 L115.778175,48.2218254 C119.998577,52.4422283 120.07262,59.2388494 116.000301,63.5496459 L115.778175,63.7781746 L67.7781746,111.778175 C63.5577717,115.998577 56.7611506,116.07262 52.4503541,112.000301 L52.2218254,111.778175 L4.22182541,63.7781746 C-0.0739418023,59.4824074 -0.0739418023,52.5175926 4.22182541,48.2218254 Z\", fill: \"currentColor\", fillRule: \"nonzero\" }) })));\n}\n\n/**\n * Render a styled select component – displaying a caption and a custom\n * drop-down icon.\n */\nfunction Dropdown(props) {\n    var _a, _b;\n    var onChange = props.onChange, value = props.value, children = props.children, caption = props.caption, className = props.className, style = props.style;\n    var dayPicker = useDayPicker();\n    var IconDropdownComponent = (_b = (_a = dayPicker.components) === null || _a === void 0 ? void 0 : _a.IconDropdown) !== null && _b !== void 0 ? _b : IconDropdown;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: className, style: style, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: dayPicker.classNames.vhidden, children: props['aria-label'] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"select\", { name: props.name, \"aria-label\": props['aria-label'], className: dayPicker.classNames.dropdown, style: dayPicker.styles.dropdown, value: value, onChange: onChange, children: children }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: dayPicker.classNames.caption_label, style: dayPicker.styles.caption_label, \"aria-hidden\": \"true\", children: [caption, (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconDropdownComponent, { className: dayPicker.classNames.dropdown_icon, style: dayPicker.styles.dropdown_icon })] })] }));\n}\n\n/** Render the dropdown to navigate between months. */\nfunction MonthsDropdown(props) {\n    var _a;\n    var _b = useDayPicker(), fromDate = _b.fromDate, toDate = _b.toDate, styles = _b.styles, locale = _b.locale, formatMonthCaption = _b.formatters.formatMonthCaption, classNames = _b.classNames, components = _b.components, labelMonthDropdown = _b.labels.labelMonthDropdown;\n    // Dropdown should appear only when both from/toDate is set\n    if (!fromDate)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    if (!toDate)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    var dropdownMonths = [];\n    if ((0,date_fns__WEBPACK_IMPORTED_MODULE_7__.isSameYear)(fromDate, toDate)) {\n        // only display the months included in the range\n        var date = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(fromDate);\n        for (var month = fromDate.getMonth(); month <= toDate.getMonth(); month++) {\n            dropdownMonths.push((0,date_fns__WEBPACK_IMPORTED_MODULE_8__.setMonth)(date, month));\n        }\n    }\n    else {\n        // display all the 12 months\n        var date = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(new Date()); // Any date should be OK, as we just need the year\n        for (var month = 0; month <= 11; month++) {\n            dropdownMonths.push((0,date_fns__WEBPACK_IMPORTED_MODULE_8__.setMonth)(date, month));\n        }\n    }\n    var handleChange = function (e) {\n        var selectedMonth = Number(e.target.value);\n        var newMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_8__.setMonth)((0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(props.displayMonth), selectedMonth);\n        props.onChange(newMonth);\n    };\n    var DropdownComponent = (_a = components === null || components === void 0 ? void 0 : components.Dropdown) !== null && _a !== void 0 ? _a : Dropdown;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DropdownComponent, { name: \"months\", \"aria-label\": labelMonthDropdown(), className: classNames.dropdown_month, style: styles.dropdown_month, onChange: handleChange, value: props.displayMonth.getMonth(), caption: formatMonthCaption(props.displayMonth, { locale: locale }), children: dropdownMonths.map(function (m) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"option\", { value: m.getMonth(), children: formatMonthCaption(m, { locale: locale }) }, m.getMonth())); }) }));\n}\n\n/**\n * Render a dropdown to change the year. Take in account the `nav.fromDate` and\n * `toDate` from context.\n */\nfunction YearsDropdown(props) {\n    var _a;\n    var displayMonth = props.displayMonth;\n    var _b = useDayPicker(), fromDate = _b.fromDate, toDate = _b.toDate, locale = _b.locale, styles = _b.styles, classNames = _b.classNames, components = _b.components, formatYearCaption = _b.formatters.formatYearCaption, labelYearDropdown = _b.labels.labelYearDropdown;\n    var years = [];\n    // Dropdown should appear only when both from/toDate is set\n    if (!fromDate)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    if (!toDate)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    var fromYear = fromDate.getFullYear();\n    var toYear = toDate.getFullYear();\n    for (var year = fromYear; year <= toYear; year++) {\n        years.push((0,date_fns__WEBPACK_IMPORTED_MODULE_9__.setYear)((0,date_fns__WEBPACK_IMPORTED_MODULE_10__.startOfYear)(new Date()), year));\n    }\n    var handleChange = function (e) {\n        var newMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_9__.setYear)((0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(displayMonth), Number(e.target.value));\n        props.onChange(newMonth);\n    };\n    var DropdownComponent = (_a = components === null || components === void 0 ? void 0 : components.Dropdown) !== null && _a !== void 0 ? _a : Dropdown;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DropdownComponent, { name: \"years\", \"aria-label\": labelYearDropdown(), className: classNames.dropdown_year, style: styles.dropdown_year, onChange: handleChange, value: displayMonth.getFullYear(), caption: formatYearCaption(displayMonth, { locale: locale }), children: years.map(function (year) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"option\", { value: year.getFullYear(), children: formatYearCaption(year, { locale: locale }) }, year.getFullYear())); }) }));\n}\n\n/**\n * Helper hook for using controlled/uncontrolled values from a component props.\n *\n * When the value is not controlled, pass `undefined` as `controlledValue` and\n * use the returned setter to update it.\n *\n * When the value is controlled, pass the controlled value as second\n * argument, which will be always returned as `value`.\n */\nfunction useControlledValue(defaultValue, controlledValue) {\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultValue), uncontrolledValue = _a[0], setValue = _a[1];\n    var value = controlledValue === undefined ? uncontrolledValue : controlledValue;\n    return [value, setValue];\n}\n\n/** Return the initial month according to the given options. */\nfunction getInitialMonth(context) {\n    var month = context.month, defaultMonth = context.defaultMonth, today = context.today;\n    var initialMonth = month || defaultMonth || today || new Date();\n    var toDate = context.toDate, fromDate = context.fromDate, _a = context.numberOfMonths, numberOfMonths = _a === void 0 ? 1 : _a;\n    // Fix the initialMonth if is after the to-date\n    if (toDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(toDate, initialMonth) < 0) {\n        var offset = -1 * (numberOfMonths - 1);\n        initialMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(toDate, offset);\n    }\n    // Fix the initialMonth if is before the from-date\n    if (fromDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(initialMonth, fromDate) < 0) {\n        initialMonth = fromDate;\n    }\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(initialMonth);\n}\n\n/** Controls the navigation state. */\nfunction useNavigationState() {\n    var context = useDayPicker();\n    var initialMonth = getInitialMonth(context);\n    var _a = useControlledValue(initialMonth, context.month), month = _a[0], setMonth = _a[1];\n    var goToMonth = function (date) {\n        var _a;\n        if (context.disableNavigation)\n            return;\n        var month = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(date);\n        setMonth(month);\n        (_a = context.onMonthChange) === null || _a === void 0 ? void 0 : _a.call(context, month);\n    };\n    return [month, goToMonth];\n}\n\n/**\n * Return the months to display in the component according to the number of\n * months and the from/to date.\n */\nfunction getDisplayMonths(month, _a) {\n    var reverseMonths = _a.reverseMonths, numberOfMonths = _a.numberOfMonths;\n    var start = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(month);\n    var end = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)((0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(start, numberOfMonths));\n    var monthsDiff = (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(end, start);\n    var months = [];\n    for (var i = 0; i < monthsDiff; i++) {\n        var nextMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(start, i);\n        months.push(nextMonth);\n    }\n    if (reverseMonths)\n        months = months.reverse();\n    return months;\n}\n\n/**\n * Returns the next month the user can navigate to according to the given\n * options.\n *\n * Please note that the next month is not always the next calendar month:\n *\n * - if after the `toDate` range, is undefined;\n * - if the navigation is paged, is the number of months displayed ahead.\n *\n */\nfunction getNextMonth(startingMonth, options) {\n    if (options.disableNavigation) {\n        return undefined;\n    }\n    var toDate = options.toDate, pagedNavigation = options.pagedNavigation, _a = options.numberOfMonths, numberOfMonths = _a === void 0 ? 1 : _a;\n    var offset = pagedNavigation ? numberOfMonths : 1;\n    var month = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(startingMonth);\n    if (!toDate) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(month, offset);\n    }\n    var monthsDiff = (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(toDate, startingMonth);\n    if (monthsDiff < numberOfMonths) {\n        return undefined;\n    }\n    // Jump forward as the number of months when paged navigation\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(month, offset);\n}\n\n/**\n * Returns the next previous the user can navigate to, according to the given\n * options.\n *\n * Please note that the previous month is not always the previous calendar\n * month:\n *\n * - if before the `fromDate` date, is `undefined`;\n * - if the navigation is paged, is the number of months displayed before.\n *\n */\nfunction getPreviousMonth(startingMonth, options) {\n    if (options.disableNavigation) {\n        return undefined;\n    }\n    var fromDate = options.fromDate, pagedNavigation = options.pagedNavigation, _a = options.numberOfMonths, numberOfMonths = _a === void 0 ? 1 : _a;\n    var offset = pagedNavigation ? numberOfMonths : 1;\n    var month = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(startingMonth);\n    if (!fromDate) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(month, -offset);\n    }\n    var monthsDiff = (0,date_fns__WEBPACK_IMPORTED_MODULE_11__.differenceInCalendarMonths)(month, fromDate);\n    if (monthsDiff <= 0) {\n        return undefined;\n    }\n    // Jump back as the number of months when paged navigation\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(month, -offset);\n}\n\n/**\n * The Navigation context shares details and methods to navigate the months in DayPicker.\n * Access this context from the {@link useNavigation} hook.\n */\nvar NavigationContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provides the values for the {@link NavigationContext}. */\nfunction NavigationProvider(props) {\n    var dayPicker = useDayPicker();\n    var _a = useNavigationState(), currentMonth = _a[0], goToMonth = _a[1];\n    var displayMonths = getDisplayMonths(currentMonth, dayPicker);\n    var nextMonth = getNextMonth(currentMonth, dayPicker);\n    var previousMonth = getPreviousMonth(currentMonth, dayPicker);\n    var isDateDisplayed = function (date) {\n        return displayMonths.some(function (displayMonth) {\n            return (0,date_fns__WEBPACK_IMPORTED_MODULE_13__.isSameMonth)(date, displayMonth);\n        });\n    };\n    var goToDate = function (date, refDate) {\n        if (isDateDisplayed(date)) {\n            return;\n        }\n        if (refDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_14__.isBefore)(date, refDate)) {\n            goToMonth((0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(date, 1 + dayPicker.numberOfMonths * -1));\n        }\n        else {\n            goToMonth(date);\n        }\n    };\n    var value = {\n        currentMonth: currentMonth,\n        displayMonths: displayMonths,\n        goToMonth: goToMonth,\n        goToDate: goToDate,\n        previousMonth: previousMonth,\n        nextMonth: nextMonth,\n        isDateDisplayed: isDateDisplayed\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(NavigationContext.Provider, { value: value, children: props.children }));\n}\n/**\n * Hook to access the {@link NavigationContextValue}. Use this hook to navigate\n * between months or years in DayPicker.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useNavigation() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NavigationContext);\n    if (!context) {\n        throw new Error('useNavigation must be used within a NavigationProvider');\n    }\n    return context;\n}\n\n/**\n * Render a caption with the dropdowns to navigate between months and years.\n */\nfunction CaptionDropdowns(props) {\n    var _a;\n    var _b = useDayPicker(), classNames = _b.classNames, styles = _b.styles, components = _b.components;\n    var goToMonth = useNavigation().goToMonth;\n    var handleMonthChange = function (newMonth) {\n        goToMonth((0,date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths)(newMonth, props.displayIndex ? -props.displayIndex : 0));\n    };\n    var CaptionLabelComponent = (_a = components === null || components === void 0 ? void 0 : components.CaptionLabel) !== null && _a !== void 0 ? _a : CaptionLabel;\n    var captionLabel = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionLabelComponent, { id: props.id, displayMonth: props.displayMonth }));\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: classNames.caption_dropdowns, style: styles.caption_dropdowns, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: classNames.vhidden, children: captionLabel }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MonthsDropdown, { onChange: handleMonthChange, displayMonth: props.displayMonth }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(YearsDropdown, { onChange: handleMonthChange, displayMonth: props.displayMonth })] }));\n}\n\n/**\n * Render the \"previous month\" button in the navigation.\n */\nfunction IconLeft(props) {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", __assign({ width: \"16px\", height: \"16px\", viewBox: \"0 0 120 120\" }, props, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { d: \"M69.490332,3.34314575 C72.6145263,0.218951416 77.6798462,0.218951416 80.8040405,3.34314575 C83.8617626,6.40086786 83.9268205,11.3179931 80.9992143,14.4548388 L80.8040405,14.6568542 L35.461,60 L80.8040405,105.343146 C83.8617626,108.400868 83.9268205,113.317993 80.9992143,116.454839 L80.8040405,116.656854 C77.7463184,119.714576 72.8291931,119.779634 69.6923475,116.852028 L69.490332,116.656854 L18.490332,65.6568542 C15.4326099,62.5991321 15.367552,57.6820069 18.2951583,54.5451612 L18.490332,54.3431458 L69.490332,3.34314575 Z\", fill: \"currentColor\", fillRule: \"nonzero\" }) })));\n}\n\n/**\n * Render the \"next month\" button in the navigation.\n */\nfunction IconRight(props) {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", __assign({ width: \"16px\", height: \"16px\", viewBox: \"0 0 120 120\" }, props, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { d: \"M49.8040405,3.34314575 C46.6798462,0.218951416 41.6145263,0.218951416 38.490332,3.34314575 C35.4326099,6.40086786 35.367552,11.3179931 38.2951583,14.4548388 L38.490332,14.6568542 L83.8333725,60 L38.490332,105.343146 C35.4326099,108.400868 35.367552,113.317993 38.2951583,116.454839 L38.490332,116.656854 C41.5480541,119.714576 46.4651794,119.779634 49.602025,116.852028 L49.8040405,116.656854 L100.804041,65.6568542 C103.861763,62.5991321 103.926821,57.6820069 100.999214,54.5451612 L100.804041,54.3431458 L49.8040405,3.34314575 Z\", fill: \"currentColor\" }) })));\n}\n\n/** Render a button HTML element applying the reset class name. */\nvar Button = (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function (props, ref) {\n    var _a = useDayPicker(), classNames = _a.classNames, styles = _a.styles;\n    var classNamesArr = [classNames.button_reset, classNames.button];\n    if (props.className) {\n        classNamesArr.push(props.className);\n    }\n    var className = classNamesArr.join(' ');\n    var style = __assign(__assign({}, styles.button_reset), styles.button);\n    if (props.style) {\n        Object.assign(style, props.style);\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"button\", __assign({}, props, { ref: ref, type: \"button\", className: className, style: style })));\n});\n\n/** A component rendering the navigation buttons or the drop-downs. */\nfunction Navigation(props) {\n    var _a, _b;\n    var _c = useDayPicker(), dir = _c.dir, locale = _c.locale, classNames = _c.classNames, styles = _c.styles, _d = _c.labels, labelPrevious = _d.labelPrevious, labelNext = _d.labelNext, components = _c.components;\n    if (!props.nextMonth && !props.previousMonth) {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    }\n    var previousLabel = labelPrevious(props.previousMonth, { locale: locale });\n    var previousClassName = [\n        classNames.nav_button,\n        classNames.nav_button_previous\n    ].join(' ');\n    var nextLabel = labelNext(props.nextMonth, { locale: locale });\n    var nextClassName = [\n        classNames.nav_button,\n        classNames.nav_button_next\n    ].join(' ');\n    var IconRightComponent = (_a = components === null || components === void 0 ? void 0 : components.IconRight) !== null && _a !== void 0 ? _a : IconRight;\n    var IconLeftComponent = (_b = components === null || components === void 0 ? void 0 : components.IconLeft) !== null && _b !== void 0 ? _b : IconLeft;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: classNames.nav, style: styles.nav, children: [!props.hidePrevious && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Button, { name: \"previous-month\", \"aria-label\": previousLabel, className: previousClassName, style: styles.nav_button_previous, disabled: !props.previousMonth, onClick: props.onPreviousClick, children: dir === 'rtl' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconRightComponent, { className: classNames.nav_icon, style: styles.nav_icon })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconLeftComponent, { className: classNames.nav_icon, style: styles.nav_icon })) })), !props.hideNext && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Button, { name: \"next-month\", \"aria-label\": nextLabel, className: nextClassName, style: styles.nav_button_next, disabled: !props.nextMonth, onClick: props.onNextClick, children: dir === 'rtl' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconLeftComponent, { className: classNames.nav_icon, style: styles.nav_icon })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconRightComponent, { className: classNames.nav_icon, style: styles.nav_icon })) }))] }));\n}\n\n/**\n * Render a caption with a button-based navigation.\n */\nfunction CaptionNavigation(props) {\n    var numberOfMonths = useDayPicker().numberOfMonths;\n    var _a = useNavigation(), previousMonth = _a.previousMonth, nextMonth = _a.nextMonth, goToMonth = _a.goToMonth, displayMonths = _a.displayMonths;\n    var displayIndex = displayMonths.findIndex(function (month) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_13__.isSameMonth)(props.displayMonth, month);\n    });\n    var isFirst = displayIndex === 0;\n    var isLast = displayIndex === displayMonths.length - 1;\n    var hideNext = numberOfMonths > 1 && (isFirst || !isLast);\n    var hidePrevious = numberOfMonths > 1 && (isLast || !isFirst);\n    var handlePreviousClick = function () {\n        if (!previousMonth)\n            return;\n        goToMonth(previousMonth);\n    };\n    var handleNextClick = function () {\n        if (!nextMonth)\n            return;\n        goToMonth(nextMonth);\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Navigation, { displayMonth: props.displayMonth, hideNext: hideNext, hidePrevious: hidePrevious, nextMonth: nextMonth, previousMonth: previousMonth, onPreviousClick: handlePreviousClick, onNextClick: handleNextClick }));\n}\n\n/**\n * Render the caption of a month. The caption has a different layout when\n * setting the {@link DayPickerBase.captionLayout} prop.\n */\nfunction Caption(props) {\n    var _a;\n    var _b = useDayPicker(), classNames = _b.classNames, disableNavigation = _b.disableNavigation, styles = _b.styles, captionLayout = _b.captionLayout, components = _b.components;\n    var CaptionLabelComponent = (_a = components === null || components === void 0 ? void 0 : components.CaptionLabel) !== null && _a !== void 0 ? _a : CaptionLabel;\n    var caption;\n    if (disableNavigation) {\n        caption = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionLabelComponent, { id: props.id, displayMonth: props.displayMonth }));\n    }\n    else if (captionLayout === 'dropdown') {\n        caption = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionDropdowns, { displayMonth: props.displayMonth, id: props.id }));\n    }\n    else if (captionLayout === 'dropdown-buttons') {\n        caption = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionDropdowns, { displayMonth: props.displayMonth, displayIndex: props.displayIndex, id: props.id }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionNavigation, { displayMonth: props.displayMonth, displayIndex: props.displayIndex, id: props.id })] }));\n    }\n    else {\n        caption = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionLabelComponent, { id: props.id, displayMonth: props.displayMonth, displayIndex: props.displayIndex }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionNavigation, { displayMonth: props.displayMonth, id: props.id })] }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: classNames.caption, style: styles.caption, children: caption }));\n}\n\n/** Render the Footer component (empty as default).*/\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction Footer(props) {\n    var _a = useDayPicker(), footer = _a.footer, styles = _a.styles, tfoot = _a.classNames.tfoot;\n    if (!footer)\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {});\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"tfoot\", { className: tfoot, style: styles.tfoot, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"tr\", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { colSpan: 8, children: footer }) }) }));\n}\n\n/**\n * Generate a series of 7 days, starting from the week, to use for formatting\n * the weekday names (Monday, Tuesday, etc.).\n */\nfunction getWeekdays(locale, \n/** The index of the first day of the week (0 - Sunday). */\nweekStartsOn, \n/** Use ISOWeek instead of locale/ */\nISOWeek) {\n    var start = ISOWeek\n        ? (0,date_fns__WEBPACK_IMPORTED_MODULE_15__.startOfISOWeek)(new Date())\n        : (0,date_fns__WEBPACK_IMPORTED_MODULE_16__.startOfWeek)(new Date(), { locale: locale, weekStartsOn: weekStartsOn });\n    var days = [];\n    for (var i = 0; i < 7; i++) {\n        var day = (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(start, i);\n        days.push(day);\n    }\n    return days;\n}\n\n/**\n * Render the HeadRow component - i.e. the table head row with the weekday names.\n */\nfunction HeadRow() {\n    var _a = useDayPicker(), classNames = _a.classNames, styles = _a.styles, showWeekNumber = _a.showWeekNumber, locale = _a.locale, weekStartsOn = _a.weekStartsOn, ISOWeek = _a.ISOWeek, formatWeekdayName = _a.formatters.formatWeekdayName, labelWeekday = _a.labels.labelWeekday;\n    var weekdays = getWeekdays(locale, weekStartsOn, ISOWeek);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"tr\", { style: styles.head_row, className: classNames.head_row, children: [showWeekNumber && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { style: styles.head_cell, className: classNames.head_cell })), weekdays.map(function (weekday, i) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"th\", { scope: \"col\", className: classNames.head_cell, style: styles.head_cell, \"aria-label\": labelWeekday(weekday, { locale: locale }), children: formatWeekdayName(weekday, { locale: locale }) }, i)); })] }));\n}\n\n/** Render the table head. */\nfunction Head() {\n    var _a;\n    var _b = useDayPicker(), classNames = _b.classNames, styles = _b.styles, components = _b.components;\n    var HeadRowComponent = (_a = components === null || components === void 0 ? void 0 : components.HeadRow) !== null && _a !== void 0 ? _a : HeadRow;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"thead\", { style: styles.head, className: classNames.head, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(HeadRowComponent, {}) }));\n}\n\n/** Render the content of the day cell. */\nfunction DayContent(props) {\n    var _a = useDayPicker(), locale = _a.locale, formatDay = _a.formatters.formatDay;\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: formatDay(props.date, { locale: locale }) });\n}\n\n/**\n * The SelectMultiple context shares details about the selected days when in\n * multiple selection mode.\n *\n * Access this context from the {@link useSelectMultiple} hook.\n */\nvar SelectMultipleContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provides the values for the {@link SelectMultipleContext}. */\nfunction SelectMultipleProvider(props) {\n    if (!isDayPickerMultiple(props.initialProps)) {\n        var emptyContextValue = {\n            selected: undefined,\n            modifiers: {\n                disabled: []\n            }\n        };\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectMultipleContext.Provider, { value: emptyContextValue, children: props.children }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectMultipleProviderInternal, { initialProps: props.initialProps, children: props.children }));\n}\nfunction SelectMultipleProviderInternal(_a) {\n    var initialProps = _a.initialProps, children = _a.children;\n    var selected = initialProps.selected, min = initialProps.min, max = initialProps.max;\n    var onDayClick = function (day, activeModifiers, e) {\n        var _a, _b;\n        (_a = initialProps.onDayClick) === null || _a === void 0 ? void 0 : _a.call(initialProps, day, activeModifiers, e);\n        var isMinSelected = Boolean(activeModifiers.selected && min && (selected === null || selected === void 0 ? void 0 : selected.length) === min);\n        if (isMinSelected) {\n            return;\n        }\n        var isMaxSelected = Boolean(!activeModifiers.selected && max && (selected === null || selected === void 0 ? void 0 : selected.length) === max);\n        if (isMaxSelected) {\n            return;\n        }\n        var selectedDays = selected ? __spreadArray([], selected, true) : [];\n        if (activeModifiers.selected) {\n            var index = selectedDays.findIndex(function (selectedDay) {\n                return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(day, selectedDay);\n            });\n            selectedDays.splice(index, 1);\n        }\n        else {\n            selectedDays.push(day);\n        }\n        (_b = initialProps.onSelect) === null || _b === void 0 ? void 0 : _b.call(initialProps, selectedDays, day, activeModifiers, e);\n    };\n    var modifiers = {\n        disabled: []\n    };\n    if (selected) {\n        modifiers.disabled.push(function (day) {\n            var isMaxSelected = max && selected.length > max - 1;\n            var isSelected = selected.some(function (selectedDay) {\n                return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(selectedDay, day);\n            });\n            return Boolean(isMaxSelected && !isSelected);\n        });\n    }\n    var contextValue = {\n        selected: selected,\n        onDayClick: onDayClick,\n        modifiers: modifiers\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectMultipleContext.Provider, { value: contextValue, children: children }));\n}\n/**\n * Hook to access the {@link SelectMultipleContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useSelectMultiple() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SelectMultipleContext);\n    if (!context) {\n        throw new Error('useSelectMultiple must be used within a SelectMultipleProvider');\n    }\n    return context;\n}\n\n/**\n * Add a day to an existing range.\n *\n * The returned range takes in account the `undefined` values and if the added\n * day is already present in the range.\n */\nfunction addToRange(day, range) {\n    var _a = range || {}, from = _a.from, to = _a.to;\n    if (from && to) {\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(to, day) && (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(from, day)) {\n            return undefined;\n        }\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(to, day)) {\n            return { from: to, to: undefined };\n        }\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(from, day)) {\n            return undefined;\n        }\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_19__.isAfter)(from, day)) {\n            return { from: day, to: to };\n        }\n        return { from: from, to: day };\n    }\n    if (to) {\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_19__.isAfter)(day, to)) {\n            return { from: to, to: day };\n        }\n        return { from: day, to: to };\n    }\n    if (from) {\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_14__.isBefore)(day, from)) {\n            return { from: day, to: from };\n        }\n        return { from: from, to: day };\n    }\n    return { from: day, to: undefined };\n}\n\n/**\n * The SelectRange context shares details about the selected days when in\n * range selection mode.\n *\n * Access this context from the {@link useSelectRange} hook.\n */\nvar SelectRangeContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provides the values for the {@link SelectRangeProvider}. */\nfunction SelectRangeProvider(props) {\n    if (!isDayPickerRange(props.initialProps)) {\n        var emptyContextValue = {\n            selected: undefined,\n            modifiers: {\n                range_start: [],\n                range_end: [],\n                range_middle: [],\n                disabled: []\n            }\n        };\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectRangeContext.Provider, { value: emptyContextValue, children: props.children }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectRangeProviderInternal, { initialProps: props.initialProps, children: props.children }));\n}\nfunction SelectRangeProviderInternal(_a) {\n    var initialProps = _a.initialProps, children = _a.children;\n    var selected = initialProps.selected;\n    var _b = selected || {}, selectedFrom = _b.from, selectedTo = _b.to;\n    var min = initialProps.min;\n    var max = initialProps.max;\n    var onDayClick = function (day, activeModifiers, e) {\n        var _a, _b;\n        (_a = initialProps.onDayClick) === null || _a === void 0 ? void 0 : _a.call(initialProps, day, activeModifiers, e);\n        var newRange = addToRange(day, selected);\n        (_b = initialProps.onSelect) === null || _b === void 0 ? void 0 : _b.call(initialProps, newRange, day, activeModifiers, e);\n    };\n    var modifiers = {\n        range_start: [],\n        range_end: [],\n        range_middle: [],\n        disabled: []\n    };\n    if (selectedFrom) {\n        modifiers.range_start = [selectedFrom];\n        if (!selectedTo) {\n            modifiers.range_end = [selectedFrom];\n        }\n        else {\n            modifiers.range_end = [selectedTo];\n            if (!(0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(selectedFrom, selectedTo)) {\n                modifiers.range_middle = [\n                    {\n                        after: selectedFrom,\n                        before: selectedTo\n                    }\n                ];\n            }\n        }\n    }\n    else if (selectedTo) {\n        modifiers.range_start = [selectedTo];\n        modifiers.range_end = [selectedTo];\n    }\n    if (min) {\n        if (selectedFrom && !selectedTo) {\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_20__.subDays)(selectedFrom, min - 1),\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedFrom, min - 1)\n            });\n        }\n        if (selectedFrom && selectedTo) {\n            modifiers.disabled.push({\n                after: selectedFrom,\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedFrom, min - 1)\n            });\n        }\n        if (!selectedFrom && selectedTo) {\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_20__.subDays)(selectedTo, min - 1),\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedTo, min - 1)\n            });\n        }\n    }\n    if (max) {\n        if (selectedFrom && !selectedTo) {\n            modifiers.disabled.push({\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedFrom, -max + 1)\n            });\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedFrom, max - 1)\n            });\n        }\n        if (selectedFrom && selectedTo) {\n            var selectedCount = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(selectedTo, selectedFrom) + 1;\n            var offset = max - selectedCount;\n            modifiers.disabled.push({\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_20__.subDays)(selectedFrom, offset)\n            });\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedTo, offset)\n            });\n        }\n        if (!selectedFrom && selectedTo) {\n            modifiers.disabled.push({\n                before: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedTo, -max + 1)\n            });\n            modifiers.disabled.push({\n                after: (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(selectedTo, max - 1)\n            });\n        }\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectRangeContext.Provider, { value: { selected: selected, onDayClick: onDayClick, modifiers: modifiers }, children: children }));\n}\n/**\n * Hook to access the {@link SelectRangeContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useSelectRange() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SelectRangeContext);\n    if (!context) {\n        throw new Error('useSelectRange must be used within a SelectRangeProvider');\n    }\n    return context;\n}\n\n/** Normalize to array a matcher input. */\nfunction matcherToArray(matcher) {\n    if (Array.isArray(matcher)) {\n        return __spreadArray([], matcher, true);\n    }\n    else if (matcher !== undefined) {\n        return [matcher];\n    }\n    else {\n        return [];\n    }\n}\n\n/** Create CustomModifiers from dayModifiers */\nfunction getCustomModifiers(dayModifiers) {\n    var customModifiers = {};\n    Object.entries(dayModifiers).forEach(function (_a) {\n        var modifier = _a[0], matcher = _a[1];\n        customModifiers[modifier] = matcherToArray(matcher);\n    });\n    return customModifiers;\n}\n\n/** The name of the modifiers that are used internally by DayPicker. */\nvar InternalModifier;\n(function (InternalModifier) {\n    InternalModifier[\"Outside\"] = \"outside\";\n    /** Name of the modifier applied to the disabled days, using the `disabled` prop. */\n    InternalModifier[\"Disabled\"] = \"disabled\";\n    /** Name of the modifier applied to the selected days using the `selected` prop). */\n    InternalModifier[\"Selected\"] = \"selected\";\n    /** Name of the modifier applied to the hidden days using the `hidden` prop). */\n    InternalModifier[\"Hidden\"] = \"hidden\";\n    /** Name of the modifier applied to the day specified using the `today` prop). */\n    InternalModifier[\"Today\"] = \"today\";\n    /** The modifier applied to the day starting a selected range, when in range selection mode.  */\n    InternalModifier[\"RangeStart\"] = \"range_start\";\n    /** The modifier applied to the day ending a selected range, when in range selection mode.  */\n    InternalModifier[\"RangeEnd\"] = \"range_end\";\n    /** The modifier applied to the days between the start and the end of a selected range, when in range selection mode.  */\n    InternalModifier[\"RangeMiddle\"] = \"range_middle\";\n})(InternalModifier || (InternalModifier = {}));\n\nvar Selected = InternalModifier.Selected, Disabled = InternalModifier.Disabled, Hidden = InternalModifier.Hidden, Today = InternalModifier.Today, RangeEnd = InternalModifier.RangeEnd, RangeMiddle = InternalModifier.RangeMiddle, RangeStart = InternalModifier.RangeStart, Outside = InternalModifier.Outside;\n/** Return the {@link InternalModifiers} from the DayPicker and select contexts. */\nfunction getInternalModifiers(dayPicker, selectMultiple, selectRange) {\n    var _a;\n    var internalModifiers = (_a = {},\n        _a[Selected] = matcherToArray(dayPicker.selected),\n        _a[Disabled] = matcherToArray(dayPicker.disabled),\n        _a[Hidden] = matcherToArray(dayPicker.hidden),\n        _a[Today] = [dayPicker.today],\n        _a[RangeEnd] = [],\n        _a[RangeMiddle] = [],\n        _a[RangeStart] = [],\n        _a[Outside] = [],\n        _a);\n    if (dayPicker.fromDate) {\n        internalModifiers[Disabled].push({ before: dayPicker.fromDate });\n    }\n    if (dayPicker.toDate) {\n        internalModifiers[Disabled].push({ after: dayPicker.toDate });\n    }\n    if (isDayPickerMultiple(dayPicker)) {\n        internalModifiers[Disabled] = internalModifiers[Disabled].concat(selectMultiple.modifiers[Disabled]);\n    }\n    else if (isDayPickerRange(dayPicker)) {\n        internalModifiers[Disabled] = internalModifiers[Disabled].concat(selectRange.modifiers[Disabled]);\n        internalModifiers[RangeStart] = selectRange.modifiers[RangeStart];\n        internalModifiers[RangeMiddle] = selectRange.modifiers[RangeMiddle];\n        internalModifiers[RangeEnd] = selectRange.modifiers[RangeEnd];\n    }\n    return internalModifiers;\n}\n\n/** The Modifiers context store the modifiers used in DayPicker. To access the value of this context, use {@link useModifiers}. */\nvar ModifiersContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provide the value for the {@link ModifiersContext}. */\nfunction ModifiersProvider(props) {\n    var dayPicker = useDayPicker();\n    var selectMultiple = useSelectMultiple();\n    var selectRange = useSelectRange();\n    var internalModifiers = getInternalModifiers(dayPicker, selectMultiple, selectRange);\n    var customModifiers = getCustomModifiers(dayPicker.modifiers);\n    var modifiers = __assign(__assign({}, internalModifiers), customModifiers);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ModifiersContext.Provider, { value: modifiers, children: props.children }));\n}\n/**\n * Return the modifiers used by DayPicker.\n *\n * This hook is meant to be used inside internal or custom components.\n * Requires to be wrapped into {@link ModifiersProvider}.\n *\n */\nfunction useModifiers() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ModifiersContext);\n    if (!context) {\n        throw new Error('useModifiers must be used within a ModifiersProvider');\n    }\n    return context;\n}\n\n/** Returns true if `matcher` is of type {@link DateInterval}. */\nfunction isDateInterval(matcher) {\n    return Boolean(matcher &&\n        typeof matcher === 'object' &&\n        'before' in matcher &&\n        'after' in matcher);\n}\n/** Returns true if `value` is a {@link DateRange} type. */\nfunction isDateRange(value) {\n    return Boolean(value && typeof value === 'object' && 'from' in value);\n}\n/** Returns true if `value` is of type {@link DateAfter}. */\nfunction isDateAfterType(value) {\n    return Boolean(value && typeof value === 'object' && 'after' in value);\n}\n/** Returns true if `value` is of type {@link DateBefore}. */\nfunction isDateBeforeType(value) {\n    return Boolean(value && typeof value === 'object' && 'before' in value);\n}\n/** Returns true if `value` is a {@link DayOfWeek} type. */\nfunction isDayOfWeekType(value) {\n    return Boolean(value && typeof value === 'object' && 'dayOfWeek' in value);\n}\n\n/** Return `true` whether `date` is inside `range`. */\nfunction isDateInRange(date, range) {\n    var _a;\n    var from = range.from, to = range.to;\n    if (from && to) {\n        var isRangeInverted = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(to, from) < 0;\n        if (isRangeInverted) {\n            _a = [to, from], from = _a[0], to = _a[1];\n        }\n        var isInRange = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(date, from) >= 0 &&\n            (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(to, date) >= 0;\n        return isInRange;\n    }\n    if (to) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(to, date);\n    }\n    if (from) {\n        return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(from, date);\n    }\n    return false;\n}\n\n/** Returns true if `value` is a Date type. */\nfunction isDateType(value) {\n    return (0,date_fns__WEBPACK_IMPORTED_MODULE_22__.isDate)(value);\n}\n/** Returns true if `value` is an array of valid dates. */\nfunction isArrayOfDates(value) {\n    return Array.isArray(value) && value.every(date_fns__WEBPACK_IMPORTED_MODULE_22__.isDate);\n}\n/**\n * Returns whether a day matches against at least one of the given Matchers.\n *\n * ```\n * const day = new Date(2022, 5, 19);\n * const matcher1: DateRange = {\n *    from: new Date(2021, 12, 21),\n *    to: new Date(2021, 12, 30)\n * }\n * const matcher2: DateRange = {\n *    from: new Date(2022, 5, 1),\n *    to: new Date(2022, 5, 23)\n * }\n *\n * const isMatch(day, [matcher1, matcher2]); // true, since day is in the matcher1 range.\n * ```\n * */\nfunction isMatch(day, matchers) {\n    return matchers.some(function (matcher) {\n        if (typeof matcher === 'boolean') {\n            return matcher;\n        }\n        if (isDateType(matcher)) {\n            return (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(day, matcher);\n        }\n        if (isArrayOfDates(matcher)) {\n            return matcher.includes(day);\n        }\n        if (isDateRange(matcher)) {\n            return isDateInRange(day, matcher);\n        }\n        if (isDayOfWeekType(matcher)) {\n            return matcher.dayOfWeek.includes(day.getDay());\n        }\n        if (isDateInterval(matcher)) {\n            var diffBefore = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(matcher.before, day);\n            var diffAfter = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(matcher.after, day);\n            var isDayBefore = diffBefore > 0;\n            var isDayAfter = diffAfter < 0;\n            var isClosedInterval = (0,date_fns__WEBPACK_IMPORTED_MODULE_19__.isAfter)(matcher.before, matcher.after);\n            if (isClosedInterval) {\n                return isDayAfter && isDayBefore;\n            }\n            else {\n                return isDayBefore || isDayAfter;\n            }\n        }\n        if (isDateAfterType(matcher)) {\n            return (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(day, matcher.after) > 0;\n        }\n        if (isDateBeforeType(matcher)) {\n            return (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(matcher.before, day) > 0;\n        }\n        if (typeof matcher === 'function') {\n            return matcher(day);\n        }\n        return false;\n    });\n}\n\n/** Return the active modifiers for the given day. */\nfunction getActiveModifiers(day, \n/** The modifiers to match for the given date. */\nmodifiers, \n/** The month where the day is displayed, to add the \"outside\" modifiers.  */\ndisplayMonth) {\n    var matchedModifiers = Object.keys(modifiers).reduce(function (result, key) {\n        var modifier = modifiers[key];\n        if (isMatch(day, modifier)) {\n            result.push(key);\n        }\n        return result;\n    }, []);\n    var activeModifiers = {};\n    matchedModifiers.forEach(function (modifier) { return (activeModifiers[modifier] = true); });\n    if (displayMonth && !(0,date_fns__WEBPACK_IMPORTED_MODULE_13__.isSameMonth)(day, displayMonth)) {\n        activeModifiers.outside = true;\n    }\n    return activeModifiers;\n}\n\n/**\n * Returns the day that should be the target of the focus when DayPicker is\n * rendered the first time.\n *\n * TODO: this function doesn't consider if the day is outside the month. We\n * implemented this check in `useDayRender` but it should probably go here. See\n * https://github.com/gpbl/react-day-picker/pull/1576\n */\nfunction getInitialFocusTarget(displayMonths, modifiers) {\n    var firstDayInMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(displayMonths[0]);\n    var lastDayInMonth = (0,date_fns__WEBPACK_IMPORTED_MODULE_5__.endOfMonth)(displayMonths[displayMonths.length - 1]);\n    // TODO: cleanup code\n    var firstFocusableDay;\n    var today;\n    var date = firstDayInMonth;\n    while (date <= lastDayInMonth) {\n        var activeModifiers = getActiveModifiers(date, modifiers);\n        var isFocusable = !activeModifiers.disabled && !activeModifiers.hidden;\n        if (!isFocusable) {\n            date = (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(date, 1);\n            continue;\n        }\n        if (activeModifiers.selected) {\n            return date;\n        }\n        if (activeModifiers.today && !today) {\n            today = date;\n        }\n        if (!firstFocusableDay) {\n            firstFocusableDay = date;\n        }\n        date = (0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(date, 1);\n    }\n    if (today) {\n        return today;\n    }\n    else {\n        return firstFocusableDay;\n    }\n}\n\nvar MAX_RETRY = 365;\n/** Return the next date to be focused. */\nfunction getNextFocus(focusedDay, options) {\n    var moveBy = options.moveBy, direction = options.direction, context = options.context, modifiers = options.modifiers, _a = options.retry, retry = _a === void 0 ? { count: 0, lastFocused: focusedDay } : _a;\n    var weekStartsOn = context.weekStartsOn, fromDate = context.fromDate, toDate = context.toDate, locale = context.locale;\n    var moveFns = {\n        day: date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays,\n        week: date_fns__WEBPACK_IMPORTED_MODULE_23__.addWeeks,\n        month: date_fns__WEBPACK_IMPORTED_MODULE_12__.addMonths,\n        year: date_fns__WEBPACK_IMPORTED_MODULE_24__.addYears,\n        startOfWeek: function (date) {\n            return context.ISOWeek\n                ? (0,date_fns__WEBPACK_IMPORTED_MODULE_15__.startOfISOWeek)(date)\n                : (0,date_fns__WEBPACK_IMPORTED_MODULE_16__.startOfWeek)(date, { locale: locale, weekStartsOn: weekStartsOn });\n        },\n        endOfWeek: function (date) {\n            return context.ISOWeek\n                ? (0,date_fns__WEBPACK_IMPORTED_MODULE_25__.endOfISOWeek)(date)\n                : (0,date_fns__WEBPACK_IMPORTED_MODULE_26__.endOfWeek)(date, { locale: locale, weekStartsOn: weekStartsOn });\n        }\n    };\n    var newFocusedDay = moveFns[moveBy](focusedDay, direction === 'after' ? 1 : -1);\n    if (direction === 'before' && fromDate) {\n        newFocusedDay = (0,date_fns__WEBPACK_IMPORTED_MODULE_27__.max)([fromDate, newFocusedDay]);\n    }\n    else if (direction === 'after' && toDate) {\n        newFocusedDay = (0,date_fns__WEBPACK_IMPORTED_MODULE_28__.min)([toDate, newFocusedDay]);\n    }\n    var isFocusable = true;\n    if (modifiers) {\n        var activeModifiers = getActiveModifiers(newFocusedDay, modifiers);\n        isFocusable = !activeModifiers.disabled && !activeModifiers.hidden;\n    }\n    if (isFocusable) {\n        return newFocusedDay;\n    }\n    else {\n        if (retry.count > MAX_RETRY) {\n            return retry.lastFocused;\n        }\n        return getNextFocus(newFocusedDay, {\n            moveBy: moveBy,\n            direction: direction,\n            context: context,\n            modifiers: modifiers,\n            retry: __assign(__assign({}, retry), { count: retry.count + 1 })\n        });\n    }\n}\n\n/**\n * The Focus context shares details about the focused day for the keyboard\n *\n * Access this context from the {@link useFocusContext} hook.\n */\nvar FocusContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** The provider for the {@link FocusContext}. */\nfunction FocusProvider(props) {\n    var navigation = useNavigation();\n    var modifiers = useModifiers();\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(), focusedDay = _a[0], setFocusedDay = _a[1];\n    var _b = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(), lastFocused = _b[0], setLastFocused = _b[1];\n    var initialFocusTarget = getInitialFocusTarget(navigation.displayMonths, modifiers);\n    // TODO: cleanup and test obscure code below\n    var focusTarget = (focusedDay !== null && focusedDay !== void 0 ? focusedDay : (lastFocused && navigation.isDateDisplayed(lastFocused)))\n        ? lastFocused\n        : initialFocusTarget;\n    var blur = function () {\n        setLastFocused(focusedDay);\n        setFocusedDay(undefined);\n    };\n    var focus = function (date) {\n        setFocusedDay(date);\n    };\n    var context = useDayPicker();\n    var moveFocus = function (moveBy, direction) {\n        if (!focusedDay)\n            return;\n        var nextFocused = getNextFocus(focusedDay, {\n            moveBy: moveBy,\n            direction: direction,\n            context: context,\n            modifiers: modifiers\n        });\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(focusedDay, nextFocused))\n            return undefined;\n        navigation.goToDate(nextFocused, focusedDay);\n        focus(nextFocused);\n    };\n    var value = {\n        focusedDay: focusedDay,\n        focusTarget: focusTarget,\n        blur: blur,\n        focus: focus,\n        focusDayAfter: function () { return moveFocus('day', 'after'); },\n        focusDayBefore: function () { return moveFocus('day', 'before'); },\n        focusWeekAfter: function () { return moveFocus('week', 'after'); },\n        focusWeekBefore: function () { return moveFocus('week', 'before'); },\n        focusMonthBefore: function () { return moveFocus('month', 'before'); },\n        focusMonthAfter: function () { return moveFocus('month', 'after'); },\n        focusYearBefore: function () { return moveFocus('year', 'before'); },\n        focusYearAfter: function () { return moveFocus('year', 'after'); },\n        focusStartOfWeek: function () { return moveFocus('startOfWeek', 'before'); },\n        focusEndOfWeek: function () { return moveFocus('endOfWeek', 'after'); }\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(FocusContext.Provider, { value: value, children: props.children }));\n}\n/**\n * Hook to access the {@link FocusContextValue}. Use this hook to handle the\n * focus state of the elements.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useFocusContext() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(FocusContext);\n    if (!context) {\n        throw new Error('useFocusContext must be used within a FocusProvider');\n    }\n    return context;\n}\n\n/**\n * Return the active modifiers for the specified day.\n *\n * This hook is meant to be used inside internal or custom components.\n *\n * @param day\n * @param displayMonth\n */\nfunction useActiveModifiers(day, \n/**\n * The month where the date is displayed. If not the same as `date`, the day\n * is an \"outside day\".\n */\ndisplayMonth) {\n    var modifiers = useModifiers();\n    var activeModifiers = getActiveModifiers(day, modifiers, displayMonth);\n    return activeModifiers;\n}\n\n/**\n * The SelectSingle context shares details about the selected days when in\n * single selection mode.\n *\n * Access this context from the {@link useSelectSingle} hook.\n */\nvar SelectSingleContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n/** Provides the values for the {@link SelectSingleProvider}. */\nfunction SelectSingleProvider(props) {\n    if (!isDayPickerSingle(props.initialProps)) {\n        var emptyContextValue = {\n            selected: undefined\n        };\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectSingleContext.Provider, { value: emptyContextValue, children: props.children }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectSingleProviderInternal, { initialProps: props.initialProps, children: props.children }));\n}\nfunction SelectSingleProviderInternal(_a) {\n    var initialProps = _a.initialProps, children = _a.children;\n    var onDayClick = function (day, activeModifiers, e) {\n        var _a, _b, _c;\n        (_a = initialProps.onDayClick) === null || _a === void 0 ? void 0 : _a.call(initialProps, day, activeModifiers, e);\n        if (activeModifiers.selected && !initialProps.required) {\n            (_b = initialProps.onSelect) === null || _b === void 0 ? void 0 : _b.call(initialProps, undefined, day, activeModifiers, e);\n            return;\n        }\n        (_c = initialProps.onSelect) === null || _c === void 0 ? void 0 : _c.call(initialProps, day, day, activeModifiers, e);\n    };\n    var contextValue = {\n        selected: initialProps.selected,\n        onDayClick: onDayClick\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectSingleContext.Provider, { value: contextValue, children: children }));\n}\n/**\n * Hook to access the {@link SelectSingleContextValue}.\n *\n * This hook is meant to be used inside internal or custom components.\n */\nfunction useSelectSingle() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SelectSingleContext);\n    if (!context) {\n        throw new Error('useSelectSingle must be used within a SelectSingleProvider');\n    }\n    return context;\n}\n\n/**\n * This hook returns details about the content to render in the day cell.\n *\n *\n * When a day cell is rendered in the table, DayPicker can either:\n *\n * - render nothing: when the day is outside the month or has matched the\n *   \"hidden\" modifier.\n * - render a button when `onDayClick` or a selection mode is set.\n * - render a non-interactive element: when no selection mode is set, the day\n *   cell shouldn’t respond to any interaction. DayPicker should render a `div`\n *   or a `span`.\n *\n * ### Usage\n *\n * Use this hook to customize the behavior of the {@link Day} component. Create a\n * new `Day` component using this hook and pass it to the `components` prop.\n * The source of {@link Day} can be a good starting point.\n *\n */\nfunction useDayEventHandlers(date, activeModifiers) {\n    var dayPicker = useDayPicker();\n    var single = useSelectSingle();\n    var multiple = useSelectMultiple();\n    var range = useSelectRange();\n    var _a = useFocusContext(), focusDayAfter = _a.focusDayAfter, focusDayBefore = _a.focusDayBefore, focusWeekAfter = _a.focusWeekAfter, focusWeekBefore = _a.focusWeekBefore, blur = _a.blur, focus = _a.focus, focusMonthBefore = _a.focusMonthBefore, focusMonthAfter = _a.focusMonthAfter, focusYearBefore = _a.focusYearBefore, focusYearAfter = _a.focusYearAfter, focusStartOfWeek = _a.focusStartOfWeek, focusEndOfWeek = _a.focusEndOfWeek;\n    var onClick = function (e) {\n        var _a, _b, _c, _d;\n        if (isDayPickerSingle(dayPicker)) {\n            (_a = single.onDayClick) === null || _a === void 0 ? void 0 : _a.call(single, date, activeModifiers, e);\n        }\n        else if (isDayPickerMultiple(dayPicker)) {\n            (_b = multiple.onDayClick) === null || _b === void 0 ? void 0 : _b.call(multiple, date, activeModifiers, e);\n        }\n        else if (isDayPickerRange(dayPicker)) {\n            (_c = range.onDayClick) === null || _c === void 0 ? void 0 : _c.call(range, date, activeModifiers, e);\n        }\n        else {\n            (_d = dayPicker.onDayClick) === null || _d === void 0 ? void 0 : _d.call(dayPicker, date, activeModifiers, e);\n        }\n    };\n    var onFocus = function (e) {\n        var _a;\n        focus(date);\n        (_a = dayPicker.onDayFocus) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onBlur = function (e) {\n        var _a;\n        blur();\n        (_a = dayPicker.onDayBlur) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onMouseEnter = function (e) {\n        var _a;\n        (_a = dayPicker.onDayMouseEnter) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onMouseLeave = function (e) {\n        var _a;\n        (_a = dayPicker.onDayMouseLeave) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onPointerEnter = function (e) {\n        var _a;\n        (_a = dayPicker.onDayPointerEnter) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onPointerLeave = function (e) {\n        var _a;\n        (_a = dayPicker.onDayPointerLeave) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchCancel = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchCancel) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchEnd = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchEnd) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchMove = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchMove) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onTouchStart = function (e) {\n        var _a;\n        (_a = dayPicker.onDayTouchStart) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onKeyUp = function (e) {\n        var _a;\n        (_a = dayPicker.onDayKeyUp) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var onKeyDown = function (e) {\n        var _a;\n        switch (e.key) {\n            case 'ArrowLeft':\n                e.preventDefault();\n                e.stopPropagation();\n                dayPicker.dir === 'rtl' ? focusDayAfter() : focusDayBefore();\n                break;\n            case 'ArrowRight':\n                e.preventDefault();\n                e.stopPropagation();\n                dayPicker.dir === 'rtl' ? focusDayBefore() : focusDayAfter();\n                break;\n            case 'ArrowDown':\n                e.preventDefault();\n                e.stopPropagation();\n                focusWeekAfter();\n                break;\n            case 'ArrowUp':\n                e.preventDefault();\n                e.stopPropagation();\n                focusWeekBefore();\n                break;\n            case 'PageUp':\n                e.preventDefault();\n                e.stopPropagation();\n                e.shiftKey ? focusYearBefore() : focusMonthBefore();\n                break;\n            case 'PageDown':\n                e.preventDefault();\n                e.stopPropagation();\n                e.shiftKey ? focusYearAfter() : focusMonthAfter();\n                break;\n            case 'Home':\n                e.preventDefault();\n                e.stopPropagation();\n                focusStartOfWeek();\n                break;\n            case 'End':\n                e.preventDefault();\n                e.stopPropagation();\n                focusEndOfWeek();\n                break;\n        }\n        (_a = dayPicker.onDayKeyDown) === null || _a === void 0 ? void 0 : _a.call(dayPicker, date, activeModifiers, e);\n    };\n    var eventHandlers = {\n        onClick: onClick,\n        onFocus: onFocus,\n        onBlur: onBlur,\n        onKeyDown: onKeyDown,\n        onKeyUp: onKeyUp,\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onPointerEnter: onPointerEnter,\n        onPointerLeave: onPointerLeave,\n        onTouchCancel: onTouchCancel,\n        onTouchEnd: onTouchEnd,\n        onTouchMove: onTouchMove,\n        onTouchStart: onTouchStart\n    };\n    return eventHandlers;\n}\n\n/**\n * Return the current selected days when DayPicker is in selection mode. Days\n * selected by the custom selection mode are not returned.\n *\n * This hook is meant to be used inside internal or custom components.\n *\n */\nfunction useSelectedDays() {\n    var dayPicker = useDayPicker();\n    var single = useSelectSingle();\n    var multiple = useSelectMultiple();\n    var range = useSelectRange();\n    var selectedDays = isDayPickerSingle(dayPicker)\n        ? single.selected\n        : isDayPickerMultiple(dayPicker)\n            ? multiple.selected\n            : isDayPickerRange(dayPicker)\n                ? range.selected\n                : undefined;\n    return selectedDays;\n}\n\nfunction isInternalModifier(modifier) {\n    return Object.values(InternalModifier).includes(modifier);\n}\n/**\n * Return the class names for the Day element, according to the given active\n * modifiers.\n *\n * Custom class names are set via `modifiersClassNames` or `classNames`,\n * where the first have the precedence.\n */\nfunction getDayClassNames(dayPicker, activeModifiers) {\n    var classNames = [dayPicker.classNames.day];\n    Object.keys(activeModifiers).forEach(function (modifier) {\n        var customClassName = dayPicker.modifiersClassNames[modifier];\n        if (customClassName) {\n            classNames.push(customClassName);\n        }\n        else if (isInternalModifier(modifier)) {\n            var internalClassName = dayPicker.classNames[\"day_\".concat(modifier)];\n            if (internalClassName) {\n                classNames.push(internalClassName);\n            }\n        }\n    });\n    return classNames;\n}\n\n/** Return the style for the Day element, according to the given active modifiers. */\nfunction getDayStyle(dayPicker, activeModifiers) {\n    var style = __assign({}, dayPicker.styles.day);\n    Object.keys(activeModifiers).forEach(function (modifier) {\n        var _a;\n        style = __assign(__assign({}, style), (_a = dayPicker.modifiersStyles) === null || _a === void 0 ? void 0 : _a[modifier]);\n    });\n    return style;\n}\n\n/**\n * Return props and data used to render the {@link Day} component.\n *\n * Use this hook when creating a component to replace the built-in `Day`\n * component.\n */\nfunction useDayRender(\n/** The date to render. */\nday, \n/** The month where the date is displayed (if not the same as `date`, it means it is an \"outside\" day). */\ndisplayMonth, \n/** A ref to the button element that will be target of focus when rendered (if required). */\nbuttonRef) {\n    var _a;\n    var _b, _c;\n    var dayPicker = useDayPicker();\n    var focusContext = useFocusContext();\n    var activeModifiers = useActiveModifiers(day, displayMonth);\n    var eventHandlers = useDayEventHandlers(day, activeModifiers);\n    var selectedDays = useSelectedDays();\n    var isButton = Boolean(dayPicker.onDayClick || dayPicker.mode !== 'default');\n    // Focus the button if the day is focused according to the focus context\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        var _a;\n        if (activeModifiers.outside)\n            return;\n        if (!focusContext.focusedDay)\n            return;\n        if (!isButton)\n            return;\n        if ((0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(focusContext.focusedDay, day)) {\n            (_a = buttonRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n        }\n    }, [\n        focusContext.focusedDay,\n        day,\n        buttonRef,\n        isButton,\n        activeModifiers.outside\n    ]);\n    var className = getDayClassNames(dayPicker, activeModifiers).join(' ');\n    var style = getDayStyle(dayPicker, activeModifiers);\n    var isHidden = Boolean((activeModifiers.outside && !dayPicker.showOutsideDays) ||\n        activeModifiers.hidden);\n    var DayContentComponent = (_c = (_b = dayPicker.components) === null || _b === void 0 ? void 0 : _b.DayContent) !== null && _c !== void 0 ? _c : DayContent;\n    var children = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DayContentComponent, { date: day, displayMonth: displayMonth, activeModifiers: activeModifiers }));\n    var divProps = {\n        style: style,\n        className: className,\n        children: children,\n        role: 'gridcell'\n    };\n    var isFocusTarget = focusContext.focusTarget &&\n        (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(focusContext.focusTarget, day) &&\n        !activeModifiers.outside;\n    var isFocused = focusContext.focusedDay && (0,date_fns__WEBPACK_IMPORTED_MODULE_18__.isSameDay)(focusContext.focusedDay, day);\n    var buttonProps = __assign(__assign(__assign({}, divProps), (_a = { disabled: activeModifiers.disabled, role: 'gridcell' }, _a['aria-selected'] = activeModifiers.selected, _a.tabIndex = isFocused || isFocusTarget ? 0 : -1, _a)), eventHandlers);\n    var dayRender = {\n        isButton: isButton,\n        isHidden: isHidden,\n        activeModifiers: activeModifiers,\n        selectedDays: selectedDays,\n        buttonProps: buttonProps,\n        divProps: divProps\n    };\n    return dayRender;\n}\n\n/**\n * The content of a day cell – as a button or span element according to its\n * modifiers.\n */\nfunction Day(props) {\n    var buttonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var dayRender = useDayRender(props.date, props.displayMonth, buttonRef);\n    if (dayRender.isHidden) {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { role: \"gridcell\" });\n    }\n    if (!dayRender.isButton) {\n        return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", __assign({}, dayRender.divProps));\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Button, __assign({ name: \"day\", ref: buttonRef }, dayRender.buttonProps));\n}\n\n/**\n * Render the week number element. If `onWeekNumberClick` is passed to DayPicker, it\n * renders a button, otherwise a span element.\n */\nfunction WeekNumber(props) {\n    var weekNumber = props.number, dates = props.dates;\n    var _a = useDayPicker(), onWeekNumberClick = _a.onWeekNumberClick, styles = _a.styles, classNames = _a.classNames, locale = _a.locale, labelWeekNumber = _a.labels.labelWeekNumber, formatWeekNumber = _a.formatters.formatWeekNumber;\n    var content = formatWeekNumber(Number(weekNumber), { locale: locale });\n    if (!onWeekNumberClick) {\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: classNames.weeknumber, style: styles.weeknumber, children: content }));\n    }\n    var label = labelWeekNumber(Number(weekNumber), { locale: locale });\n    var handleClick = function (e) {\n        onWeekNumberClick(weekNumber, dates, e);\n    };\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Button, { name: \"week-number\", \"aria-label\": label, className: classNames.weeknumber, style: styles.weeknumber, onClick: handleClick, children: content }));\n}\n\n/** Render a row in the calendar, with the days and the week number. */\nfunction Row(props) {\n    var _a, _b;\n    var _c = useDayPicker(), styles = _c.styles, classNames = _c.classNames, showWeekNumber = _c.showWeekNumber, components = _c.components;\n    var DayComponent = (_a = components === null || components === void 0 ? void 0 : components.Day) !== null && _a !== void 0 ? _a : Day;\n    var WeeknumberComponent = (_b = components === null || components === void 0 ? void 0 : components.WeekNumber) !== null && _b !== void 0 ? _b : WeekNumber;\n    var weekNumberCell;\n    if (showWeekNumber) {\n        weekNumberCell = ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { className: classNames.cell, style: styles.cell, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(WeeknumberComponent, { number: props.weekNumber, dates: props.dates }) }));\n    }\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"tr\", { className: classNames.row, style: styles.row, children: [weekNumberCell, props.dates.map(function (date) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"td\", { className: classNames.cell, style: styles.cell, role: \"presentation\", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DayComponent, { displayMonth: props.displayMonth, date: date }) }, (0,date_fns__WEBPACK_IMPORTED_MODULE_29__.getUnixTime)(date))); })] }));\n}\n\n/** Return the weeks between two dates.  */\nfunction daysToMonthWeeks(fromDate, toDate, options) {\n    var toWeek = (options === null || options === void 0 ? void 0 : options.ISOWeek)\n        ? (0,date_fns__WEBPACK_IMPORTED_MODULE_25__.endOfISOWeek)(toDate)\n        : (0,date_fns__WEBPACK_IMPORTED_MODULE_26__.endOfWeek)(toDate, options);\n    var fromWeek = (options === null || options === void 0 ? void 0 : options.ISOWeek)\n        ? (0,date_fns__WEBPACK_IMPORTED_MODULE_15__.startOfISOWeek)(fromDate)\n        : (0,date_fns__WEBPACK_IMPORTED_MODULE_16__.startOfWeek)(fromDate, options);\n    var nOfDays = (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(toWeek, fromWeek);\n    var days = [];\n    for (var i = 0; i <= nOfDays; i++) {\n        days.push((0,date_fns__WEBPACK_IMPORTED_MODULE_17__.addDays)(fromWeek, i));\n    }\n    var weeksInMonth = days.reduce(function (result, date) {\n        var weekNumber = (options === null || options === void 0 ? void 0 : options.ISOWeek)\n            ? (0,date_fns__WEBPACK_IMPORTED_MODULE_30__.getISOWeek)(date)\n            : (0,date_fns__WEBPACK_IMPORTED_MODULE_31__.getWeek)(date, options);\n        var existingWeek = result.find(function (value) { return value.weekNumber === weekNumber; });\n        if (existingWeek) {\n            existingWeek.dates.push(date);\n            return result;\n        }\n        result.push({\n            weekNumber: weekNumber,\n            dates: [date]\n        });\n        return result;\n    }, []);\n    return weeksInMonth;\n}\n\n/**\n * Return the weeks belonging to the given month, adding the \"outside days\" to\n * the first and last week.\n */\nfunction getMonthWeeks(month, options) {\n    var weeksInMonth = daysToMonthWeeks((0,date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfMonth)(month), (0,date_fns__WEBPACK_IMPORTED_MODULE_5__.endOfMonth)(month), options);\n    if (options === null || options === void 0 ? void 0 : options.useFixedWeeks) {\n        // Add extra weeks to the month, up to 6 weeks\n        var nrOfMonthWeeks = (0,date_fns__WEBPACK_IMPORTED_MODULE_32__.getWeeksInMonth)(month, options);\n        if (nrOfMonthWeeks < 6) {\n            var lastWeek = weeksInMonth[weeksInMonth.length - 1];\n            var lastDate = lastWeek.dates[lastWeek.dates.length - 1];\n            var toDate = (0,date_fns__WEBPACK_IMPORTED_MODULE_23__.addWeeks)(lastDate, 6 - nrOfMonthWeeks);\n            var extraWeeks = daysToMonthWeeks((0,date_fns__WEBPACK_IMPORTED_MODULE_23__.addWeeks)(lastDate, 1), toDate, options);\n            weeksInMonth.push.apply(weeksInMonth, extraWeeks);\n        }\n    }\n    return weeksInMonth;\n}\n\n/** Render the table with the calendar. */\nfunction Table(props) {\n    var _a, _b, _c;\n    var _d = useDayPicker(), locale = _d.locale, classNames = _d.classNames, styles = _d.styles, hideHead = _d.hideHead, fixedWeeks = _d.fixedWeeks, components = _d.components, weekStartsOn = _d.weekStartsOn, firstWeekContainsDate = _d.firstWeekContainsDate, ISOWeek = _d.ISOWeek;\n    var weeks = getMonthWeeks(props.displayMonth, {\n        useFixedWeeks: Boolean(fixedWeeks),\n        ISOWeek: ISOWeek,\n        locale: locale,\n        weekStartsOn: weekStartsOn,\n        firstWeekContainsDate: firstWeekContainsDate\n    });\n    var HeadComponent = (_a = components === null || components === void 0 ? void 0 : components.Head) !== null && _a !== void 0 ? _a : Head;\n    var RowComponent = (_b = components === null || components === void 0 ? void 0 : components.Row) !== null && _b !== void 0 ? _b : Row;\n    var FooterComponent = (_c = components === null || components === void 0 ? void 0 : components.Footer) !== null && _c !== void 0 ? _c : Footer;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"table\", { id: props.id, className: classNames.table, style: styles.table, role: \"grid\", \"aria-labelledby\": props['aria-labelledby'], children: [!hideHead && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(HeadComponent, {}), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"tbody\", { className: classNames.tbody, style: styles.tbody, children: weeks.map(function (week) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(RowComponent, { displayMonth: props.displayMonth, dates: week.dates, weekNumber: week.weekNumber }, week.weekNumber)); }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(FooterComponent, { displayMonth: props.displayMonth })] }));\n}\n\n/*\nThe MIT License (MIT)\n\nCopyright (c) 2018-present, React Training LLC\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n/* eslint-disable prefer-const */\n/* eslint-disable @typescript-eslint/ban-ts-comment */\n/*\n * Welcome to @reach/auto-id!\n * Let's see if we can make sense of why this hook exists and its\n * implementation.\n *\n * Some background:\n *   1. Accessibility APIs rely heavily on element IDs\n *   2. Requiring developers to put IDs on every element in Reach UI is both\n *      cumbersome and error-prone\n *   3. With a component model, we can generate IDs for them!\n *\n * Solution 1: Generate random IDs.\n *\n * This works great as long as you don't server render your app. When React (in\n * the client) tries to reuse the markup from the server, the IDs won't match\n * and React will then recreate the entire DOM tree.\n *\n * Solution 2: Increment an integer\n *\n * This sounds great. Since we're rendering the exact same tree on the server\n * and client, we can increment a counter and get a deterministic result between\n * client and server. Also, JS integers can go up to nine-quadrillion. I'm\n * pretty sure the tab will be closed before an app never needs\n * 10 quadrillion IDs!\n *\n * Problem solved, right?\n *\n * Ah, but there's a catch! React's concurrent rendering makes this approach\n * non-deterministic. While the client and server will end up with the same\n * elements in the end, depending on suspense boundaries (and possibly some user\n * input during the initial render) the incrementing integers won't always match\n * up.\n *\n * Solution 3: Don't use IDs at all on the server; patch after first render.\n *\n * What we've done here is solution 2 with some tricks. With this approach, the\n * ID returned is an empty string on the first render. This way the server and\n * client have the same markup no matter how wild the concurrent rendering may\n * have gotten.\n *\n * After the render, we patch up the components with an incremented ID. This\n * causes a double render on any components with `useId`. Shouldn't be a problem\n * since the components using this hook should be small, and we're only updating\n * the ID attribute on the DOM, nothing big is happening.\n *\n * It doesn't have to be an incremented number, though--we could do generate\n * random strings instead, but incrementing a number is probably the cheapest\n * thing we can do.\n *\n * Additionally, we only do this patchup on the very first client render ever.\n * Any calls to `useId` that happen dynamically in the client will be\n * populated immediately with a value. So, we only get the double render after\n * server hydration and never again, SO BACK OFF ALRIGHT?\n */\nfunction canUseDOM() {\n    return !!(typeof window !== 'undefined' &&\n        window.document &&\n        window.document.createElement);\n}\n/**\n * React currently throws a warning when using useLayoutEffect on the server. To\n * get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect in the browser. We occasionally need useLayoutEffect to\n * ensure we don't get a render flash for certain operations, but we may also\n * need affected components to render on the server. One example is when setting\n * a component's descendants to retrieve their index values.\n *\n * Important to note that using this hook as an escape hatch will break the\n * eslint dependency warnings unless you rename the import to `useLayoutEffect`.\n * Use sparingly only when the effect won't effect the rendered HTML to avoid\n * any server/client mismatch.\n *\n * If a useLayoutEffect is needed and the result would create a mismatch, it's\n * likely that the component in question shouldn't be rendered on the server at\n * all, so a better approach would be to lazily render those in a parent\n * component after client-side hydration.\n *\n * https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n * https://github.com/reduxjs/react-redux/blob/master/src/utils/useIsomorphicLayoutEffect.js\n *\n * @param effect\n * @param deps\n */\nvar useIsomorphicLayoutEffect = canUseDOM() ? react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\nvar serverHandoffComplete = false;\nvar id = 0;\nfunction genId() {\n    return \"react-day-picker-\".concat(++id);\n}\nfunction useId(providedId) {\n    // TODO: Remove error flag when updating internal deps to React 18. None of\n    // our tricks will play well with concurrent rendering anyway.\n    var _a;\n    // If this instance isn't part of the initial render, we don't have to do the\n    // double render/patch-up dance. We can just generate the ID and return it.\n    var initialId = providedId !== null && providedId !== void 0 ? providedId : (serverHandoffComplete ? genId() : null);\n    var _b = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialId), id = _b[0], setId = _b[1];\n    useIsomorphicLayoutEffect(function () {\n        if (id === null) {\n            // Patch the ID after render. We do this in `useLayoutEffect` to avoid any\n            // rendering flicker, though it'll make the first render slower (unlikely\n            // to matter, but you're welcome to measure your app and let us know if\n            // it's a problem).\n            setId(genId());\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        if (serverHandoffComplete === false) {\n            // Flag all future uses of `useId` to skip the update dance. This is in\n            // `useEffect` because it goes after `useLayoutEffect`, ensuring we don't\n            // accidentally bail out of the patch-up dance prematurely.\n            serverHandoffComplete = true;\n        }\n    }, []);\n    return (_a = providedId !== null && providedId !== void 0 ? providedId : id) !== null && _a !== void 0 ? _a : undefined;\n}\n\n/** Render a month. */\nfunction Month(props) {\n    var _a;\n    var _b;\n    var dayPicker = useDayPicker();\n    var dir = dayPicker.dir, classNames = dayPicker.classNames, styles = dayPicker.styles, components = dayPicker.components;\n    var displayMonths = useNavigation().displayMonths;\n    var captionId = useId(dayPicker.id ? \"\".concat(dayPicker.id, \"-\").concat(props.displayIndex) : undefined);\n    var tableId = dayPicker.id\n        ? \"\".concat(dayPicker.id, \"-grid-\").concat(props.displayIndex)\n        : undefined;\n    var className = [classNames.month];\n    var style = styles.month;\n    var isStart = props.displayIndex === 0;\n    var isEnd = props.displayIndex === displayMonths.length - 1;\n    var isCenter = !isStart && !isEnd;\n    if (dir === 'rtl') {\n        _a = [isStart, isEnd], isEnd = _a[0], isStart = _a[1];\n    }\n    if (isStart) {\n        className.push(classNames.caption_start);\n        style = __assign(__assign({}, style), styles.caption_start);\n    }\n    if (isEnd) {\n        className.push(classNames.caption_end);\n        style = __assign(__assign({}, style), styles.caption_end);\n    }\n    if (isCenter) {\n        className.push(classNames.caption_between);\n        style = __assign(__assign({}, style), styles.caption_between);\n    }\n    var CaptionComponent = (_b = components === null || components === void 0 ? void 0 : components.Caption) !== null && _b !== void 0 ? _b : Caption;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: className.join(' '), style: style, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CaptionComponent, { id: captionId, displayMonth: props.displayMonth, displayIndex: props.displayIndex }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Table, { id: tableId, \"aria-labelledby\": captionId, displayMonth: props.displayMonth })] }, props.displayIndex));\n}\n\n/**\n * Render the wrapper for the month grids.\n */\nfunction Months(props) {\n    var _a = useDayPicker(), classNames = _a.classNames, styles = _a.styles;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: classNames.months, style: styles.months, children: props.children }));\n}\n\n/** Render the container with the months according to the number of months to display. */\nfunction Root(_a) {\n    var _b, _c;\n    var initialProps = _a.initialProps;\n    var dayPicker = useDayPicker();\n    var focusContext = useFocusContext();\n    var navigation = useNavigation();\n    var _d = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), hasInitialFocus = _d[0], setHasInitialFocus = _d[1];\n    // Focus the focus target when initialFocus is passed in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        if (!dayPicker.initialFocus)\n            return;\n        if (!focusContext.focusTarget)\n            return;\n        if (hasInitialFocus)\n            return;\n        focusContext.focus(focusContext.focusTarget);\n        setHasInitialFocus(true);\n    }, [\n        dayPicker.initialFocus,\n        hasInitialFocus,\n        focusContext.focus,\n        focusContext.focusTarget,\n        focusContext\n    ]);\n    // Apply classnames according to props\n    var classNames = [dayPicker.classNames.root, dayPicker.className];\n    if (dayPicker.numberOfMonths > 1) {\n        classNames.push(dayPicker.classNames.multiple_months);\n    }\n    if (dayPicker.showWeekNumber) {\n        classNames.push(dayPicker.classNames.with_weeknumber);\n    }\n    var style = __assign(__assign({}, dayPicker.styles.root), dayPicker.style);\n    var dataAttributes = Object.keys(initialProps)\n        .filter(function (key) { return key.startsWith('data-'); })\n        .reduce(function (attrs, key) {\n        var _a;\n        return __assign(__assign({}, attrs), (_a = {}, _a[key] = initialProps[key], _a));\n    }, {});\n    var MonthsComponent = (_c = (_b = initialProps.components) === null || _b === void 0 ? void 0 : _b.Months) !== null && _c !== void 0 ? _c : Months;\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", __assign({ className: classNames.join(' '), style: style, dir: dayPicker.dir, id: dayPicker.id, nonce: initialProps.nonce, title: initialProps.title, lang: initialProps.lang }, dataAttributes, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MonthsComponent, { children: navigation.displayMonths.map(function (month, i) { return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Month, { displayIndex: i, displayMonth: month }, i)); }) }) })));\n}\n\n/** Provide the value for all the context providers. */\nfunction RootProvider(props) {\n    var children = props.children, initialProps = __rest(props, [\"children\"]);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DayPickerProvider, { initialProps: initialProps, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(NavigationProvider, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectSingleProvider, { initialProps: initialProps, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectMultipleProvider, { initialProps: initialProps, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SelectRangeProvider, { initialProps: initialProps, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ModifiersProvider, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(FocusProvider, { children: children }) }) }) }) }) }) }));\n}\n\n/**\n * DayPicker render a date picker component to let users pick dates from a\n * calendar. See http://react-day-picker.js.org for updated documentation and\n * examples.\n *\n * ### Customization\n *\n * DayPicker offers different customization props. For example,\n *\n * - show multiple months using `numberOfMonths`\n * - display a dropdown to navigate the months via `captionLayout`\n * - display the week numbers with `showWeekNumbers`\n * - disable or hide days with `disabled` or `hidden`\n *\n * ### Controlling the months\n *\n * Change the initially displayed month using the `defaultMonth` prop. The\n * displayed months are controlled by DayPicker and stored in its internal\n * state. To control the months yourself, use `month` instead of `defaultMonth`\n * and use the `onMonthChange` event to set it.\n *\n * To limit the months the user can navigate to, use\n * `fromDate`/`fromMonth`/`fromYear` or `toDate`/`toMonth`/`toYear`.\n *\n * ### Selection modes\n *\n * DayPicker supports different selection mode that can be toggled using the\n * `mode` prop:\n *\n * - `mode=\"single\"`: only one day can be selected. Use `required` to make the\n *   selection required. Use the `onSelect` event handler to get the selected\n *   days.\n * - `mode=\"multiple\"`: users can select one or more days. Limit the amount of\n *   days that can be selected with the `min` or the `max` props.\n * - `mode=\"range\"`: users can select a range of days. Limit the amount of days\n *   in the range with the `min` or the `max` props.\n * - `mode=\"default\"` (default): the built-in selections are disabled. Implement\n *   your own selection mode with `onDayClick`.\n *\n * The selection modes should cover the most common use cases. In case you\n * need a more refined way of selecting days, use `mode=\"default\"`. Use the\n * `selected` props and add the day event handlers to add/remove days from the\n * selection.\n *\n * ### Modifiers\n *\n * A _modifier_ represents different styles or states for the days displayed in\n * the calendar (like \"selected\" or \"disabled\"). Define custom modifiers using\n * the `modifiers` prop.\n *\n * ### Formatters and custom component\n *\n * You can customize how the content is displayed in the date picker by using\n * either the formatters or replacing the internal components.\n *\n * For the most common cases you want to use the `formatters` prop to change how\n * the content is formatted in the calendar. Use the `components` prop to\n * replace the internal components, like the navigation icons.\n *\n * ### Styling\n *\n * DayPicker comes with a default, basic style in `react-day-picker/style` – use\n * it as template for your own style.\n *\n * If you are using CSS modules, pass the imported styles object the\n * `classNames` props.\n *\n * You can also style the elements via inline styles using the `styles` prop.\n *\n * ### Form fields\n *\n * If you need to bind the date picker to a form field, you can use the\n * `useInput` hooks for a basic behavior. See the `useInput` source as an\n * example to bind the date picker with form fields.\n *\n * ### Localization\n *\n * To localize DayPicker, import the locale from `date-fns` package and use the\n * `locale` prop.\n *\n * For example, to use Spanish locale:\n *\n * ```\n * import { es } from 'date-fns/locale';\n * <DayPicker locale={es} />\n * ```\n */\nfunction DayPicker(props) {\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(RootProvider, __assign({}, props, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Root, { initialProps: props }) })));\n}\n\n/** @private */\nfunction isValidDate(day) {\n    return !isNaN(day.getTime());\n}\n\n/** Return props and setters for binding an input field to DayPicker. */\nfunction useInput(options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.locale, locale = _a === void 0 ? date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.enUS : _a, required = options.required, _b = options.format, format$1 = _b === void 0 ? 'PP' : _b, defaultSelected = options.defaultSelected, _c = options.today, today = _c === void 0 ? new Date() : _c;\n    var _d = parseFromToProps(options), fromDate = _d.fromDate, toDate = _d.toDate;\n    // Shortcut to the DateFns functions\n    var parseValue = function (value) { return (0,date_fns__WEBPACK_IMPORTED_MODULE_33__.parse)(value, format$1, today, { locale: locale }); };\n    // Initialize states\n    var _e = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSelected !== null && defaultSelected !== void 0 ? defaultSelected : today), month = _e[0], setMonth = _e[1];\n    var _f = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSelected), selectedDay = _f[0], setSelectedDay = _f[1];\n    var defaultInputValue = defaultSelected\n        ? (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(defaultSelected, format$1, { locale: locale })\n        : '';\n    var _g = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultInputValue), inputValue = _g[0], setInputValue = _g[1];\n    var reset = function () {\n        setSelectedDay(defaultSelected);\n        setMonth(defaultSelected !== null && defaultSelected !== void 0 ? defaultSelected : today);\n        setInputValue(defaultInputValue !== null && defaultInputValue !== void 0 ? defaultInputValue : '');\n    };\n    var setSelected = function (date) {\n        setSelectedDay(date);\n        setMonth(date !== null && date !== void 0 ? date : today);\n        setInputValue(date ? (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(date, format$1, { locale: locale }) : '');\n    };\n    var handleDayClick = function (day, _a) {\n        var selected = _a.selected;\n        if (!required && selected) {\n            setSelectedDay(undefined);\n            setInputValue('');\n            return;\n        }\n        setSelectedDay(day);\n        setInputValue(day ? (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(day, format$1, { locale: locale }) : '');\n    };\n    var handleMonthChange = function (month) {\n        setMonth(month);\n    };\n    // When changing the input field, save its value in state and check if the\n    // string is a valid date. If it is a valid day, set it as selected and update\n    // the calendar’s month.\n    var handleChange = function (e) {\n        setInputValue(e.target.value);\n        var day = parseValue(e.target.value);\n        var isBefore = fromDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(fromDate, day) > 0;\n        var isAfter = toDate && (0,date_fns__WEBPACK_IMPORTED_MODULE_21__.differenceInCalendarDays)(day, toDate) > 0;\n        if (!isValidDate(day) || isBefore || isAfter) {\n            setSelectedDay(undefined);\n            return;\n        }\n        setSelectedDay(day);\n        setMonth(day);\n    };\n    // Special case for _required_ fields: on blur, if the value of the input is not\n    // a valid date, reset the calendar and the input value.\n    var handleBlur = function (e) {\n        var day = parseValue(e.target.value);\n        if (!isValidDate(day)) {\n            reset();\n        }\n    };\n    // When focusing, make sure DayPicker visualizes the month of the date in the\n    // input field.\n    var handleFocus = function (e) {\n        if (!e.target.value) {\n            reset();\n            return;\n        }\n        var day = parseValue(e.target.value);\n        if (isValidDate(day)) {\n            setMonth(day);\n        }\n    };\n    var dayPickerProps = {\n        month: month,\n        onDayClick: handleDayClick,\n        onMonthChange: handleMonthChange,\n        selected: selectedDay,\n        locale: locale,\n        fromDate: fromDate,\n        toDate: toDate,\n        today: today\n    };\n    var inputProps = {\n        onBlur: handleBlur,\n        onChange: handleChange,\n        onFocus: handleFocus,\n        value: inputValue,\n        placeholder: (0,date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(new Date(), format$1, { locale: locale })\n    };\n    return { dayPickerProps: dayPickerProps, inputProps: inputProps, reset: reset, setSelected: setSelected };\n}\n\n/** Returns true when the props are of type {@link DayPickerDefaultProps}. */\nfunction isDayPickerDefault(props) {\n    return props.mode === undefined || props.mode === 'default';\n}\n\n\n//# sourceMappingURL=index.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-day-picker/dist/index.esm.js\n");

/***/ })

};
;