import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APInsightAndPerspective,
  APInsightAndPerspectiveBaseData,
} from "@/features/account-plan/types/position-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createInsightAndPerspective = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APInsightAndPerspectiveBaseData;
}): ApiResponse<APInsightAndPerspective> => {
  return api.post(
    API_ROUTES.ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE(accountId),
    data
  );
};

type UseCreateInsightAndPerspectiveOptions = {
  mutationConfig?: MutationConfig<typeof createInsightAndPerspective>;
};

export const useCreateInsightAndPerspective = ({
  mutationConfig,
}: UseCreateInsightAndPerspectiveOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createInsightAndPerspective,
  });
};
