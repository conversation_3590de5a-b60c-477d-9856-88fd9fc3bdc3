GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.0.6)
      actionpack (= 7.0.6)
      activesupport (= 7.0.6)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (7.0.6)
      actionpack (= 7.0.6)
      activejob (= 7.0.6)
      activerecord (= 7.0.6)
      activestorage (= 7.0.6)
      activesupport (= 7.0.6)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.0.6)
      actionpack (= 7.0.6)
      actionview (= 7.0.6)
      activejob (= 7.0.6)
      activesupport (= 7.0.6)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (7.0.6)
      actionview (= 7.0.6)
      activesupport (= 7.0.6)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (7.0.6)
      actionpack (= 7.0.6)
      activerecord (= 7.0.6)
      activestorage (= 7.0.6)
      activesupport (= 7.0.6)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.0.6)
      activesupport (= 7.0.6)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (7.0.6)
      activesupport (= 7.0.6)
      globalid (>= 0.3.6)
    activemodel (7.0.6)
      activesupport (= 7.0.6)
    activerecord (7.0.6)
      activemodel (= 7.0.6)
      activesupport (= 7.0.6)
    activestorage (7.0.6)
      actionpack (= 7.0.6)
      activejob (= 7.0.6)
      activerecord (= 7.0.6)
      activesupport (= 7.0.6)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (7.0.6)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    aws-eventstream (1.3.0)
    aws-partitions (1.968.0)
    aws-sdk-core (3.201.5)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.9)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.88.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.159.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.9.1)
      aws-eventstream (~> 1, >= 1.0.2)
    bcrypt (3.1.20)
    bootsnap (1.16.0)
      msgpack (~> 1.2)
    builder (3.2.4)
    childprocess (5.1.0)
      logger (~> 1.5)
    coderay (1.1.3)
    concurrent-ruby (1.2.2)
    connection_pool (2.4.1)
    crass (1.0.6)
    cronex (0.15.0)
      tzinfo
      unicode (>= *******)
    date (3.3.3)
    debug (1.8.0)
      irb (>= 1.5.0)
      reline (>= 0.3.1)
    diff-lcs (1.5.1)
    discard (1.3.0)
      activerecord (>= 4.2, < 8)
    dockerfile-rails (1.5.0)
      rails
    domain_name (0.6.20240107)
    dotenv (3.1.2)
    erubi (1.12.0)
    et-orbi (1.2.11)
      tzinfo
    event_stream_parser (1.0.0)
    faraday (2.7.9)
      faraday-net_http (>= 2.0, < 3.1)
      ruby2_keywords (>= 0.0.4)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (3.0.2)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.1.0)
      activesupport (>= 5.0)
    honeybadger (4.12.2)
    http-accept (1.7.0)
    http-cookie (1.0.7)
      domain_name (~> 0.5)
    i18n (1.14.1)
      concurrent-ruby (~> 1.0)
    io-console (0.6.0)
    irb (1.7.0)
      reline (>= 0.3.0)
    jmespath (1.6.2)
    jwt (2.7.1)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    launchy (3.0.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    logger (1.6.1)
    loofah (2.21.3)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.2)
    method_source (1.0.0)
    mime-types (3.6.0)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2024.1105)
    mini_mime (1.1.2)
    minitest (5.18.1)
    msgpack (1.7.1)
    multipart-post (2.4.1)
    net-imap (0.3.6)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.1)
      timeout
    net-smtp (0.3.3)
      net-protocol
    netrc (0.11.0)
    nio4r (2.5.9)
    nokogiri (1.15.2-aarch64-linux)
      racc (~> 1.4)
    nokogiri (1.15.2-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.15.2-x86_64-linux)
      racc (~> 1.4)
    open-uri (0.2.0)
      stringio
      time
      uri
    pg (1.5.3)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-rails (0.3.9)
      pry (>= 0.10.4)
    public_suffix (6.0.1)
    puma (5.6.6)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.7.1)
    rack (2.2.7)
    rack-cors (2.0.1)
      rack (>= 2.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (7.0.6)
      actioncable (= 7.0.6)
      actionmailbox (= 7.0.6)
      actionmailer (= 7.0.6)
      actionpack (= 7.0.6)
      actiontext (= 7.0.6)
      actionview (= 7.0.6)
      activejob (= 7.0.6)
      activemodel (= 7.0.6)
      activerecord (= 7.0.6)
      activestorage (= 7.0.6)
      activesupport (= 7.0.6)
      bundler (>= 1.15.0)
      railties (= 7.0.6)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (7.0.6)
      actionpack (= 7.0.6)
      activesupport (= 7.0.6)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rake (13.0.6)
    redis (5.0.6)
      redis-client (>= 0.9.0)
    redis-client (0.14.1)
      connection_pool
    reline (0.3.5)
      io-console (~> 0.5)
    request_store (1.5.1)
      rack (>= 1.4)
    request_store-sidekiq (0.1.0)
      request_store (>= 1.3)
      sidekiq (>= 3.0)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rspec-core (3.13.1)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.0.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.1)
    ruby-openai (7.1.0)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby2_keywords (0.0.5)
    sentry-rails (4.8.1)
      railties (>= 5.0)
      sentry-ruby-core (~> 4.8.1)
    sentry-ruby (4.8.1)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      faraday (>= 1.0)
      sentry-ruby-core (= 4.8.1)
    sentry-ruby-core (4.8.1)
      concurrent-ruby
      faraday
    sentry-sidekiq (4.8.1)
      sentry-ruby-core (~> 4.8.1)
      sidekiq (>= 3.0)
    sidekiq (7.1.2)
      concurrent-ruby (< 2)
      connection_pool (>= 2.3.0)
      rack (>= 2.2.4)
      redis-client (>= 0.14.0)
    sidekiq-cron (2.0.1)
      cronex (>= 0.13.0)
      fugit (~> 1.8, >= 1.11.1)
      globalid (>= 1.0.1)
      sidekiq (>= 6.5.0)
    stringio (3.1.1)
    thor (1.2.2)
    tiktoken_ruby (0.0.5-aarch64-linux)
    tiktoken_ruby (0.0.5-arm64-darwin)
    tiktoken_ruby (0.0.5-x86_64-linux)
    time (0.2.0)
      date
    timeout (0.4.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode (*******)
    uri (0.13.0)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.6.8)

PLATFORMS
  aarch64-linux
  arm64-darwin-22
  x86_64-linux

DEPENDENCIES
  aws-sdk-s3
  bcrypt (~> 3.1.7)
  bootsnap
  debug
  discard (~> 1.2)
  dockerfile-rails (>= 1.5)
  dotenv
  faraday
  honeybadger (~> 4.0)
  jwt
  kaminari
  letter_opener
  open-uri
  pg (~> 1.1)
  pry-rails
  puma (~> 5.0)
  rack-cors
  rails (~> 7.0.4, >= 7.0.4.3)
  redis (~> 5.0)
  request_store (~> 1.5)
  request_store-sidekiq (= 0.1.0)
  rest-client
  rspec-rails (~> 7.0.0)
  ruby-openai (= 7.1.0)
  sentry-rails (~> 4.8, >= 4.8.1)
  sentry-ruby (~> 4.8, >= 4.8.1)
  sentry-sidekiq (~> 4.8, >= 4.8.1)
  sidekiq
  sidekiq-cron
  tiktoken_ruby
  tzinfo-data

RUBY VERSION
   ruby 3.1.2p20

BUNDLED WITH
   2.3.7
