import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APCurrentOpportunity } from "@/features/account-plan/types/revenue-types";

export const getCurrentOpportunityDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APCurrentOpportunity> => {
  return api.get(
    API_ROUTES.ACCOUNT_PLANS_CURRENT_OPPORTUNITY_DETAIL(accountId, id)
  );
};

export const getCurrentOpportunityDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_CURRENT_OPPORTUNITY,
      id,
    ],
    queryFn: () => getCurrentOpportunityDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseCurrentOpportunityDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getCurrentOpportunityDetail>;
  options?: Partial<ReturnType<typeof getCurrentOpportunityDetailQueryOptions>>;
};

export const useCurrentOpportunityDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseCurrentOpportunityDetailOptions) => {
  const currentOpportunityDetailQuery = useQuery({
    ...getCurrentOpportunityDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...currentOpportunityDetailQuery,
    currentOpportunityDetail: currentOpportunityDetailQuery.data?.data,
  };
};
