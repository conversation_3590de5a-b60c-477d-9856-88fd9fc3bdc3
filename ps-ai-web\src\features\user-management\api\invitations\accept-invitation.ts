import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  UserManagementAcceptInvitationBaseData,
  UserManagementAcceptInvitationData,
} from "../../types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const userManagementAcceptInvitation = ({
  data,
}: {
  data?: UserManagementAcceptInvitationBaseData;
}): ApiResponse<UserManagementAcceptInvitationData> => {
  return api.post(`${API_ROUTES.USER_MANAGEMENTS_ACCEPT_INVITATION}`, data);
};

type UseUserManagementAcceptInvitationOptions = {
  mutationConfig?: MutationConfig<typeof userManagementAcceptInvitation>;
};

export const useUserManagementAcceptInvitation = ({
  mutationConfig,
}: UseUserManagementAcceptInvitationOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.USER_MANAGEMENTS],
      });
    },
    ...restConfig,
    mutationFn: userManagementAcceptInvitation,
  });
};
