"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ag-grid-react";
exports.ids = ["vendor-chunks/ag-grid-react"];
exports.modules = {

/***/ "(ssr)/./node_modules/ag-grid-react/dist/package/index.esm.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/ag-grid-react/dist/package/index.esm.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgGridReact: () => (/* binding */ AgGridReact),\n/* harmony export */   CustomComponentContext: () => (/* binding */ CustomContext),\n/* harmony export */   getInstance: () => (/* binding */ getInstance),\n/* harmony export */   useGridCellEditor: () => (/* binding */ useGridCellEditor),\n/* harmony export */   useGridDate: () => (/* binding */ useGridDate),\n/* harmony export */   useGridFilter: () => (/* binding */ useGridFilter),\n/* harmony export */   useGridFloatingFilter: () => (/* binding */ useGridFloatingFilter),\n/* harmony export */   useGridMenuItem: () => (/* binding */ useGridMenuItem),\n/* harmony export */   warnReactiveCustomComponents: () => (/* binding */ warnReactiveCustomComponents)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var ag_grid_community__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ag-grid-community */ \"(ssr)/./node_modules/ag-grid-community/dist/package/main.esm.mjs\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n// packages/ag-grid-react/src/agGridReact.tsx\n\n\n// packages/ag-grid-react/src/reactUi/agGridReactUi.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/cellRenderer/groupCellRenderer.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/beansContext.tsx\n\nvar BeansContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext({});\n\n// packages/ag-grid-react/src/reactUi/jsComp.tsx\nvar showJsComp = (compDetails, context, eParent, ref) => {\n  const doNothing = !compDetails || compDetails.componentFromFramework || context.isDestroyed();\n  if (doNothing) {\n    return;\n  }\n  const promise = compDetails.newAgStackInstance();\n  if (promise == null) {\n    return;\n  }\n  let comp;\n  let compGui;\n  let destroyed = false;\n  promise.then((c) => {\n    if (destroyed) {\n      context.destroyBean(c);\n      return;\n    }\n    comp = c;\n    compGui = comp.getGui();\n    eParent.appendChild(compGui);\n    setRef(ref, comp);\n  });\n  return () => {\n    destroyed = true;\n    if (!comp) {\n      return;\n    }\n    compGui?.parentElement?.removeChild(compGui);\n    context.destroyBean(comp);\n    if (ref) {\n      setRef(ref, void 0);\n    }\n  };\n};\nvar setRef = (ref, value) => {\n  if (!ref) {\n    return;\n  }\n  if (ref instanceof Function) {\n    const refCallback = ref;\n    refCallback(value);\n  } else {\n    const refObj = ref;\n    refObj.current = value;\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/utils.tsx\n\n\nvar classesList = (...list) => {\n  const filtered = list.filter((s) => s != null && s !== \"\");\n  return filtered.join(\" \");\n};\nvar CssClasses = class _CssClasses {\n  constructor(...initialClasses) {\n    this.classesMap = {};\n    initialClasses.forEach((className) => {\n      this.classesMap[className] = true;\n    });\n  }\n  setClass(className, on) {\n    const nothingHasChanged = !!this.classesMap[className] == on;\n    if (nothingHasChanged) {\n      return this;\n    }\n    const res = new _CssClasses();\n    res.classesMap = { ...this.classesMap };\n    res.classesMap[className] = on;\n    return res;\n  }\n  toString() {\n    const res = Object.keys(this.classesMap).filter((key) => this.classesMap[key]).join(\" \");\n    return res;\n  }\n};\nvar isComponentStateless = (Component2) => {\n  const hasSymbol = () => typeof Symbol === \"function\" && Symbol.for;\n  const getMemoType = () => hasSymbol() ? Symbol.for(\"react.memo\") : 60115;\n  return typeof Component2 === \"function\" && !(Component2.prototype && Component2.prototype.isReactComponent) || typeof Component2 === \"object\" && Component2.$$typeof === getMemoType();\n};\nvar reactVersion = react__WEBPACK_IMPORTED_MODULE_0__.version?.split(\".\")[0];\nvar isReactVersion17Minus = reactVersion === \"16\" || reactVersion === \"17\";\nfunction isReact19() {\n  return reactVersion === \"19\";\n}\nvar disableFlushSync = false;\nfunction runWithoutFlushSync(func) {\n  if (!disableFlushSync) {\n    setTimeout(() => disableFlushSync = false, 0);\n  }\n  disableFlushSync = true;\n  return func();\n}\nvar agFlushSync = (useFlushSync, fn) => {\n  if (!isReactVersion17Minus && useFlushSync && !disableFlushSync) {\n    react_dom__WEBPACK_IMPORTED_MODULE_2__.flushSync(fn);\n  } else {\n    fn();\n  }\n};\nfunction getNextValueIfDifferent(prev, next, maintainOrder) {\n  if (next == null || prev == null) {\n    return next;\n  }\n  if (prev === next || next.length === 0 && prev.length === 0) {\n    return prev;\n  }\n  if (maintainOrder || prev.length === 0 && next.length > 0 || prev.length > 0 && next.length === 0) {\n    return next;\n  }\n  const oldValues = [];\n  const newValues = [];\n  const prevMap = /* @__PURE__ */ new Map();\n  const nextMap = /* @__PURE__ */ new Map();\n  for (let i = 0; i < next.length; i++) {\n    const c = next[i];\n    nextMap.set(c.instanceId, c);\n  }\n  for (let i = 0; i < prev.length; i++) {\n    const c = prev[i];\n    prevMap.set(c.instanceId, c);\n    if (nextMap.has(c.instanceId)) {\n      oldValues.push(c);\n    }\n  }\n  for (let i = 0; i < next.length; i++) {\n    const c = next[i];\n    const instanceId = c.instanceId;\n    if (!prevMap.has(instanceId)) {\n      newValues.push(c);\n    }\n  }\n  if (oldValues.length === prev.length && newValues.length === 0) {\n    return prev;\n  }\n  if (oldValues.length === 0 && newValues.length === next.length) {\n    return next;\n  }\n  if (oldValues.length === 0) {\n    return newValues;\n  }\n  if (newValues.length === 0) {\n    return oldValues;\n  }\n  return [...oldValues, ...newValues];\n}\n\n// packages/ag-grid-react/src/reactUi/cellRenderer/groupCellRenderer.tsx\nvar GroupCellRenderer = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const { ctrlsFactory, context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eValueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eCheckboxRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eExpandedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eContractedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const ctrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [innerCompDetails, setInnerCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [childCount, setChildCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [expandedCssClasses, setExpandedCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses(\"ag-hidden\"));\n  const [contractedCssClasses, setContractedCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses(\"ag-hidden\"));\n  const [checkboxCssClasses, setCheckboxCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses(\"ag-invisible\"));\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => {\n    return {\n      // force new instance when grid tries to refresh\n      refresh() {\n        return false;\n      }\n    };\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    return showJsComp(innerCompDetails, context, eValueRef.current);\n  }, [innerCompDetails]);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    if (!eRef) {\n      ctrlRef.current = context.destroyBean(ctrlRef.current);\n      return;\n    }\n    const compProxy = {\n      setInnerRenderer: (details, valueToDisplay) => {\n        setInnerCompDetails(details);\n        setValue(valueToDisplay);\n      },\n      setChildCount: (count) => setChildCount(count),\n      addOrRemoveCssClass: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setContractedDisplayed: (displayed) => setContractedCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed)),\n      setExpandedDisplayed: (displayed) => setExpandedCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed)),\n      setCheckboxVisible: (visible) => setCheckboxCssClasses((prev) => prev.setClass(\"ag-invisible\", !visible))\n    };\n    const groupCellRendererCtrl = ctrlsFactory.getInstance(\"groupCellRendererCtrl\");\n    if (groupCellRendererCtrl) {\n      ctrlRef.current = context.createBean(groupCellRendererCtrl);\n      ctrlRef.current.init(\n        compProxy,\n        eRef,\n        eCheckboxRef.current,\n        eExpandedRef.current,\n        eContractedRef.current,\n        GroupCellRenderer,\n        props\n      );\n    }\n  }, []);\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => `ag-cell-wrapper ${cssClasses.toString()}`, [cssClasses]);\n  const expandedClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => `ag-group-expanded ${expandedCssClasses.toString()}`, [expandedCssClasses]);\n  const contractedClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => `ag-group-contracted ${contractedCssClasses.toString()}`,\n    [contractedCssClasses]\n  );\n  const checkboxClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => `ag-group-checkbox ${checkboxCssClasses.toString()}`, [checkboxCssClasses]);\n  const useFwRenderer = innerCompDetails && innerCompDetails.componentFromFramework;\n  const FwRenderer = useFwRenderer ? innerCompDetails.componentClass : void 0;\n  const useValue = innerCompDetails == null && value != null;\n  const escapedValue = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._escapeString)(value, true);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"span\",\n    {\n      className,\n      ref: setRef2,\n      ...!props.colDef ? { role: ctrlRef.current?.getCellAriaRole() } : {}\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: expandedClassName, ref: eExpandedRef }),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: contractedClassName, ref: eContractedRef }),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: checkboxClassName, ref: eCheckboxRef }),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: \"ag-group-value\", ref: eValueRef }, useValue && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, escapedValue), useFwRenderer && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FwRenderer, { ...innerCompDetails.params })),\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { className: \"ag-group-child-count\" }, childCount)\n  );\n});\nvar groupCellRenderer_default = GroupCellRenderer;\n\n// packages/ag-grid-react/src/shared/customComp/customComponentWrapper.ts\n\n\n// packages/ag-grid-react/src/reactUi/customComp/customWrapperComp.tsx\n\n\n// packages/ag-grid-react/src/shared/customComp/customContext.ts\n\nvar CustomContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n  setMethods: () => {\n  }\n});\n\n// packages/ag-grid-react/src/reactUi/customComp/customWrapperComp.tsx\nvar CustomWrapperComp = (params) => {\n  const { initialProps, addUpdateCallback, CustomComponentClass, setMethods } = params;\n  const [{ key, ...props }, setProps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialProps);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    addUpdateCallback((newProps) => setProps(newProps));\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CustomContext.Provider, { value: { setMethods } }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CustomComponentClass, { key, ...props }));\n};\nvar customWrapperComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(CustomWrapperComp);\n\n// packages/ag-grid-react/src/shared/reactComponent.ts\n\n\n\n\n// packages/ag-grid-react/src/shared/keyGenerator.ts\nvar counter = 0;\nfunction generateNewKey() {\n  return `agPortalKey_${++counter}`;\n}\n\n// packages/ag-grid-react/src/shared/reactComponent.ts\nvar ReactComponent = class {\n  constructor(reactComponent, portalManager, componentType, suppressFallbackMethods) {\n    this.portal = null;\n    this.oldPortal = null;\n    this.reactComponent = reactComponent;\n    this.portalManager = portalManager;\n    this.componentType = componentType;\n    this.suppressFallbackMethods = !!suppressFallbackMethods;\n    this.statelessComponent = this.isStateless(this.reactComponent);\n    this.key = generateNewKey();\n    this.portalKey = generateNewKey();\n    this.instanceCreated = this.isStatelessComponent() ? ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.AgPromise.resolve(false) : new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.AgPromise((resolve) => {\n      this.resolveInstanceCreated = resolve;\n    });\n  }\n  getGui() {\n    return this.eParentElement;\n  }\n  /** `getGui()` returns the parent element. This returns the actual root element. */\n  getRootElement() {\n    const firstChild = this.eParentElement.firstChild;\n    return firstChild;\n  }\n  destroy() {\n    if (this.componentInstance && typeof this.componentInstance.destroy == \"function\") {\n      this.componentInstance.destroy();\n    }\n    const portal = this.portal;\n    if (portal) {\n      this.portalManager.destroyPortal(portal);\n    }\n  }\n  createParentElement(params) {\n    const componentWrappingElement = this.portalManager.getComponentWrappingElement();\n    const eParentElement = document.createElement(componentWrappingElement || \"div\");\n    eParentElement.classList.add(\"ag-react-container\");\n    params.reactContainer = eParentElement;\n    return eParentElement;\n  }\n  addParentContainerStyleAndClasses() {\n    if (!this.componentInstance) {\n      return;\n    }\n    if (this.componentInstance.getReactContainerStyle && this.componentInstance.getReactContainerStyle()) {\n      (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._warnOnce)(\n        'Since v31.1 \"getReactContainerStyle\" is deprecated. Apply styling directly to \".ag-react-container\" if needed.'\n      );\n      Object.assign(this.eParentElement.style, this.componentInstance.getReactContainerStyle());\n    }\n    if (this.componentInstance.getReactContainerClasses && this.componentInstance.getReactContainerClasses()) {\n      (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._warnOnce)(\n        'Since v31.1 \"getReactContainerClasses\" is deprecated. Apply styling directly to \".ag-react-container\" if needed.'\n      );\n      const parentContainerClasses = this.componentInstance.getReactContainerClasses();\n      parentContainerClasses.forEach((className) => this.eParentElement.classList.add(className));\n    }\n  }\n  statelessComponentRendered() {\n    return this.eParentElement.childElementCount > 0 || this.eParentElement.childNodes.length > 0;\n  }\n  getFrameworkComponentInstance() {\n    return this.componentInstance;\n  }\n  isStatelessComponent() {\n    return this.statelessComponent;\n  }\n  getReactComponentName() {\n    return this.reactComponent.name;\n  }\n  getMemoType() {\n    return this.hasSymbol() ? Symbol.for(\"react.memo\") : 60115;\n  }\n  hasSymbol() {\n    return typeof Symbol === \"function\" && Symbol.for;\n  }\n  isStateless(Component2) {\n    return typeof Component2 === \"function\" && !(Component2.prototype && Component2.prototype.isReactComponent) || typeof Component2 === \"object\" && Component2.$$typeof === this.getMemoType();\n  }\n  hasMethod(name) {\n    const frameworkComponentInstance = this.getFrameworkComponentInstance();\n    return !!frameworkComponentInstance && frameworkComponentInstance[name] != null || this.fallbackMethodAvailable(name);\n  }\n  callMethod(name, args) {\n    const frameworkComponentInstance = this.getFrameworkComponentInstance();\n    if (this.isStatelessComponent()) {\n      return this.fallbackMethod(name, !!args && args[0] ? args[0] : {});\n    } else if (!frameworkComponentInstance) {\n      setTimeout(() => this.callMethod(name, args));\n      return;\n    }\n    const method = frameworkComponentInstance[name];\n    if (method) {\n      return method.apply(frameworkComponentInstance, args);\n    }\n    if (this.fallbackMethodAvailable(name)) {\n      return this.fallbackMethod(name, !!args && args[0] ? args[0] : {});\n    }\n  }\n  addMethod(name, callback) {\n    this[name] = callback;\n  }\n  init(params) {\n    this.eParentElement = this.createParentElement(params);\n    this.createOrUpdatePortal(params);\n    return new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.AgPromise((resolve) => this.createReactComponent(resolve));\n  }\n  createOrUpdatePortal(params) {\n    if (!this.isStatelessComponent()) {\n      this.ref = (element) => {\n        this.componentInstance = element;\n        this.addParentContainerStyleAndClasses();\n        this.resolveInstanceCreated?.(true);\n        this.resolveInstanceCreated = void 0;\n      };\n      params.ref = this.ref;\n    }\n    this.reactElement = this.createElement(this.reactComponent, { ...params, key: this.key });\n    this.portal = (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(\n      this.reactElement,\n      this.eParentElement,\n      this.portalKey\n      // fixed deltaRowModeRefreshCompRenderer\n    );\n  }\n  createElement(reactComponent, props) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(reactComponent, props);\n  }\n  createReactComponent(resolve) {\n    this.portalManager.mountReactPortal(this.portal, this, resolve);\n  }\n  rendered() {\n    return this.isStatelessComponent() && this.statelessComponentRendered() || !!(!this.isStatelessComponent() && this.getFrameworkComponentInstance());\n  }\n  /*\n   * fallback methods - these will be invoked if a corresponding instance method is not present\n   * for example if refresh is called and is not available on the component instance, then refreshComponent on this\n   * class will be invoked instead\n   *\n   * Currently only refresh is supported\n   */\n  refreshComponent(args) {\n    this.oldPortal = this.portal;\n    this.createOrUpdatePortal(args);\n    this.portalManager.updateReactPortal(this.oldPortal, this.portal);\n  }\n  fallbackMethod(name, params) {\n    const method = this[`${name}Component`];\n    if (!this.suppressFallbackMethods && !!method) {\n      return method.bind(this)(params);\n    }\n  }\n  fallbackMethodAvailable(name) {\n    if (this.suppressFallbackMethods) {\n      return false;\n    }\n    const method = this[`${name}Component`];\n    return !!method;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/customComponentWrapper.ts\nfunction addOptionalMethods(optionalMethodNames, providedMethods, component) {\n  optionalMethodNames.forEach((methodName) => {\n    const providedMethod = providedMethods[methodName];\n    if (providedMethod) {\n      component[methodName] = providedMethod;\n    }\n  });\n}\nvar CustomComponentWrapper = class extends ReactComponent {\n  constructor() {\n    super(...arguments);\n    this.awaitUpdateCallback = new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.AgPromise((resolve) => {\n      this.resolveUpdateCallback = resolve;\n    });\n    this.wrapperComponent = customWrapperComp_default;\n  }\n  init(params) {\n    this.sourceParams = params;\n    return super.init(this.getProps());\n  }\n  addMethod() {\n  }\n  getInstance() {\n    return this.instanceCreated.then(() => this.componentInstance);\n  }\n  getFrameworkComponentInstance() {\n    return this;\n  }\n  createElement(reactComponent, props) {\n    return super.createElement(this.wrapperComponent, {\n      initialProps: props,\n      CustomComponentClass: reactComponent,\n      setMethods: (methods) => this.setMethods(methods),\n      addUpdateCallback: (callback) => {\n        this.updateCallback = () => {\n          callback(this.getProps());\n          return new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.AgPromise((resolve) => {\n            setTimeout(() => {\n              resolve();\n            });\n          });\n        };\n        this.resolveUpdateCallback();\n      }\n    });\n  }\n  setMethods(methods) {\n    this.providedMethods = methods;\n    addOptionalMethods(this.getOptionalMethods(), this.providedMethods, this);\n  }\n  getOptionalMethods() {\n    return [];\n  }\n  getProps() {\n    return {\n      ...this.sourceParams,\n      key: this.key,\n      ref: this.ref\n    };\n  }\n  refreshProps() {\n    if (this.updateCallback) {\n      return this.updateCallback();\n    }\n    return new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.AgPromise(\n      (resolve) => this.awaitUpdateCallback.then(() => {\n        this.updateCallback().then(() => resolve());\n      })\n    );\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/cellRendererComponentWrapper.ts\nvar CellRendererComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/dateComponentWrapper.ts\nvar DateComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.date = null;\n    this.onDateChange = (date) => this.updateDate(date);\n  }\n  getDate() {\n    return this.date;\n  }\n  setDate(date) {\n    this.date = date;\n    this.refreshProps();\n  }\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\", \"setInputPlaceholder\", \"setInputAriaLabel\", \"setDisabled\"];\n  }\n  updateDate(date) {\n    this.setDate(date);\n    this.sourceParams.onDateChanged();\n  }\n  getProps() {\n    const props = super.getProps();\n    props.date = this.date;\n    props.onDateChange = this.onDateChange;\n    delete props.onDateChanged;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/dragAndDropImageComponentWrapper.ts\nvar DragAndDropImageComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.label = \"\";\n    this.icon = null;\n    this.shake = false;\n  }\n  setIcon(iconName, shake) {\n    this.icon = iconName;\n    this.shake = shake;\n    this.refreshProps();\n  }\n  setLabel(label) {\n    this.label = label;\n    this.refreshProps();\n  }\n  getProps() {\n    const props = super.getProps();\n    const { label, icon, shake } = this;\n    props.label = label;\n    props.icon = icon;\n    props.shake = shake;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/filterComponentWrapper.ts\nvar FilterComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.model = null;\n    this.onModelChange = (model) => this.updateModel(model);\n    this.onUiChange = () => this.sourceParams.filterChangedCallback();\n    this.expectingNewMethods = true;\n    this.hasBeenActive = false;\n  }\n  isFilterActive() {\n    return this.model != null;\n  }\n  doesFilterPass(params) {\n    return this.providedMethods.doesFilterPass(params);\n  }\n  getModel() {\n    return this.model;\n  }\n  setModel(model) {\n    this.expectingNewMethods = true;\n    this.model = model;\n    this.hasBeenActive || (this.hasBeenActive = this.isFilterActive());\n    return this.refreshProps();\n  }\n  refresh(newParams) {\n    this.sourceParams = newParams;\n    this.refreshProps();\n    return true;\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\", \"afterGuiDetached\", \"onNewRowsLoaded\", \"getModelAsString\", \"onAnyFilterChanged\"];\n  }\n  setMethods(methods) {\n    if (this.expectingNewMethods === false && this.hasBeenActive && this.providedMethods?.doesFilterPass !== methods?.doesFilterPass) {\n      setTimeout(() => {\n        this.sourceParams.filterChangedCallback();\n      });\n    }\n    this.expectingNewMethods = false;\n    super.setMethods(methods);\n  }\n  updateModel(model) {\n    this.setModel(model).then(() => this.sourceParams.filterChangedCallback());\n  }\n  getProps() {\n    const props = super.getProps();\n    props.model = this.model;\n    props.onModelChange = this.onModelChange;\n    props.onUiChange = this.onUiChange;\n    delete props.filterChangedCallback;\n    delete props.filterModifiedCallback;\n    delete props.valueGetter;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/floatingFilterComponentProxy.ts\n\nfunction updateFloatingFilterParent(params, model) {\n  params.parentFilterInstance((instance) => {\n    (instance.setModel(model) || ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.AgPromise.resolve()).then(() => {\n      params.filterParams.filterChangedCallback();\n    });\n  });\n}\nvar FloatingFilterComponentProxy = class {\n  constructor(floatingFilterParams, refreshProps) {\n    this.floatingFilterParams = floatingFilterParams;\n    this.refreshProps = refreshProps;\n    this.model = null;\n    this.onModelChange = (model) => this.updateModel(model);\n  }\n  getProps() {\n    return {\n      ...this.floatingFilterParams,\n      model: this.model,\n      onModelChange: this.onModelChange\n    };\n  }\n  onParentModelChanged(parentModel) {\n    this.model = parentModel;\n    this.refreshProps();\n  }\n  refresh(params) {\n    this.floatingFilterParams = params;\n    this.refreshProps();\n  }\n  setMethods(methods) {\n    addOptionalMethods(this.getOptionalMethods(), methods, this);\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\"];\n  }\n  updateModel(model) {\n    this.model = model;\n    this.refreshProps();\n    updateFloatingFilterParent(this.floatingFilterParams, model);\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/floatingFilterComponentWrapper.ts\nvar FloatingFilterComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.model = null;\n    this.onModelChange = (model) => this.updateModel(model);\n  }\n  onParentModelChanged(parentModel) {\n    this.model = parentModel;\n    this.refreshProps();\n  }\n  refresh(newParams) {\n    this.sourceParams = newParams;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"afterGuiAttached\"];\n  }\n  updateModel(model) {\n    this.model = model;\n    this.refreshProps();\n    updateFloatingFilterParent(this.sourceParams, model);\n  }\n  getProps() {\n    const props = super.getProps();\n    props.model = this.model;\n    props.onModelChange = this.onModelChange;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/loadingOverlayComponentWrapper.ts\nvar LoadingOverlayComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/menuItemComponentWrapper.ts\nvar MenuItemComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.active = false;\n    this.expanded = false;\n    this.onActiveChange = (active) => this.updateActive(active);\n  }\n  setActive(active) {\n    this.awaitSetActive(active);\n  }\n  setExpanded(expanded) {\n    this.expanded = expanded;\n    this.refreshProps();\n  }\n  getOptionalMethods() {\n    return [\"select\", \"configureDefaults\"];\n  }\n  awaitSetActive(active) {\n    this.active = active;\n    return this.refreshProps();\n  }\n  updateActive(active) {\n    const result = this.awaitSetActive(active);\n    if (active) {\n      result.then(() => this.sourceParams.onItemActivated());\n    }\n  }\n  getProps() {\n    const props = super.getProps();\n    props.active = this.active;\n    props.expanded = this.expanded;\n    props.onActiveChange = this.onActiveChange;\n    delete props.onItemActivated;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/noRowsOverlayComponentWrapper.ts\nvar NoRowsOverlayComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/statusPanelComponentWrapper.ts\nvar StatusPanelComponentWrapper = class extends CustomComponentWrapper {\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/toolPanelComponentWrapper.ts\nvar ToolPanelComponentWrapper = class extends CustomComponentWrapper {\n  constructor() {\n    super(...arguments);\n    this.onStateChange = (state) => this.updateState(state);\n  }\n  refresh(params) {\n    this.sourceParams = params;\n    this.refreshProps();\n    return true;\n  }\n  getState() {\n    return this.state;\n  }\n  updateState(state) {\n    this.state = state;\n    this.refreshProps();\n    this.sourceParams.onStateUpdated();\n  }\n  getProps() {\n    const props = super.getProps();\n    props.state = this.state;\n    props.onStateChange = this.onStateChange;\n    return props;\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/util.ts\n\nfunction getInstance(wrapperComponent, callback) {\n  const promise = wrapperComponent?.getInstance?.() ?? ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.AgPromise.resolve(void 0);\n  promise.then((comp) => callback(comp));\n}\nfunction warnReactiveCustomComponents() {\n  (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._warnOnce)(\"As of v32, using custom components with `reactiveCustomComponents = false` is deprecated.\");\n}\n\n// packages/ag-grid-react/src/shared/portalManager.ts\nvar MAX_COMPONENT_CREATION_TIME_IN_MS = 1e3;\nvar PortalManager = class {\n  constructor(refresher, wrappingElement, maxComponentCreationTimeMs) {\n    this.destroyed = false;\n    this.portals = [];\n    this.hasPendingPortalUpdate = false;\n    this.wrappingElement = wrappingElement ? wrappingElement : \"div\";\n    this.refresher = refresher;\n    this.maxComponentCreationTimeMs = maxComponentCreationTimeMs ? maxComponentCreationTimeMs : MAX_COMPONENT_CREATION_TIME_IN_MS;\n  }\n  getPortals() {\n    return this.portals;\n  }\n  destroy() {\n    this.destroyed = true;\n  }\n  destroyPortal(portal) {\n    this.portals = this.portals.filter((curPortal) => curPortal !== portal);\n    this.batchUpdate();\n  }\n  getComponentWrappingElement() {\n    return this.wrappingElement;\n  }\n  mountReactPortal(portal, reactComponent, resolve) {\n    this.portals = [...this.portals, portal];\n    this.waitForInstance(reactComponent, resolve);\n    this.batchUpdate();\n  }\n  updateReactPortal(oldPortal, newPortal) {\n    this.portals[this.portals.indexOf(oldPortal)] = newPortal;\n    this.batchUpdate();\n  }\n  batchUpdate() {\n    if (this.hasPendingPortalUpdate) {\n      return;\n    }\n    setTimeout(() => {\n      if (!this.destroyed) {\n        this.refresher();\n        this.hasPendingPortalUpdate = false;\n      }\n    });\n    this.hasPendingPortalUpdate = true;\n  }\n  waitForInstance(reactComponent, resolve, startTime = Date.now()) {\n    if (this.destroyed) {\n      resolve(null);\n      return;\n    }\n    if (reactComponent.rendered()) {\n      resolve(reactComponent);\n    } else {\n      if (Date.now() - startTime >= this.maxComponentCreationTimeMs && !this.hasPendingPortalUpdate) {\n        return;\n      }\n      window.setTimeout(() => {\n        this.waitForInstance(reactComponent, resolve, startTime);\n      });\n    }\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/gridComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/gridBodyComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/gridHeaderComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/headerRowContainerComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/headerRowComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/header/headerCellComp.tsx\n\n\nvar HeaderCellComp = ({ ctrl }) => {\n  const isAlive = ctrl.isAlive();\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const colId = isAlive ? ctrl.getColId() : void 0;\n  const [userCompDetails, setUserCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eHeaderCompWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const userCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const cssClassManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (isAlive && !cssClassManager.current) {\n    cssClassManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.CssClassManager(() => eGui.current);\n  }\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef || !isAlive) {\n      return;\n    }\n    const compProxy = {\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      },\n      addOrRemoveCssClass: (name, on) => cssClassManager.current.addOrRemoveCssClass(name, on),\n      setAriaSort: (sort) => {\n        if (eGui.current) {\n          sort ? (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._setAriaSort)(eGui.current, sort) : (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._removeAriaSort)(eGui.current);\n        }\n      },\n      setUserCompDetails: (compDetails) => setUserCompDetails(compDetails),\n      getUserCompInstance: () => userCompRef.current || void 0\n    };\n    ctrl.setComp(compProxy, eRef, eResize.current, eHeaderCompWrapper.current, compBean.current);\n    const selectAllGui = ctrl.getSelectAllGui();\n    eResize.current?.insertAdjacentElement(\"afterend\", selectAllGui);\n    compBean.current.addDestroyFunc(() => selectAllGui.remove());\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(\n    () => showJsComp(userCompDetails, context, eHeaderCompWrapper.current, userCompRef),\n    [userCompDetails]\n  );\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ctrl.setDragSource(eGui.current);\n  }, [userCompDetails]);\n  const userCompStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = userCompDetails?.componentFromFramework && isComponentStateless(userCompDetails.componentClass);\n    return !!res;\n  }, [userCompDetails]);\n  const reactUserComp = userCompDetails && userCompDetails.componentFromFramework;\n  const UserCompClass = userCompDetails && userCompDetails.componentClass;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: \"ag-header-cell\", \"col-id\": colId, role: \"columnheader\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eResize, className: \"ag-header-cell-resize\", role: \"presentation\" }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eHeaderCompWrapper, className: \"ag-header-cell-comp-wrapper\", role: \"presentation\" }, reactUserComp && userCompStateless && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params }), reactUserComp && !userCompStateless && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params, ref: userCompRef })));\n};\nvar headerCellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderCellComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerFilterCellComp.tsx\n\n\nvar HeaderFilterCellComp = ({ ctrl }) => {\n  const { context, gos } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => new CssClasses(\"ag-header-cell\", \"ag-floating-filter\")\n  );\n  const [cssBodyClasses, setBodyCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [cssButtonWrapperClasses, setButtonWrapperCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => new CssClasses(\"ag-floating-filter-button\", \"ag-hidden\")\n  );\n  const [buttonWrapperAriaHidden, setButtonWrapperAriaHidden] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"false\");\n  const [userCompDetails, setUserCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [, setRenderKey] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eFloatingFilterBody = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eButtonWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eButtonShowMainFilter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const userCompResolve = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const userCompPromise = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const userCompRef = (value) => {\n    if (value == null) {\n      return;\n    }\n    userCompResolve.current && userCompResolve.current(value);\n  };\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      return;\n    }\n    userCompPromise.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.AgPromise((resolve) => {\n      userCompResolve.current = resolve;\n    });\n    const compProxy = {\n      addOrRemoveCssClass: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      addOrRemoveBodyCssClass: (name, on) => setBodyCssClasses((prev) => prev.setClass(name, on)),\n      setButtonWrapperDisplayed: (displayed) => {\n        setButtonWrapperCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed));\n        setButtonWrapperAriaHidden(!displayed ? \"true\" : \"false\");\n      },\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      },\n      setCompDetails: (compDetails) => setUserCompDetails(compDetails),\n      getFloatingFilterComp: () => userCompPromise.current ? userCompPromise.current : null,\n      setMenuIcon: (eIcon) => eButtonShowMainFilter.current?.appendChild(eIcon)\n    };\n    ctrl.setComp(compProxy, eRef, eButtonShowMainFilter.current, eFloatingFilterBody.current, compBean.current);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(\n    () => showJsComp(userCompDetails, context, eFloatingFilterBody.current, userCompRef),\n    [userCompDetails]\n  );\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssClasses.toString(), [cssClasses]);\n  const bodyClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssBodyClasses.toString(), [cssBodyClasses]);\n  const buttonWrapperClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssButtonWrapperClasses.toString(), [cssButtonWrapperClasses]);\n  const userCompStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = userCompDetails && userCompDetails.componentFromFramework && isComponentStateless(userCompDetails.componentClass);\n    return !!res;\n  }, [userCompDetails]);\n  const reactiveCustomComponents = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => gos.get(\"reactiveCustomComponents\"), []);\n  const floatingFilterCompProxy = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (userCompDetails) {\n      if (reactiveCustomComponents) {\n        const compProxy = new FloatingFilterComponentProxy(\n          userCompDetails.params,\n          () => setRenderKey((prev) => prev + 1)\n        );\n        userCompRef(compProxy);\n        return compProxy;\n      } else if (userCompDetails.componentFromFramework) {\n        warnReactiveCustomComponents();\n      }\n    }\n    return void 0;\n  }, [userCompDetails]);\n  const floatingFilterProps = floatingFilterCompProxy?.getProps();\n  const reactUserComp = userCompDetails && userCompDetails.componentFromFramework;\n  const UserCompClass = userCompDetails && userCompDetails.componentClass;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className, role: \"gridcell\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eFloatingFilterBody, className: bodyClassName, role: \"presentation\" }, reactUserComp && !reactiveCustomComponents && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params, ref: userCompStateless ? () => {\n  } : userCompRef }), reactUserComp && reactiveCustomComponents && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    CustomContext.Provider,\n    {\n      value: {\n        setMethods: (methods) => floatingFilterCompProxy.setMethods(methods)\n      }\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...floatingFilterProps })\n  )), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: eButtonWrapper,\n      \"aria-hidden\": buttonWrapperAriaHidden,\n      className: buttonWrapperClassName,\n      role: \"presentation\"\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"button\",\n      {\n        ref: eButtonShowMainFilter,\n        type: \"button\",\n        className: \"ag-button ag-floating-filter-button-button\",\n        tabIndex: -1\n      }\n    )\n  ));\n};\nvar headerFilterCellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderFilterCellComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerGroupCellComp.tsx\n\n\nvar HeaderGroupCellComp = ({ ctrl }) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [cssResizableClasses, setResizableCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [resizableAriaHidden, setResizableAriaHidden] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"false\");\n  const [ariaExpanded, setAriaExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [userCompDetails, setUserCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const colId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ctrl.getColId(), []);\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eHeaderCompWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const userCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      return;\n    }\n    const compProxy = {\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      },\n      addOrRemoveCssClass: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setHeaderWrapperHidden: (hidden) => {\n        const headerCompWrapper = eHeaderCompWrapper.current;\n        if (!headerCompWrapper) {\n          return;\n        }\n        if (hidden) {\n          headerCompWrapper.style.setProperty(\"display\", \"none\");\n        } else {\n          headerCompWrapper.style.removeProperty(\"display\");\n        }\n      },\n      setHeaderWrapperMaxHeight: (value) => {\n        const headerCompWrapper = eHeaderCompWrapper.current;\n        if (!headerCompWrapper) {\n          return;\n        }\n        if (value != null) {\n          headerCompWrapper.style.setProperty(\"max-height\", `${value}px`);\n        } else {\n          headerCompWrapper.style.removeProperty(\"max-height\");\n        }\n        headerCompWrapper.classList.toggle(\"ag-header-cell-comp-wrapper-limited-height\", value != null);\n      },\n      setUserCompDetails: (compDetails) => setUserCompDetails(compDetails),\n      setResizableDisplayed: (displayed) => {\n        setResizableCssClasses((prev) => prev.setClass(\"ag-hidden\", !displayed));\n        setResizableAriaHidden(!displayed ? \"true\" : \"false\");\n      },\n      setAriaExpanded: (expanded) => setAriaExpanded(expanded),\n      getUserCompInstance: () => userCompRef.current || void 0\n    };\n    ctrl.setComp(compProxy, eRef, eResize.current, eHeaderCompWrapper.current, compBean.current);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => showJsComp(userCompDetails, context, eHeaderCompWrapper.current), [userCompDetails]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (eGui.current) {\n      ctrl.setDragSource(eGui.current);\n    }\n  }, [userCompDetails]);\n  const userCompStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = userCompDetails?.componentFromFramework && isComponentStateless(userCompDetails.componentClass);\n    return !!res;\n  }, [userCompDetails]);\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => \"ag-header-group-cell \" + cssClasses.toString(), [cssClasses]);\n  const resizableClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => \"ag-header-cell-resize \" + cssResizableClasses.toString(),\n    [cssResizableClasses]\n  );\n  const reactUserComp = userCompDetails && userCompDetails.componentFromFramework;\n  const UserCompClass = userCompDetails && userCompDetails.componentClass;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className, \"col-id\": colId, role: \"columnheader\", \"aria-expanded\": ariaExpanded }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eHeaderCompWrapper, className: \"ag-header-cell-comp-wrapper\", role: \"presentation\" }, reactUserComp && userCompStateless && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params }), reactUserComp && !userCompStateless && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserCompClass, { ...userCompDetails.params, ref: userCompRef })), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eResize, \"aria-hidden\": resizableAriaHidden, className: resizableClassName }));\n};\nvar headerGroupCellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderGroupCellComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerRowComp.tsx\nvar HeaderRowComp = ({ ctrl }) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { topOffset, rowHeight } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ctrl.getTopAndHeight(), []);\n  const ariaRowIndex = ctrl.getAriaRowIndex();\n  const className = ctrl.getHeaderRowClass();\n  const [height, setHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowHeight + \"px\");\n  const [top, setTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => topOffset + \"px\");\n  const cellCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const prevCellCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [cellCtrls, setCellCtrls] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => ctrl.getHeaderCtrls());\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      return;\n    }\n    const compProxy = {\n      setHeight: (height2) => setHeight(height2),\n      setTop: (top2) => setTop(top2),\n      setHeaderCtrls: (ctrls, forceOrder, afterScroll) => {\n        prevCellCtrlsRef.current = cellCtrlsRef.current;\n        cellCtrlsRef.current = ctrls;\n        const next = getNextValueIfDifferent(prevCellCtrlsRef.current, ctrls, forceOrder);\n        if (next !== prevCellCtrlsRef.current) {\n          agFlushSync(afterScroll, () => setCellCtrls(next));\n        }\n      },\n      setWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n        }\n      }\n    };\n    ctrl.setComp(compProxy, compBean.current, false);\n  }, []);\n  const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height,\n      top\n    }),\n    [height, top]\n  );\n  const createCellJsx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((cellCtrl) => {\n    switch (ctrl.getType()) {\n      case ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.HeaderRowType.COLUMN_GROUP:\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerGroupCellComp_default, { ctrl: cellCtrl, key: cellCtrl.instanceId });\n      case ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.HeaderRowType.FLOATING_FILTER:\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerFilterCellComp_default, { ctrl: cellCtrl, key: cellCtrl.instanceId });\n      default:\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerCellComp_default, { ctrl: cellCtrl, key: cellCtrl.instanceId });\n    }\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className, role: \"row\", style, \"aria-rowindex\": ariaRowIndex }, cellCtrls.map(createCellJsx));\n};\nvar headerRowComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderRowComp);\n\n// packages/ag-grid-react/src/reactUi/header/headerRowContainerComp.tsx\nvar HeaderRowContainerComp = ({ pinned }) => {\n  const [displayed, setDisplayed] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n  const [headerRowCtrls, setHeaderRowCtrls] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eCenterContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const headerRowCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const pinnedLeft = pinned === \"left\";\n  const pinnedRight = pinned === \"right\";\n  const centre = !pinnedLeft && !pinnedRight;\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    headerRowCtrlRef.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.HeaderRowContainerCtrl(pinned)) : context.destroyBean(headerRowCtrlRef.current);\n    if (!eRef) {\n      return;\n    }\n    const compProxy = {\n      setDisplayed,\n      setCtrls: (ctrls) => setHeaderRowCtrls(ctrls),\n      // centre only\n      setCenterWidth: (width) => {\n        if (eCenterContainer.current) {\n          eCenterContainer.current.style.width = width;\n        }\n      },\n      setViewportScrollLeft: (left) => {\n        if (eGui.current) {\n          eGui.current.scrollLeft = left;\n        }\n      },\n      // pinned only\n      setPinnedContainerWidth: (width) => {\n        if (eGui.current) {\n          eGui.current.style.width = width;\n          eGui.current.style.minWidth = width;\n          eGui.current.style.maxWidth = width;\n        }\n      }\n    };\n    headerRowCtrlRef.current.setComp(compProxy, eGui.current);\n  }, []);\n  const className = !displayed ? \"ag-hidden\" : \"\";\n  const insertRowsJsx = () => headerRowCtrls.map((ctrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowComp_default, { ctrl, key: ctrl.instanceId }));\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, pinnedLeft && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: setRef2,\n      className: \"ag-pinned-left-header \" + className,\n      \"aria-hidden\": !displayed,\n      role: \"rowgroup\"\n    },\n    insertRowsJsx()\n  ), pinnedRight && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: setRef2,\n      className: \"ag-pinned-right-header \" + className,\n      \"aria-hidden\": !displayed,\n      role: \"rowgroup\"\n    },\n    insertRowsJsx()\n  ), centre && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: \"ag-header-viewport \" + className, role: \"presentation\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: eCenterContainer, className: \"ag-header-container\", role: \"rowgroup\" }, insertRowsJsx())));\n};\nvar headerRowContainerComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderRowContainerComp);\n\n// packages/ag-grid-react/src/reactUi/header/gridHeaderComp.tsx\nvar GridHeaderComp = () => {\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [height, setHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const gridCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    gridCtrlRef.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.GridHeaderCtrl()) : context.destroyBean(gridCtrlRef.current);\n    if (!eRef)\n      return;\n    const compProxy = {\n      addOrRemoveCssClass: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      setHeightAndMinHeight: (height2) => setHeight(height2)\n    };\n    gridCtrlRef.current.setComp(compProxy, eRef, eRef);\n  }, []);\n  const className = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = cssClasses.toString();\n    return \"ag-header \" + res;\n  }, [cssClasses]);\n  const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height,\n      minHeight: height\n    }),\n    [height]\n  );\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className, style, role: \"presentation\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowContainerComp_default, { pinned: \"left\" }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowContainerComp_default, { pinned: null }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(headerRowContainerComp_default, { pinned: \"right\" }));\n};\nvar gridHeaderComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GridHeaderComp);\n\n// packages/ag-grid-react/src/reactUi/reactComment.tsx\n\nvar useReactCommentEffect = (comment, eForCommentRef) => {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const eForComment = eForCommentRef.current;\n    if (eForComment) {\n      const eParent = eForComment.parentElement;\n      if (eParent) {\n        const eComment = document.createComment(comment);\n        eParent.insertBefore(eComment, eForComment);\n        return () => {\n          eParent.removeChild(eComment);\n        };\n      }\n    }\n  }, [comment]);\n};\nvar reactComment_default = useReactCommentEffect;\n\n// packages/ag-grid-react/src/reactUi/rows/rowContainerComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/rows/rowComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/cells/cellComp.tsx\n\n\n\n// packages/ag-grid-react/src/shared/customComp/cellEditorComponentProxy.ts\n\nvar CellEditorComponentProxy = class {\n  constructor(cellEditorParams, refreshProps) {\n    this.cellEditorParams = cellEditorParams;\n    this.refreshProps = refreshProps;\n    this.instanceCreated = new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.AgPromise((resolve) => {\n      this.resolveInstanceCreated = resolve;\n    });\n    this.onValueChange = (value) => this.updateValue(value);\n    this.value = cellEditorParams.value;\n  }\n  getProps() {\n    return {\n      ...this.cellEditorParams,\n      initialValue: this.cellEditorParams.value,\n      value: this.value,\n      onValueChange: this.onValueChange\n    };\n  }\n  getValue() {\n    return this.value;\n  }\n  refresh(params) {\n    this.cellEditorParams = params;\n    this.refreshProps();\n  }\n  setMethods(methods) {\n    addOptionalMethods(this.getOptionalMethods(), methods, this);\n  }\n  getInstance() {\n    return this.instanceCreated.then(() => this.componentInstance);\n  }\n  setRef(componentInstance) {\n    this.componentInstance = componentInstance;\n    this.resolveInstanceCreated?.();\n    this.resolveInstanceCreated = void 0;\n  }\n  getOptionalMethods() {\n    return [\"isCancelBeforeStart\", \"isCancelAfterEnd\", \"focusIn\", \"focusOut\", \"afterGuiAttached\"];\n  }\n  updateValue(value) {\n    this.value = value;\n    this.refreshProps();\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/cells/popupEditorComp.tsx\n\n\n\n// packages/ag-grid-react/src/reactUi/useEffectOnce.tsx\n\nvar useEffectOnce = (effect) => {\n  const effectFn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(effect);\n  const destroyFn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const effectCalled = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const rendered = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const [, setVal] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  if (effectCalled.current) {\n    rendered.current = true;\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!effectCalled.current) {\n      destroyFn.current = effectFn.current();\n      effectCalled.current = true;\n    }\n    setVal((val) => val + 1);\n    return () => {\n      if (!rendered.current) {\n        return;\n      }\n      destroyFn.current?.();\n    };\n  }, []);\n};\n\n// packages/ag-grid-react/src/reactUi/cells/popupEditorComp.tsx\nvar PopupEditorComp = (props) => {\n  const [popupEditorWrapper, setPopupEditorWrapper] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const { context, popupService, localeService, gos, editService } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  useEffectOnce(() => {\n    const { editDetails, cellCtrl, eParentCell } = props;\n    const { compDetails } = editDetails;\n    const useModelPopup = gos.get(\"stopEditingWhenCellsLoseFocus\");\n    const wrapper = context.createBean(editService.createPopupEditorWrapper(compDetails.params));\n    const ePopupGui = wrapper.getGui();\n    if (props.jsChildComp) {\n      const eChildGui = props.jsChildComp.getGui();\n      if (eChildGui) {\n        ePopupGui.appendChild(eChildGui);\n      }\n    }\n    const positionParams = {\n      column: cellCtrl.getColumn(),\n      rowNode: cellCtrl.getRowNode(),\n      type: \"popupCellEditor\",\n      eventSource: eParentCell,\n      ePopup: ePopupGui,\n      position: editDetails.popupPosition,\n      keepWithinBounds: true\n    };\n    const positionCallback = popupService.positionPopupByComponent.bind(popupService, positionParams);\n    const translate = localeService.getLocaleTextFunc();\n    const addPopupRes = popupService.addPopup({\n      modal: useModelPopup,\n      eChild: ePopupGui,\n      closeOnEsc: true,\n      closedCallback: () => {\n        cellCtrl.onPopupEditorClosed();\n      },\n      anchorToElement: eParentCell,\n      positionCallback,\n      ariaLabel: translate(\"ariaLabelCellEditor\", \"Cell Editor\")\n    });\n    const hideEditorPopup = addPopupRes ? addPopupRes.hideFunc : void 0;\n    setPopupEditorWrapper(wrapper);\n    props.jsChildComp?.afterGuiAttached?.();\n    return () => {\n      hideEditorPopup?.();\n      context.destroyBean(wrapper);\n    };\n  });\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, popupEditorWrapper && props.wrappedContent && (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(props.wrappedContent, popupEditorWrapper.getGui()));\n};\nvar popupEditorComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(PopupEditorComp);\n\n// packages/ag-grid-react/src/reactUi/cells/showJsRenderer.tsx\n\nvar useJsCellRenderer = (showDetails, showTools, eCellValue, cellValueVersion, jsCellRendererRef, eGui) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const destroyCellRenderer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const comp = jsCellRendererRef.current;\n    if (!comp) {\n      return;\n    }\n    const compGui = comp.getGui();\n    if (compGui && compGui.parentElement) {\n      compGui.parentElement.removeChild(compGui);\n    }\n    context.destroyBean(comp);\n    jsCellRendererRef.current = void 0;\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const showValue = showDetails != null;\n    const jsCompDetails = showDetails?.compDetails && !showDetails.compDetails.componentFromFramework;\n    const waitingForToolsSetup = showTools && eCellValue == null;\n    const showComp = showValue && jsCompDetails && !waitingForToolsSetup;\n    if (!showComp) {\n      destroyCellRenderer();\n      return;\n    }\n    const compDetails = showDetails.compDetails;\n    if (jsCellRendererRef.current) {\n      const comp = jsCellRendererRef.current;\n      const attemptRefresh = comp.refresh != null && showDetails.force == false;\n      const refreshResult = attemptRefresh ? comp.refresh(compDetails.params) : false;\n      const refreshWorked = refreshResult === true || refreshResult === void 0;\n      if (refreshWorked) {\n        return;\n      }\n      destroyCellRenderer();\n    }\n    const promise = compDetails.newAgStackInstance();\n    if (promise == null) {\n      return;\n    }\n    promise.then((comp) => {\n      if (!comp) {\n        return;\n      }\n      const compGui = comp.getGui();\n      if (!compGui) {\n        return;\n      }\n      const parent = showTools ? eCellValue : eGui.current;\n      parent.appendChild(compGui);\n      jsCellRendererRef.current = comp;\n    });\n  }, [showDetails, showTools, cellValueVersion]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return destroyCellRenderer;\n  }, []);\n};\nvar showJsRenderer_default = useJsCellRenderer;\n\n// packages/ag-grid-react/src/reactUi/cells/cellComp.tsx\nvar jsxEditorProxy = (editDetails, CellEditorClass, setRef2) => {\n  const { compProxy } = editDetails;\n  setRef2(compProxy);\n  const props = compProxy.getProps();\n  const isStateless = isComponentStateless(CellEditorClass);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    CustomContext.Provider,\n    {\n      value: {\n        setMethods: (methods) => compProxy.setMethods(methods)\n      }\n    },\n    isStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellEditorClass, { ...props }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellEditorClass, { ...props, ref: (ref) => compProxy.setRef(ref) })\n  );\n};\nvar jsxEditor = (editDetails, CellEditorClass, setRef2) => {\n  const newFormat = editDetails.compProxy;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, newFormat ? jsxEditorProxy(editDetails, CellEditorClass, setRef2) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellEditorClass, { ...editDetails.compDetails.params, ref: setRef2 }));\n};\nvar jsxEditValue = (editDetails, setCellEditorRef, eGui, cellCtrl, jsEditorComp) => {\n  const compDetails = editDetails.compDetails;\n  const CellEditorClass = compDetails.componentClass;\n  const reactInlineEditor = compDetails.componentFromFramework && !editDetails.popup;\n  const reactPopupEditor = compDetails.componentFromFramework && editDetails.popup;\n  const jsPopupEditor = !compDetails.componentFromFramework && editDetails.popup;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, reactInlineEditor && jsxEditor(editDetails, CellEditorClass, setCellEditorRef), reactPopupEditor && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    popupEditorComp_default,\n    {\n      editDetails,\n      cellCtrl,\n      eParentCell: eGui,\n      wrappedContent: jsxEditor(editDetails, CellEditorClass, setCellEditorRef)\n    }\n  ), jsPopupEditor && jsEditorComp && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    popupEditorComp_default,\n    {\n      editDetails,\n      cellCtrl,\n      eParentCell: eGui,\n      jsChildComp: jsEditorComp\n    }\n  ));\n};\nvar jsxShowValue = (showDetails, key, parentId, cellRendererRef, showCellWrapper, reactCellRendererStateless, setECellValue) => {\n  const { compDetails, value } = showDetails;\n  const noCellRenderer = !compDetails;\n  const reactCellRenderer = compDetails && compDetails.componentFromFramework;\n  const CellRendererClass = compDetails && compDetails.componentClass;\n  const valueForNoCellRenderer = value?.toString ? value.toString() : value;\n  const bodyJsxFunc = () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, noCellRenderer && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, valueForNoCellRenderer), reactCellRenderer && !reactCellRendererStateless && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellRendererClass, { ...compDetails.params, key, ref: cellRendererRef }), reactCellRenderer && reactCellRendererStateless && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CellRendererClass, { ...compDetails.params, key }));\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, showCellWrapper ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", { role: \"presentation\", id: `cell-${parentId}`, className: \"ag-cell-value\", ref: setECellValue }, bodyJsxFunc()) : bodyJsxFunc());\n};\nvar CellComp = ({\n  cellCtrl,\n  printLayout,\n  editingRow\n}) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const { colIdSanitised, instanceId } = cellCtrl;\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [renderDetails, setRenderDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => cellCtrl.isCellRenderer() ? void 0 : { compDetails: void 0, value: cellCtrl.getValueToDisplay(), force: false }\n  );\n  const [editDetails, setEditDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [renderKey, setRenderKey] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [includeSelection, setIncludeSelection] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [includeRowDrag, setIncludeRowDrag] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [includeDndSource, setIncludeDndSource] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [jsEditorComp, setJsEditorComp] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const forceWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cellCtrl.isForceWrapper(), [cellCtrl]);\n  const cellAriaRole = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cellCtrl.getCellAriaRole(), [cellCtrl]);\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const cellRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const jsCellRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const cellEditorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eCellWrapper = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const cellWrapperDestroyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const eCellValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [cellValueVersion, setCellValueVersion] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const setCellValueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    eCellValue.current = ref;\n    setCellValueVersion((v) => v + 1);\n  }, []);\n  const showTools = renderDetails != null && (includeSelection || includeDndSource || includeRowDrag);\n  const showCellWrapper = forceWrapper || showTools;\n  const setCellEditorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (cellEditor) => {\n      cellEditorRef.current = cellEditor;\n      if (cellEditor) {\n        const editingCancelledByUserComp = cellEditor.isCancelBeforeStart && cellEditor.isCancelBeforeStart();\n        setTimeout(() => {\n          if (editingCancelledByUserComp) {\n            cellCtrl.stopEditing(true);\n            cellCtrl.focusCell(true);\n          } else {\n            cellCtrl.cellEditorAttached();\n          }\n        });\n      }\n    },\n    [cellCtrl]\n  );\n  const cssClassManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (!cssClassManager.current) {\n    cssClassManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.CssClassManager(() => eGui.current);\n  }\n  showJsRenderer_default(renderDetails, showCellWrapper, eCellValue.current, cellValueVersion, jsCellRendererRef, eGui);\n  const lastRenderDetails = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    const oldDetails = lastRenderDetails.current;\n    const newDetails = renderDetails;\n    lastRenderDetails.current = renderDetails;\n    if (oldDetails == null || oldDetails.compDetails == null || newDetails == null || newDetails.compDetails == null) {\n      return;\n    }\n    const oldCompDetails = oldDetails.compDetails;\n    const newCompDetails = newDetails.compDetails;\n    if (oldCompDetails.componentClass != newCompDetails.componentClass) {\n      return;\n    }\n    if (cellRendererRef.current?.refresh == null) {\n      return;\n    }\n    const result = cellRendererRef.current.refresh(newCompDetails.params);\n    if (result != true) {\n      setRenderKey((prev) => prev + 1);\n    }\n  }, [renderDetails]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    const doingJsEditor = editDetails && !editDetails.compDetails.componentFromFramework;\n    if (!doingJsEditor) {\n      return;\n    }\n    const compDetails = editDetails.compDetails;\n    const isPopup = editDetails.popup === true;\n    const cellEditorPromise = compDetails.newAgStackInstance();\n    cellEditorPromise.then((cellEditor) => {\n      if (!cellEditor) {\n        return;\n      }\n      const compGui = cellEditor.getGui();\n      setCellEditorRef(cellEditor);\n      if (!isPopup) {\n        const parentEl = (forceWrapper ? eCellWrapper : eGui).current;\n        parentEl?.appendChild(compGui);\n        cellEditor.afterGuiAttached && cellEditor.afterGuiAttached();\n      }\n      setJsEditorComp(cellEditor);\n    });\n    return () => {\n      cellEditorPromise.then((cellEditor) => {\n        const compGui = cellEditor.getGui();\n        context.destroyBean(cellEditor);\n        setCellEditorRef(void 0);\n        setJsEditorComp(void 0);\n        compGui?.parentElement?.removeChild(compGui);\n      });\n    };\n  }, [editDetails]);\n  const setCellWrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (eRef) => {\n      eCellWrapper.current = eRef;\n      if (!eRef) {\n        cellWrapperDestroyFuncs.current.forEach((f) => f());\n        cellWrapperDestroyFuncs.current = [];\n        return;\n      }\n      const addComp = (comp) => {\n        if (comp) {\n          const eGui2 = comp.getGui();\n          eRef.insertAdjacentElement(\"afterbegin\", eGui2);\n          cellWrapperDestroyFuncs.current.push(() => {\n            context.destroyBean(comp);\n            (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._removeFromParent)(eGui2);\n          });\n        }\n        return comp;\n      };\n      if (includeSelection) {\n        const checkboxSelectionComp = cellCtrl.createSelectionCheckbox();\n        addComp(checkboxSelectionComp);\n      }\n      if (includeDndSource) {\n        addComp(cellCtrl.createDndSource());\n      }\n      if (includeRowDrag) {\n        addComp(cellCtrl.createRowDragComp());\n      }\n    },\n    [cellCtrl, context, includeDndSource, includeRowDrag, includeSelection]\n  );\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef || !cellCtrl) {\n      return;\n    }\n    const compProxy = {\n      addOrRemoveCssClass: (name, on) => cssClassManager.current.addOrRemoveCssClass(name, on),\n      setUserStyles: (styles) => setUserStyles(styles),\n      getFocusableElement: () => eGui.current,\n      setIncludeSelection: (include) => setIncludeSelection(include),\n      setIncludeRowDrag: (include) => setIncludeRowDrag(include),\n      setIncludeDndSource: (include) => setIncludeDndSource(include),\n      getCellEditor: () => cellEditorRef.current || null,\n      getCellRenderer: () => cellRendererRef.current ?? jsCellRendererRef.current,\n      getParentOfValue: () => eCellValue.current ?? eCellWrapper.current ?? eGui.current,\n      setRenderDetails: (compDetails, value, force) => {\n        setRenderDetails((prev) => {\n          if (prev?.compDetails !== compDetails || prev?.value !== value || prev?.force !== force) {\n            return {\n              value,\n              compDetails,\n              force\n            };\n          } else {\n            return prev;\n          }\n        });\n      },\n      setEditDetails: (compDetails, popup, popupPosition, reactiveCustomComponents) => {\n        if (compDetails) {\n          let compProxy2 = void 0;\n          if (reactiveCustomComponents) {\n            compProxy2 = new CellEditorComponentProxy(\n              compDetails.params,\n              () => setRenderKey((prev) => prev + 1)\n            );\n          } else if (compDetails.componentFromFramework) {\n            warnReactiveCustomComponents();\n          }\n          setEditDetails({\n            compDetails,\n            popup,\n            popupPosition,\n            compProxy: compProxy2\n          });\n          if (!popup) {\n            setRenderDetails(void 0);\n          }\n        } else {\n          setEditDetails((editDetails2) => {\n            if (editDetails2?.compProxy) {\n              cellEditorRef.current = void 0;\n            }\n            return void 0;\n          });\n        }\n      }\n    };\n    const cellWrapperOrUndefined = eCellWrapper.current || void 0;\n    cellCtrl.setComp(compProxy, eRef, cellWrapperOrUndefined, printLayout, editingRow, compBean.current);\n  }, []);\n  const reactCellRendererStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = renderDetails?.compDetails?.componentFromFramework && isComponentStateless(renderDetails.compDetails.componentClass);\n    return !!res;\n  }, [renderDetails]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (!eGui.current) {\n      return;\n    }\n    cssClassManager.current.addOrRemoveCssClass(\"ag-cell-value\", !showCellWrapper);\n    cssClassManager.current.addOrRemoveCssClass(\"ag-cell-inline-editing\", !!editDetails && !editDetails.popup);\n    cssClassManager.current.addOrRemoveCssClass(\"ag-cell-popup-editing\", !!editDetails && !!editDetails.popup);\n    cssClassManager.current.addOrRemoveCssClass(\"ag-cell-not-inline-editing\", !editDetails || !!editDetails.popup);\n    cellCtrl.getRowCtrl()?.setInlineEditingCss();\n    if (cellCtrl.shouldRestoreFocus() && !cellCtrl.isEditing()) {\n      eGui.current.focus({ preventScroll: true });\n    }\n  });\n  const showContents = () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, renderDetails != null && jsxShowValue(\n    renderDetails,\n    renderKey,\n    instanceId,\n    cellRendererRef,\n    showCellWrapper,\n    reactCellRendererStateless,\n    setCellValueRef\n  ), editDetails != null && jsxEditValue(editDetails, setCellEditorRef, eGui.current, cellCtrl, jsEditorComp));\n  const onBlur = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => cellCtrl.onFocusOut(), []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, style: userStyles, role: cellAriaRole, \"col-id\": colIdSanitised, onBlur }, showCellWrapper ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: \"ag-cell-wrapper\", role: \"presentation\", ref: setCellWrapperRef }, showContents()) : showContents());\n};\nvar cellComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(CellComp);\n\n// packages/ag-grid-react/src/reactUi/rows/rowComp.tsx\nvar RowComp = ({ rowCtrl, containerType }) => {\n  const { context, gos } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const compBean = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const domOrderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(rowCtrl.getDomOrder());\n  const isFullWidth = rowCtrl.isFullWidth();\n  const isDisplayed = rowCtrl.getRowNode().displayed;\n  const [rowIndex, setRowIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => isDisplayed ? rowCtrl.getRowIndex() : null);\n  const [rowId, setRowId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowCtrl.getRowId());\n  const [rowBusinessKey, setRowBusinessKey] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowCtrl.getBusinessKey());\n  const [userStyles, setUserStyles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => rowCtrl.getRowStyles());\n  const cellCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const prevCellCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [cellCtrls, setCellCtrls] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => null);\n  const [fullWidthCompDetails, setFullWidthCompDetails] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [top, setTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => isDisplayed ? rowCtrl.getInitialRowTop(containerType) : void 0\n  );\n  const [transform, setTransform] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    () => isDisplayed ? rowCtrl.getInitialTransform(containerType) : void 0\n  );\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const fullWidthCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const autoHeightSetup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const [autoHeightSetupAttempt, setAutoHeightSetupAttempt] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (autoHeightSetup.current || !fullWidthCompDetails || autoHeightSetupAttempt > 10) {\n      return;\n    }\n    const eChild = eGui.current?.firstChild;\n    if (eChild) {\n      rowCtrl.setupDetailRowAutoHeight(eChild);\n      autoHeightSetup.current = true;\n    } else {\n      setAutoHeightSetupAttempt((prev) => prev + 1);\n    }\n  }, [fullWidthCompDetails, autoHeightSetupAttempt]);\n  const cssClassManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (!cssClassManager.current) {\n    cssClassManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.CssClassManager(() => eGui.current);\n  }\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    compBean.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._EmptyBean()) : context.destroyBean(compBean.current);\n    if (!eRef) {\n      rowCtrl.unsetComp(containerType);\n      return;\n    }\n    if (!rowCtrl.isAlive()) {\n      return;\n    }\n    const compProxy = {\n      // the rowTop is managed by state, instead of direct style manipulation by rowCtrl (like all the other styles)\n      // as we need to have an initial value when it's placed into he DOM for the first time, for animation to work.\n      setTop,\n      setTransform,\n      // i found using React for managing classes at the row level was to slow, as modifying classes caused a lot of\n      // React code to execute, so avoiding React for managing CSS Classes made the grid go much faster.\n      addOrRemoveCssClass: (name, on) => cssClassManager.current.addOrRemoveCssClass(name, on),\n      setDomOrder: (domOrder) => domOrderRef.current = domOrder,\n      setRowIndex,\n      setRowId,\n      setRowBusinessKey,\n      setUserStyles,\n      // if we don't maintain the order, then cols will be ripped out and into the dom\n      // when cols reordered, which would stop the CSS transitions from working\n      setCellCtrls: (next, useFlushSync) => {\n        prevCellCtrlsRef.current = cellCtrlsRef.current;\n        const nextCells = getNextValueIfDifferent(prevCellCtrlsRef.current, next, domOrderRef.current);\n        if (nextCells !== prevCellCtrlsRef.current) {\n          cellCtrlsRef.current = nextCells;\n          agFlushSync(useFlushSync, () => setCellCtrls(nextCells));\n        }\n      },\n      showFullWidth: (compDetails) => setFullWidthCompDetails(compDetails),\n      getFullWidthCellRenderer: () => fullWidthCompRef.current,\n      refreshFullWidth: (getUpdatedParams) => {\n        if (canRefreshFullWidthRef.current) {\n          setFullWidthCompDetails((prevFullWidthCompDetails) => ({\n            ...prevFullWidthCompDetails,\n            params: getUpdatedParams()\n          }));\n          return true;\n        } else {\n          if (!fullWidthCompRef.current || !fullWidthCompRef.current.refresh) {\n            return false;\n          }\n          return fullWidthCompRef.current.refresh(getUpdatedParams());\n        }\n      }\n    };\n    rowCtrl.setComp(compProxy, eRef, containerType, compBean.current);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(\n    () => showJsComp(fullWidthCompDetails, context, eGui.current, fullWidthCompRef),\n    [fullWidthCompDetails]\n  );\n  const rowStyles = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = { top, transform };\n    Object.assign(res, userStyles);\n    return res;\n  }, [top, transform, userStyles]);\n  const showFullWidthFramework = isFullWidth && fullWidthCompDetails?.componentFromFramework;\n  const showCells = !isFullWidth && cellCtrls != null;\n  const reactFullWidthCellRendererStateless = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const res = fullWidthCompDetails?.componentFromFramework && isComponentStateless(fullWidthCompDetails.componentClass);\n    return !!res;\n  }, [fullWidthCompDetails]);\n  const canRefreshFullWidthRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    canRefreshFullWidthRef.current = reactFullWidthCellRendererStateless && !!fullWidthCompDetails && !!gos.get(\"reactiveCustomComponents\");\n  }, [reactFullWidthCellRendererStateless, fullWidthCompDetails]);\n  const showCellsJsx = () => cellCtrls?.map((cellCtrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    cellComp_default,\n    {\n      cellCtrl,\n      editingRow: rowCtrl.isEditing(),\n      printLayout: rowCtrl.isPrintLayout(),\n      key: cellCtrl.instanceId\n    }\n  ));\n  const showFullWidthFrameworkJsx = () => {\n    const FullWidthComp = fullWidthCompDetails.componentClass;\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, reactFullWidthCellRendererStateless ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FullWidthComp, { ...fullWidthCompDetails.params }) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(FullWidthComp, { ...fullWidthCompDetails.params, ref: fullWidthCompRef }));\n  };\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      ref: setRef2,\n      role: \"row\",\n      style: rowStyles,\n      \"row-index\": rowIndex,\n      \"row-id\": rowId,\n      \"row-business-key\": rowBusinessKey\n    },\n    showCells && showCellsJsx(),\n    showFullWidthFramework && showFullWidthFrameworkJsx()\n  );\n};\nvar rowComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(RowComp);\n\n// packages/ag-grid-react/src/reactUi/rows/rowContainerComp.tsx\nvar RowContainerComp = ({ name }) => {\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const containerOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._getRowContainerOptions)(name), [name]);\n  const eViewport = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const rowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const prevRowCtrlsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const [rowCtrlsOrdered, setRowCtrlsOrdered] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => []);\n  const domOrderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const rowContainerCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const viewportClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(containerOptions.viewport), [containerOptions]);\n  const containerClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(containerOptions.container), [containerOptions]);\n  const isCenter = containerOptions.type === \"center\";\n  const topLevelRef = isCenter ? eViewport : eContainer;\n  reactComment_default(\" AG Row Container \" + name + \" \", topLevelRef);\n  const areElementsReady = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (isCenter) {\n      return eViewport.current != null && eContainer.current != null;\n    }\n    return eContainer.current != null;\n  }, []);\n  const areElementsRemoved = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (isCenter) {\n      return eViewport.current == null && eContainer.current == null;\n    }\n    return eContainer.current == null;\n  }, []);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (areElementsRemoved()) {\n      rowContainerCtrlRef.current = context.destroyBean(rowContainerCtrlRef.current);\n    }\n    if (areElementsReady()) {\n      const updateRowCtrlsOrdered = (useFlushSync) => {\n        const next = getNextValueIfDifferent(\n          prevRowCtrlsRef.current,\n          rowCtrlsRef.current,\n          domOrderRef.current\n        );\n        if (next !== prevRowCtrlsRef.current) {\n          prevRowCtrlsRef.current = next;\n          agFlushSync(useFlushSync, () => setRowCtrlsOrdered(next));\n        }\n      };\n      const compProxy = {\n        setHorizontalScroll: (offset) => {\n          if (eViewport.current) {\n            eViewport.current.scrollLeft = offset;\n          }\n        },\n        setViewportHeight: (height) => {\n          if (eViewport.current) {\n            eViewport.current.style.height = height;\n          }\n        },\n        setRowCtrls: ({ rowCtrls, useFlushSync }) => {\n          const useFlush = !!useFlushSync && rowCtrlsRef.current.length > 0 && rowCtrls.length > 0;\n          rowCtrlsRef.current = rowCtrls;\n          updateRowCtrlsOrdered(useFlush);\n        },\n        setDomOrder: (domOrder) => {\n          if (domOrderRef.current != domOrder) {\n            domOrderRef.current = domOrder;\n            updateRowCtrlsOrdered(false);\n          }\n        },\n        setContainerWidth: (width) => {\n          if (eContainer.current) {\n            eContainer.current.style.width = width;\n          }\n        },\n        setOffsetTop: (offset) => {\n          if (eContainer.current) {\n            eContainer.current.style.transform = `translateY(${offset})`;\n          }\n        }\n      };\n      rowContainerCtrlRef.current = context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.RowContainerCtrl(name));\n      rowContainerCtrlRef.current.setComp(compProxy, eContainer.current, eViewport.current);\n    }\n  }, [areElementsReady, areElementsRemoved]);\n  const setContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      eContainer.current = e;\n      setRef2();\n    },\n    [setRef2]\n  );\n  const setViewportRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      eViewport.current = e;\n      setRef2();\n    },\n    [setRef2]\n  );\n  const buildContainer = () => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: containerClasses, ref: setContainerRef, role: \"rowgroup\" }, rowCtrlsOrdered.map((rowCtrl) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(rowComp_default, { rowCtrl, containerType: containerOptions.type, key: rowCtrl.instanceId })));\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, isCenter ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: viewportClasses, ref: setViewportRef, role: \"presentation\" }, buildContainer()) : buildContainer());\n};\nvar rowContainerComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(RowContainerComp);\n\n// packages/ag-grid-react/src/reactUi/gridBodyComp.tsx\nvar GridBodyComp = () => {\n  const { context, resizeObserverService } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const [rowAnimationClass, setRowAnimationClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [topHeight, setTopHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [bottomHeight, setBottomHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [stickyTopHeight, setStickyTopHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyTopTop, setStickyTopTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyTopWidth, setStickyTopWidth] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"100%\");\n  const [stickyBottomHeight, setStickyBottomHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyBottomBottom, setStickyBottomBottom] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"0px\");\n  const [stickyBottomWidth, setStickyBottomWidth] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"100%\");\n  const [topDisplay, setTopDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [bottomDisplay, setBottomDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [forceVerticalScrollClass, setForceVerticalScrollClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [topAndBottomOverflowY, setTopAndBottomOverflowY] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [cellSelectableCss, setCellSelectableCss] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [layoutClass, setLayoutClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"ag-layout-normal\");\n  const cssClassManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (!cssClassManager.current) {\n    cssClassManager.current = new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.CssClassManager(() => eRoot.current);\n  }\n  const eRoot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eTop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eStickyTop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eStickyBottom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eBody = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eBodyViewport = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const eBottom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const beansToDestroy = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const destroyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  reactComment_default(\" AG Grid Body \", eRoot);\n  reactComment_default(\" AG Pinned Top \", eTop);\n  reactComment_default(\" AG Sticky Top \", eStickyTop);\n  reactComment_default(\" AG Middle \", eBodyViewport);\n  reactComment_default(\" AG Pinned Bottom \", eBottom);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eRoot.current = eRef;\n    if (!eRef) {\n      beansToDestroy.current = context.destroyBeans(beansToDestroy.current);\n      destroyFuncs.current.forEach((f) => f());\n      destroyFuncs.current = [];\n      return;\n    }\n    if (!context) {\n      return;\n    }\n    const attachToDom = (eParent, eChild) => {\n      eParent.appendChild(eChild);\n      destroyFuncs.current.push(() => eParent.removeChild(eChild));\n    };\n    const newComp = (compClass) => {\n      const comp = context.createBean(new compClass());\n      beansToDestroy.current.push(comp);\n      return comp;\n    };\n    const addComp = (eParent, compClass, comment) => {\n      attachToDom(eParent, document.createComment(comment));\n      attachToDom(eParent, newComp(compClass).getGui());\n    };\n    addComp(eRef, ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.FakeHScrollComp, \" AG Fake Horizontal Scroll \");\n    addComp(eRef, ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.OverlayWrapperComponent, \" AG Overlay Wrapper \");\n    if (eBody.current) {\n      addComp(eBody.current, ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.FakeVScrollComp, \" AG Fake Vertical Scroll \");\n    }\n    const compProxy = {\n      setRowAnimationCssOnBodyViewport: setRowAnimationClass,\n      setColumnCount: (count) => {\n        if (eRoot.current) {\n          (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._setAriaColCount)(eRoot.current, count);\n        }\n      },\n      setRowCount: (count) => {\n        if (eRoot.current) {\n          (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._setAriaRowCount)(eRoot.current, count);\n        }\n      },\n      setTopHeight,\n      setBottomHeight,\n      setStickyTopHeight,\n      setStickyTopTop,\n      setStickyTopWidth,\n      setTopDisplay,\n      setBottomDisplay,\n      setColumnMovingCss: (cssClass, flag) => cssClassManager.current.addOrRemoveCssClass(cssClass, flag),\n      updateLayoutClasses: setLayoutClass,\n      setAlwaysVerticalScrollClass: setForceVerticalScrollClass,\n      setPinnedTopBottomOverflowY: setTopAndBottomOverflowY,\n      setCellSelectableCss: (cssClass, flag) => setCellSelectableCss(flag ? cssClass : null),\n      setBodyViewportWidth: (width) => {\n        if (eBodyViewport.current) {\n          eBodyViewport.current.style.width = width;\n        }\n      },\n      registerBodyViewportResizeListener: (listener) => {\n        if (eBodyViewport.current) {\n          const unsubscribeFromResize = resizeObserverService.observeResize(eBodyViewport.current, listener);\n          destroyFuncs.current.push(() => unsubscribeFromResize());\n        }\n      },\n      setStickyBottomHeight,\n      setStickyBottomBottom,\n      setStickyBottomWidth\n    };\n    const ctrl = context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.GridBodyCtrl());\n    beansToDestroy.current.push(ctrl);\n    ctrl.setComp(\n      compProxy,\n      eRef,\n      eBodyViewport.current,\n      eTop.current,\n      eBottom.current,\n      eStickyTop.current,\n      eStickyBottom.current\n    );\n  }, []);\n  const rootClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-root\", \"ag-unselectable\", layoutClass), [layoutClass]);\n  const bodyViewportClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\n      \"ag-body-viewport\",\n      rowAnimationClass,\n      layoutClass,\n      forceVerticalScrollClass,\n      cellSelectableCss\n    ),\n    [rowAnimationClass, layoutClass, forceVerticalScrollClass, cellSelectableCss]\n  );\n  const bodyClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-body\", layoutClass), [layoutClass]);\n  const topClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-floating-top\", cellSelectableCss), [cellSelectableCss]);\n  const stickyTopClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-sticky-top\", cellSelectableCss), [cellSelectableCss]);\n  const stickyBottomClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-sticky-bottom\", stickyBottomHeight === \"0px\" ? \"ag-hidden\" : null, cellSelectableCss),\n    [cellSelectableCss, stickyBottomHeight]\n  );\n  const bottomClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => classesList(\"ag-floating-bottom\", cellSelectableCss), [cellSelectableCss]);\n  const topStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: topHeight,\n      minHeight: topHeight,\n      display: topDisplay,\n      overflowY: topAndBottomOverflowY\n    }),\n    [topHeight, topDisplay, topAndBottomOverflowY]\n  );\n  const stickyTopStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: stickyTopHeight,\n      top: stickyTopTop,\n      width: stickyTopWidth\n    }),\n    [stickyTopHeight, stickyTopTop, stickyTopWidth]\n  );\n  const stickyBottomStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: stickyBottomHeight,\n      bottom: stickyBottomBottom,\n      width: stickyBottomWidth\n    }),\n    [stickyBottomHeight, stickyBottomBottom, stickyBottomWidth]\n  );\n  const bottomStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      height: bottomHeight,\n      minHeight: bottomHeight,\n      display: bottomDisplay,\n      overflowY: topAndBottomOverflowY\n    }),\n    [bottomHeight, bottomDisplay, topAndBottomOverflowY]\n  );\n  const createRowContainer = (container) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(rowContainerComp_default, { name: container, key: `${container}-container` });\n  const createSection = ({\n    section,\n    children,\n    className,\n    style\n  }) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: section, className, role: \"presentation\", style }, children.map(createRowContainer));\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: rootClasses, role: \"treegrid\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(gridHeaderComp_default, null), createSection({\n    section: eTop,\n    className: topClasses,\n    style: topStyle,\n    children: [\"topLeft\", \"topCenter\", \"topRight\", \"topFullWidth\"]\n  }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: bodyClasses, ref: eBody, role: \"presentation\" }, createSection({\n    section: eBodyViewport,\n    className: bodyViewportClasses,\n    children: [\"left\", \"center\", \"right\", \"fullWidth\"]\n  })), createSection({\n    section: eStickyTop,\n    className: stickyTopClasses,\n    style: stickyTopStyle,\n    children: [\"stickyTopLeft\", \"stickyTopCenter\", \"stickyTopRight\", \"stickyTopFullWidth\"]\n  }), createSection({\n    section: eStickyBottom,\n    className: stickyBottomClasses,\n    style: stickyBottomStyle,\n    children: [\"stickyBottomLeft\", \"stickyBottomCenter\", \"stickyBottomRight\", \"stickyBottomFullWidth\"]\n  }), createSection({\n    section: eBottom,\n    className: bottomClasses,\n    style: bottomStyle,\n    children: [\"bottomLeft\", \"bottomCenter\", \"bottomRight\", \"bottomFullWidth\"]\n  }));\n};\nvar gridBodyComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GridBodyComp);\n\n// packages/ag-grid-react/src/reactUi/tabGuardComp.tsx\n\n\nvar TabGuardCompRef = (props, forwardRef4) => {\n  const { children, eFocusableElement, onTabKeyDown, gridCtrl, forceFocusOutWhenTabGuardsAreEmpty } = props;\n  const { context } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const topTabGuardRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const bottomTabGuardRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const tabGuardCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const setTabIndex = (value) => {\n    const processedValue = value == null ? void 0 : parseInt(value, 10).toString();\n    [topTabGuardRef, bottomTabGuardRef].forEach((tabGuard) => {\n      if (processedValue === void 0) {\n        tabGuard.current?.removeAttribute(\"tabindex\");\n      } else {\n        tabGuard.current?.setAttribute(\"tabindex\", processedValue);\n      }\n    });\n  };\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(forwardRef4, () => ({\n    forceFocusOutOfContainer(up) {\n      tabGuardCtrlRef.current?.forceFocusOutOfContainer(up);\n    }\n  }));\n  const setupCtrl = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const topTabGuard = topTabGuardRef.current;\n    const bottomTabGuard = bottomTabGuardRef.current;\n    if (!topTabGuard && !bottomTabGuard) {\n      tabGuardCtrlRef.current = context.destroyBean(tabGuardCtrlRef.current);\n      return;\n    }\n    if (topTabGuard && bottomTabGuard) {\n      const compProxy = {\n        setTabIndex\n      };\n      tabGuardCtrlRef.current = context.createBean(\n        new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.TabGuardCtrl({\n          comp: compProxy,\n          eTopGuard: topTabGuard,\n          eBottomGuard: bottomTabGuard,\n          eFocusableElement,\n          onTabKeyDown,\n          forceFocusOutWhenTabGuardsAreEmpty,\n          focusInnerElement: (fromBottom) => gridCtrl.focusInnerElement(fromBottom)\n        })\n      );\n    }\n  }, []);\n  const setTopRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      topTabGuardRef.current = e;\n      setupCtrl();\n    },\n    [setupCtrl]\n  );\n  const setBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      bottomTabGuardRef.current = e;\n      setupCtrl();\n    },\n    [setupCtrl]\n  );\n  const createTabGuard = (side) => {\n    const className = side === \"top\" ? ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.TabGuardClassNames.TAB_GUARD_TOP : ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.TabGuardClassNames.TAB_GUARD_BOTTOM;\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"div\",\n      {\n        className: `${ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.TabGuardClassNames.TAB_GUARD} ${className}`,\n        role: \"presentation\",\n        ref: side === \"top\" ? setTopRef : setBottomRef\n      }\n    );\n  };\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, createTabGuard(\"top\"), children, createTabGuard(\"bottom\"));\n};\nvar TabGuardComp = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(TabGuardCompRef);\nvar tabGuardComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(TabGuardComp);\n\n// packages/ag-grid-react/src/reactUi/gridComp.tsx\nvar GridComp = ({ context }) => {\n  const [rtlClass, setRtlClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [gridThemeClass, setGridThemeClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [layoutClass, setLayoutClass] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [cursor, setCursor] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [userSelect, setUserSelect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [initialised, setInitialised] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [tabGuardReady, setTabGuardReady] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const gridCtrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eRootWrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const tabGuardRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const [eGridBodyParent, setGridBodyParent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const focusInnerElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(() => void 0);\n  const paginationCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const focusableContainersRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const onTabKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => void 0, []);\n  const beans = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (context.isDestroyed()) {\n      return null;\n    }\n    return context.getBeans();\n  }, [context]);\n  reactComment_default(\" AG Grid \", eRootWrapperRef);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eRootWrapperRef.current = eRef;\n    gridCtrlRef.current = eRef ? context.createBean(new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.GridCtrl()) : context.destroyBean(gridCtrlRef.current);\n    if (!eRef || context.isDestroyed()) {\n      return;\n    }\n    const gridCtrl = gridCtrlRef.current;\n    focusInnerElementRef.current = gridCtrl.focusInnerElement.bind(gridCtrl);\n    const compProxy = {\n      destroyGridUi: () => {\n      },\n      // do nothing, as framework users destroy grid by removing the comp\n      setRtlClass,\n      setGridThemeClass,\n      forceFocusOutOfContainer: (up) => {\n        if (!up && paginationCompRef.current?.isDisplayed()) {\n          paginationCompRef.current.forceFocusOutOfContainer(up);\n          return;\n        }\n        tabGuardRef.current?.forceFocusOutOfContainer(up);\n      },\n      updateLayoutClasses: setLayoutClass,\n      getFocusableContainers: () => {\n        const comps = [];\n        const gridBodyCompEl = eRootWrapperRef.current?.querySelector(\".ag-root\");\n        if (gridBodyCompEl) {\n          comps.push({ getGui: () => gridBodyCompEl });\n        }\n        focusableContainersRef.current.forEach((comp) => {\n          if (comp.isDisplayed()) {\n            comps.push(comp);\n          }\n        });\n        return comps;\n      },\n      setCursor,\n      setUserSelect\n    };\n    gridCtrl.setComp(compProxy, eRef, eRef);\n    setInitialised(true);\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const gridCtrl = gridCtrlRef.current;\n    const eRootWrapper = eRootWrapperRef.current;\n    if (!tabGuardReady || !beans || !gridCtrl || !eGridBodyParent || !eRootWrapper) {\n      return;\n    }\n    const beansToDestroy = [];\n    const {\n      watermarkSelector,\n      paginationSelector,\n      sideBarSelector,\n      statusBarSelector,\n      gridHeaderDropZonesSelector\n    } = gridCtrl.getOptionalSelectors();\n    const additionalEls = [];\n    if (gridHeaderDropZonesSelector) {\n      const headerDropZonesComp = context.createBean(new gridHeaderDropZonesSelector.component());\n      const eGui = headerDropZonesComp.getGui();\n      eRootWrapper.insertAdjacentElement(\"afterbegin\", eGui);\n      additionalEls.push(eGui);\n      beansToDestroy.push(headerDropZonesComp);\n    }\n    if (sideBarSelector) {\n      const sideBarComp = context.createBean(new sideBarSelector.component());\n      const eGui = sideBarComp.getGui();\n      const bottomTabGuard = eGridBodyParent.querySelector(\".ag-tab-guard-bottom\");\n      if (bottomTabGuard) {\n        bottomTabGuard.insertAdjacentElement(\"beforebegin\", eGui);\n        additionalEls.push(eGui);\n      }\n      beansToDestroy.push(sideBarComp);\n      focusableContainersRef.current.push(sideBarComp);\n    }\n    const addComponentToDom = (component) => {\n      const comp = context.createBean(new component());\n      const eGui = comp.getGui();\n      eRootWrapper.insertAdjacentElement(\"beforeend\", eGui);\n      additionalEls.push(eGui);\n      beansToDestroy.push(comp);\n      return comp;\n    };\n    if (statusBarSelector) {\n      addComponentToDom(statusBarSelector.component);\n    }\n    if (paginationSelector) {\n      const paginationComp = addComponentToDom(paginationSelector.component);\n      paginationCompRef.current = paginationComp;\n      focusableContainersRef.current.push(paginationComp);\n    }\n    if (watermarkSelector) {\n      addComponentToDom(watermarkSelector.component);\n    }\n    return () => {\n      context.destroyBeans(beansToDestroy);\n      additionalEls.forEach((el) => {\n        el.parentElement?.removeChild(el);\n      });\n    };\n  }, [tabGuardReady, eGridBodyParent, beans]);\n  const rootWrapperClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-root-wrapper\", rtlClass, gridThemeClass, layoutClass),\n    [rtlClass, gridThemeClass, layoutClass]\n  );\n  const rootWrapperBodyClasses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => classesList(\"ag-root-wrapper-body\", \"ag-focus-managed\", layoutClass),\n    [layoutClass]\n  );\n  const topStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      userSelect: userSelect != null ? userSelect : \"\",\n      WebkitUserSelect: userSelect != null ? userSelect : \"\",\n      cursor: cursor != null ? cursor : \"\"\n    }),\n    [userSelect, cursor]\n  );\n  const setTabGuardCompRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((ref) => {\n    tabGuardRef.current = ref;\n    setTabGuardReady(ref !== null);\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setRef2, className: rootWrapperClasses, style: topStyle, role: \"presentation\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: rootWrapperBodyClasses, ref: setGridBodyParent, role: \"presentation\" }, initialised && eGridBodyParent && beans && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(BeansContext.Provider, { value: beans }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    tabGuardComp_default,\n    {\n      ref: setTabGuardCompRef,\n      eFocusableElement: eGridBodyParent,\n      onTabKeyDown,\n      gridCtrl: gridCtrlRef.current,\n      forceFocusOutWhenTabGuardsAreEmpty: true\n    },\n    // we wait for initialised before rending the children, so GridComp has created and registered with it's\n    // GridCtrl before we create the child GridBodyComp. Otherwise the GridBodyComp would initialise first,\n    // before we have set the the Layout CSS classes, causing the GridBodyComp to render rows to a grid that\n    // doesn't have it's height specified, which would result if all the rows getting rendered (and if many rows,\n    // hangs the UI)\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(gridBodyComp_default, null)\n  ))));\n};\nvar gridComp_default = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GridComp);\n\n// packages/ag-grid-react/src/reactUi/renderStatusService.tsx\n\nvar RenderStatusService = class extends ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.BeanStub {\n  wireBeans(beans) {\n    this.ctrlsService = beans.ctrlsService;\n  }\n  areHeaderCellsRendered() {\n    return this.ctrlsService.getHeaderRowContainerCtrls().every((container) => container.getAllCtrls().every((ctrl) => ctrl.areCellsRendered()));\n  }\n};\n\n// packages/ag-grid-react/src/reactUi/agGridReactUi.tsx\nvar AgGridReactUi = (props) => {\n  const apiRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGui = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const portalManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const destroyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const whenReadyFuncs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const prevProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(props);\n  const frameworkOverridesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const gridIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const ready = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const [context, setContext] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const [, setPortalRefresher] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGui.current = eRef;\n    if (!eRef) {\n      destroyFuncs.current.forEach((f) => f());\n      destroyFuncs.current.length = 0;\n      return;\n    }\n    const modules = props.modules || [];\n    if (!portalManager.current) {\n      portalManager.current = new PortalManager(\n        () => setPortalRefresher((prev) => prev + 1),\n        props.componentWrappingElement,\n        props.maxComponentCreationTimeMs\n      );\n      destroyFuncs.current.push(() => {\n        portalManager.current?.destroy();\n        portalManager.current = null;\n      });\n    }\n    const mergedGridOps = (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._combineAttributesAndGridOptions)(props.gridOptions, props);\n    const processQueuedUpdates = () => {\n      if (ready.current) {\n        const getFn = () => frameworkOverridesRef.current?.shouldQueueUpdates() ? void 0 : whenReadyFuncs.current.shift();\n        let fn = getFn();\n        while (fn) {\n          fn();\n          fn = getFn();\n        }\n      }\n    };\n    const frameworkOverrides = new ReactFrameworkOverrides(processQueuedUpdates);\n    frameworkOverridesRef.current = frameworkOverrides;\n    const renderStatusService = new RenderStatusService();\n    const gridParams = {\n      providedBeanInstances: {\n        frameworkComponentWrapper: new ReactFrameworkComponentWrapper(\n          portalManager.current,\n          mergedGridOps.reactiveCustomComponents ?? (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._getGlobalGridOption)(\"reactiveCustomComponents\") ?? true\n        ),\n        renderStatusService\n      },\n      modules,\n      frameworkOverrides\n    };\n    const createUiCallback = (context2) => {\n      setContext(context2);\n      context2.createBean(renderStatusService);\n      destroyFuncs.current.push(() => {\n        context2.destroy();\n      });\n      context2.getBean(\"ctrlsService\").whenReady(\n        {\n          addDestroyFunc: (func) => {\n            destroyFuncs.current.push(func);\n          }\n        },\n        () => {\n          if (context2.isDestroyed()) {\n            return;\n          }\n          const api = apiRef.current;\n          if (api) {\n            props.setGridApi?.(api);\n          }\n        }\n      );\n    };\n    const acceptChangesCallback = (context2) => {\n      context2.getBean(\"ctrlsService\").whenReady(\n        {\n          addDestroyFunc: (func) => {\n            destroyFuncs.current.push(func);\n          }\n        },\n        () => {\n          whenReadyFuncs.current.forEach((f) => f());\n          whenReadyFuncs.current.length = 0;\n          ready.current = true;\n        }\n      );\n    };\n    const gridCoreCreator = new ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.GridCoreCreator();\n    mergedGridOps.gridId ?? (mergedGridOps.gridId = gridIdRef.current);\n    apiRef.current = gridCoreCreator.create(\n      eRef,\n      mergedGridOps,\n      createUiCallback,\n      acceptChangesCallback,\n      gridParams\n    );\n    destroyFuncs.current.push(() => {\n      apiRef.current = void 0;\n    });\n    if (apiRef.current) {\n      gridIdRef.current = apiRef.current.getGridId();\n    }\n  }, []);\n  const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return {\n      height: \"100%\",\n      ...props.containerStyle || {}\n    };\n  }, [props.containerStyle]);\n  const processWhenReady = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((func) => {\n    if (ready.current && !frameworkOverridesRef.current?.shouldQueueUpdates()) {\n      func();\n    } else {\n      whenReadyFuncs.current.push(func);\n    }\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const changes = extractGridPropertyChanges(prevProps.current, props);\n    prevProps.current = props;\n    processWhenReady(() => {\n      if (apiRef.current) {\n        (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._processOnChange)(changes, apiRef.current);\n      }\n    });\n  }, [props]);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style, className: props.className, ref: setRef2 }, context && !context.isDestroyed() ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(gridComp_default, { context }) : null, portalManager.current?.getPortals() ?? null);\n};\nfunction extractGridPropertyChanges(prevProps, nextProps) {\n  const changes = {};\n  Object.keys(nextProps).forEach((propKey) => {\n    const propValue = nextProps[propKey];\n    if (prevProps[propKey] !== propValue) {\n      changes[propKey] = propValue;\n    }\n  });\n  return changes;\n}\nvar ReactFrameworkComponentWrapper = class extends ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.BaseComponentWrapper {\n  constructor(parent, reactiveCustomComponents) {\n    super();\n    this.parent = parent;\n    this.reactiveCustomComponents = reactiveCustomComponents;\n  }\n  createWrapper(UserReactComponent, componentType) {\n    if (this.reactiveCustomComponents) {\n      const getComponentClass = (propertyName) => {\n        switch (propertyName) {\n          case \"filter\":\n            return FilterComponentWrapper;\n          case \"floatingFilterComponent\":\n            return FloatingFilterComponentWrapper;\n          case \"dateComponent\":\n            return DateComponentWrapper;\n          case \"dragAndDropImageComponent\":\n            return DragAndDropImageComponentWrapper;\n          case \"loadingOverlayComponent\":\n            return LoadingOverlayComponentWrapper;\n          case \"noRowsOverlayComponent\":\n            return NoRowsOverlayComponentWrapper;\n          case \"statusPanel\":\n            return StatusPanelComponentWrapper;\n          case \"toolPanel\":\n            return ToolPanelComponentWrapper;\n          case \"menuItem\":\n            return MenuItemComponentWrapper;\n          case \"cellRenderer\":\n            return CellRendererComponentWrapper;\n        }\n      };\n      const ComponentClass = getComponentClass(componentType.propertyName);\n      if (ComponentClass) {\n        return new ComponentClass(UserReactComponent, this.parent, componentType);\n      }\n    } else {\n      switch (componentType.propertyName) {\n        case \"filter\":\n        case \"floatingFilterComponent\":\n        case \"dateComponent\":\n        case \"dragAndDropImageComponent\":\n        case \"loadingOverlayComponent\":\n        case \"noRowsOverlayComponent\":\n        case \"statusPanel\":\n        case \"toolPanel\":\n        case \"menuItem\":\n        case \"cellRenderer\":\n          warnReactiveCustomComponents();\n          break;\n      }\n    }\n    const suppressFallbackMethods = !componentType.cellRenderer && componentType.propertyName !== \"toolPanel\";\n    return new ReactComponent(UserReactComponent, this.parent, componentType, suppressFallbackMethods);\n  }\n};\nvar DetailCellRenderer = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const { ctrlsFactory, context, gos, resizeObserverService, rowModel } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BeansContext);\n  const [cssClasses, setCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [gridCssClasses, setGridCssClasses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new CssClasses());\n  const [detailGridOptions, setDetailGridOptions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const [detailRowData, setDetailRowData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  const ctrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const eGuiRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const resizeObserverDestroyFunc = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const parentModules = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.ModuleRegistry.__getGridRegisteredModules(props.api.getGridId()), [props]);\n  const topClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => cssClasses.toString() + \" ag-details-row\", [cssClasses]);\n  const gridClassName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => gridCssClasses.toString() + \" ag-details-grid\", [gridCssClasses]);\n  if (ref) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => ({\n      refresh() {\n        return ctrlRef.current?.refresh() ?? false;\n      }\n    }));\n  }\n  if (props.template) {\n    (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._warnOnce)(\n      \"detailCellRendererParams.template is not supported by AG Grid React. To change the template, provide a Custom Detail Cell Renderer. See https://ag-grid.com/react-data-grid/master-detail-custom-detail/\"\n    );\n  }\n  const setRef2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((eRef) => {\n    eGuiRef.current = eRef;\n    if (!eRef) {\n      ctrlRef.current = context.destroyBean(ctrlRef.current);\n      resizeObserverDestroyFunc.current?.();\n      return;\n    }\n    const compProxy = {\n      addOrRemoveCssClass: (name, on) => setCssClasses((prev) => prev.setClass(name, on)),\n      addOrRemoveDetailGridCssClass: (name, on) => setGridCssClasses((prev) => prev.setClass(name, on)),\n      setDetailGrid: (gridOptions) => setDetailGridOptions(gridOptions),\n      setRowData: (rowData) => setDetailRowData(rowData),\n      getGui: () => eGuiRef.current\n    };\n    const ctrl = ctrlsFactory.getInstance(\"detailCellRenderer\");\n    if (!ctrl) {\n      return;\n    }\n    context.createBean(ctrl);\n    ctrl.init(compProxy, props);\n    ctrlRef.current = ctrl;\n    if (gos.get(\"detailRowAutoHeight\")) {\n      const checkRowSizeFunc = () => {\n        if (eGuiRef.current == null) {\n          return;\n        }\n        const clientHeight = eGuiRef.current.clientHeight;\n        if (clientHeight != null && clientHeight > 0) {\n          const updateRowHeightFunc = () => {\n            props.node.setRowHeight(clientHeight);\n            if ((0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._isClientSideRowModel)(gos) || (0,ag_grid_community__WEBPACK_IMPORTED_MODULE_1__._isServerSideRowModel)(gos)) {\n              rowModel.onRowHeightChanged();\n            }\n          };\n          setTimeout(updateRowHeightFunc, 0);\n        }\n      };\n      resizeObserverDestroyFunc.current = resizeObserverService.observeResize(eRef, checkRowSizeFunc);\n      checkRowSizeFunc();\n    }\n  }, []);\n  const setGridApi = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((api) => {\n    ctrlRef.current?.registerDetailWithMaster(api);\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: topClassName, ref: setRef2 }, detailGridOptions && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    AgGridReactUi,\n    {\n      className: gridClassName,\n      ...detailGridOptions,\n      modules: parentModules,\n      rowData: detailRowData,\n      setGridApi\n    }\n  ));\n});\nvar ReactFrameworkOverrides = class extends ag_grid_community__WEBPACK_IMPORTED_MODULE_1__.VanillaFrameworkOverrides {\n  constructor(processQueuedUpdates) {\n    super(\"react\");\n    this.processQueuedUpdates = processQueuedUpdates;\n    this.queueUpdates = false;\n    this.frameworkComponents = {\n      agGroupCellRenderer: groupCellRenderer_default,\n      agGroupRowRenderer: groupCellRenderer_default,\n      agDetailCellRenderer: DetailCellRenderer\n    };\n    this.wrapIncoming = (callback, source) => {\n      if (source === \"ensureVisible\") {\n        return runWithoutFlushSync(callback);\n      }\n      return callback();\n    };\n    this.renderingEngine = \"react\";\n  }\n  frameworkComponent(name) {\n    return this.frameworkComponents[name];\n  }\n  isFrameworkComponent(comp) {\n    if (!comp) {\n      return false;\n    }\n    const prototype = comp.prototype;\n    const isJsComp = prototype && \"getGui\" in prototype;\n    return !isJsComp;\n  }\n  getLockOnRefresh() {\n    this.queueUpdates = true;\n  }\n  releaseLockOnRefresh() {\n    this.queueUpdates = false;\n    this.processQueuedUpdates();\n  }\n  shouldQueueUpdates() {\n    return this.queueUpdates;\n  }\n  runWhenReadyAsync() {\n    return isReact19();\n  }\n};\n\n// packages/ag-grid-react/src/agGridReact.tsx\nvar AgGridReact = class extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor() {\n    super(...arguments);\n    this.apiListeners = [];\n    this.setGridApi = (api) => {\n      this.api = api;\n      this.apiListeners.forEach((listener) => listener(api));\n    };\n  }\n  registerApiListener(listener) {\n    this.apiListeners.push(listener);\n  }\n  componentWillUnmount() {\n    this.apiListeners.length = 0;\n  }\n  render() {\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(AgGridReactUi, { ...this.props, setGridApi: this.setGridApi });\n  }\n};\n\n// packages/ag-grid-react/src/shared/customComp/interfaces.ts\n\nfunction useGridCustomComponent(methods) {\n  const { setMethods } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(CustomContext);\n  setMethods(methods);\n}\nfunction useGridCellEditor(callbacks) {\n  useGridCustomComponent(callbacks);\n}\nfunction useGridDate(callbacks) {\n  return useGridCustomComponent(callbacks);\n}\nfunction useGridFilter(callbacks) {\n  return useGridCustomComponent(callbacks);\n}\nfunction useGridFloatingFilter(callbacks) {\n  useGridCustomComponent(callbacks);\n}\nfunction useGridMenuItem(callbacks) {\n  useGridCustomComponent(callbacks);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ag-grid-react/dist/package/index.esm.mjs\n");

/***/ })

};
;