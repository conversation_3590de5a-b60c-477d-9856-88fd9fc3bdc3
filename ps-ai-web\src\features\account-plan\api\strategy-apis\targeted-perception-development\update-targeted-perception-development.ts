import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  APTargetedPerceptionDevelopment,
  APTargetedPerceptionDevelopmentBaseData,
} from "@/features/account-plan/types/strategy-types";

export const updateTargetedPerceptionDevelopment = ({
  accountId,
  id,
  data,
}: {
  id: number;
  accountId: number;
  data?: APTargetedPerceptionDevelopmentBaseData;
}): ApiResponse<APTargetedPerceptionDevelopment> => {
  return api.put(
    API_ROUTES.ACCOUNT_PLANS_TARGETED_PERCEPTION_DEVELOPMENT_DETAIL(
      accountId,
      id
    ),
    data
  );
};

type UseUpdateTargetedPerceptionDevelopmentOptions = {
  mutationConfig?: MutationConfig<typeof updateTargetedPerceptionDevelopment>;
};

export const useUpdateTargetedPerceptionDevelopment = ({
  mutationConfig,
}: UseUpdateTargetedPerceptionDevelopmentOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_TARGETED_PERCEPTION_DEVELOPMENT,
        ],
      });

      onSuccess?.(...args);
    },

    ...restConfig,
    mutationFn: updateTargetedPerceptionDevelopment,
  });
};
