# frozen_string_literal: true

class ApiOutput
  include ActionView::Helpers::NumberHelper
  attr_reader :options

  def self.root_key
    :data
  end

  def self.array(models, **options)
    ArrayOutput.new(models, **options, item_output: self)
  end

  def initialize(object, options = {})
    @object = object
    @options = options
    @options[:status] ||= 200
    @options[:status] = Rack::Utils.status_code(@options[:status])
  end

  def root_json
    if error?
      error_format
    elsif self.class.root_key
      { self.class.root_key.to_s => as_json }
    else
      as_json
    end
  end

  def as_json(*_)
    format_method = @options[:use] || :format
    format_method = :error_format if error?
    send(format_method).as_json
  end

  def status
    Rack::Utils.status_code(error? ? error_status : @options[:status])
  end

  ################################
  # Overridable parts start here #
  ################################

  def format
    @object.as_json
  end

  def error?
    @object.respond_to?(:errors) && @object.errors.any?
  end

  def error_format
    ErrorOutput.new(@object, status: error_status, details: error_details)
  end

  def error_status
    ErrorOutput.new(@object).status
  end

  def error_details
    nil
  end

  def maybe(key_name, &block)
    block ||= key_name.to_sym.to_proc
    is_displayable = send("show_#{key_name}?")
    is_displayable ? { key_name => block.call(self) } : {}
  end

  def url_for(blob)
    Rails
      .application
      .routes
      .url_helpers
      .rails_blob_path(
        blob,
        only_path: true
      )
  end

  def excluded_fields
    %w[created_at discarded_at updated_at]
  end
end
