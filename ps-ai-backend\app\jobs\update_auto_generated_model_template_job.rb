class UpdateAutoGeneratedModelTemplateJob < ApplicationJob
  queue_as :default

  def perform(model_template, template_category)
    service = ModelTemplateService.new({ user: User.new, organization: Organization.new(tier: 'superuser') })

    params = {
      temperature: 1,
      max_tokens: 16000,
      rules: "You are business analyst"
    }
    service.update(model_template.id, params, bypass_verification: true)
  end
end
