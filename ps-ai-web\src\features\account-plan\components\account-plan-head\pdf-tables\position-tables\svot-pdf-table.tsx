import _ from "lodash";
import { Fragment } from "react";

import {
  PdfMarkdown,
  PdfTable,
  PdfTableCell,
  PdfTableRow,
  PdfTableTitle,
} from "../";
import { AccountPlanAnalysisPDFProps } from "../../download-analysis";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { AccountPlanTableType } from "@/features/account-plan/types";

export const SvotPDFTable = ({
  data,
}: {
  data?: AccountPlanAnalysisPDFProps["svotList"];
}) => {
  return (
    <PdfTable>
      <PdfTableTitle>
        {getAccountPlanTableName(AccountPlanTableType.SVOT)}
      </PdfTableTitle>

      {data
        ?.filter((v) => !!v.item_type)
        .map((row, idx) => (
          <Fragment key={idx}>
            <PdfTableRow>
              <PdfTableCell
                style={{
                  backgroundColor: "#e5e5e5",
                  textAlign: "left",
                  fontWeight: 700,
                }}
              >
                {_.capitalize(row.item_type)}
              </PdfTableCell>
            </PdfTableRow>
            <PdfTableRow>
              <PdfMarkdown style={{ flex: 3 }}>{row.description}</PdfMarkdown>
            </PdfTableRow>
          </Fragment>
        ))}
    </PdfTable>
  );
};
