# frozen_string_literal: true

class AppService
  def upload(blob, file, changed_file: false)
    blob.purge if blob.attached? && changed_file
    blob.attach(file) if file
  end

  def authorize!(*permissions, on_error: e('.not_allowed'))
    raise ExceptionHandler::Unauthorized, on_error if permissions.none?
  end

  def assert!(*truths, on_error: e('.invalid'))
    raise Invalid, on_error if truths.none?
  end

  def exist!(object, on_error: e('.none'))
    raise ExceptionHandler::NotFound, on_error if object.blank?

    object
  end

  def duplicate_attributes(record)
    record.attributes
          .with_indifferent_access
          .except(:id, :created_at, :updated_at)
  end

  def as_boolean(value)
    ActiveModel::Type::Boolean.new.cast(value)
  end

  def validate_email(email)
    email =~ /\A([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})\z/i
  end

  def registered_user(email)
    User.find_by(email: email)
  end

  def locale_properties
    @locale_properties ||= Locale.find_by(language_code: 'id')&.properties || {}
  end

  def verify_user_organization(user, organization_user, organization, with_permissions: false)
    authorize! organization_user.present? &&
                 organization.present? &&
                 organization_user.organization_id == organization.id,
               on_error: 'Not in any organization'

    if with_permissions
      authorize! organization_user.prompt_tuning_permissions, on_error: 'Not enough permissions'
    end

    organization_user
  end

  def verify_roles(roles, organization_user)
    authorize! roles.include?(organization_user.role&.name), on_error: 'Not enough permissions'
  end

  def verify_organization_tier(tiers, organization)
    authorize! tiers.include?(organization.tier), on_error: 'Not enough permissions'
  end

  def verify_account_plan_ownership(account_plan, organization_user)
    authorize! account_plan.organization_id == organization_user.organization_id,
               on_error: 'Not an organization account plan'
  end

  def revenue_forecast_calculations(account_plan_id)
    ap_current_rev = ApTable.find_by(account_plan_id: account_plan_id, table_type: 'current_revenue')
    ap_current_opp = ApTable.find_by(account_plan_id: account_plan_id, table_type: 'current_opportunity')
    ap_potential_opp = ApTable.find_by(account_plan_id: account_plan_id, table_type: 'potential_opportunity')
    ap_rev_forecast = ApTable.find_by(account_plan_id: account_plan_id, table_type: 'revenue_forecast')

    sum_current_rev = ::AccountPlanTableItems::ApCurrentRevenueItem.where(ap_table_id: ap_current_rev.id).pluck(:value).compact.sum
    sum_current_opp = ::AccountPlanTableItems::ApCurrentOpportunityItem.where(ap_table_id: ap_current_opp.id).pluck(:value).compact.sum
    sum_potential_opp = ::AccountPlanTableItems::ApPotentialOpportunityItem.where(ap_table_id: ap_potential_opp.id).pluck(:value).compact.sum

    low_scenario = sum_current_rev + ((3.to_f / 10) * sum_current_opp) + ((3.to_f / 10) * sum_potential_opp)
    realistic_scenario = sum_current_rev + ((5.to_f / 10) * sum_current_opp) + ((5.to_f / 10) * sum_potential_opp)
    high_scenario = sum_current_rev + ((9.to_f / 10) * sum_current_opp) + ((9.to_f / 10) * sum_potential_opp)

    ::AccountPlanTableItems::ApRevenueForecastItem.where(ap_table_id: ap_rev_forecast.id).update_all(
      low_scenario: low_scenario,
      realistic_scenario: realistic_scenario,
      high_scenario: high_scenario
    )
  end

  def account_plan_consideration_llm_query(account_plan)
    apg = account_plan.account_plan_group
    topic = apg.industry&.name

    authorize! topic.present? && apg.location.present? && apg.company.present? && apg.currency.present? && apg.account_addressable_area.present?, on_error: "Account parameter(s) is not filled"

    rss_service = RssService.new(@user_data)
    result = rss_service.index({industry_id: apg.industry_id}) # only get new_feed
    rss_feeds = result.rss_feeds
    rss_feeds_query = rss_feeds.map do |rf|
      "(#{rf.title}, #{rf.description}, #{rf.url}, #{rf.date_published})"
    end.join(',')

    query = "data:\n" \
                "'<@I-industry>': #{topic}\n" \
                "'<@I-location>': #{apg.location}\n" \
                "'<@I-company>': #{apg.company}\n" \
                "'<@I-currency>': #{apg.currency}\n" \
                "'<@I-account_addressable_area>': #{apg.account_addressable_area}\n" \
                "'feeds' list(title_feed, description_feed, url_feed, date_published) = [#{rss_feeds_query}]\n" \
                "\n\n" \
              "'role' = Expert Business Analysis\n"

    query
  end

  def verify_ap_stakeholder_mapping_to_account_plan(ap_stakeholder_mapping_item, account_plan)
    authorize! ap_stakeholder_mapping_item&.ap_table&.account_plan_id == account_plan.id,
               on_error: 'Invalid stakeholder!'
  end

  def apa_titleize(text)
    minor_words = %w[a an the and but or for nor of to as at by for in of on per via with]
    
    text.split.each_with_index.map do |word, index|
      if index == 0 || index == text.split.size - 1
        word.capitalize
      elsif minor_words.include?(word.downcase) && !word.include?('-')
        word.downcase
      elsif word.include?('-')
        word.split('-').map(&:capitalize).join('-')
      else
        word.capitalize
      end
    end.join(' ')
  end

  private

  def t(key, options = {})
    key = "actions.#{self.class.name.underscore.tr('/', '.')}#{key}" if key.to_s.first == '.'

    I18n.translate(key, **options.reverse_merge(cascade: true))
  end

  def e(key, options = {})
    opts = options.merge(scope: [:errors].concat(Array(options[:scope])))
    t(key, opts)
  end

  class Invalid < ::StandardError
  end
end
