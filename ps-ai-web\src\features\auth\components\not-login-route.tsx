/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/display-name */
"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { PATH } from "@/constants/path";
import { useAuthStore } from "../stores/auth-store";

export default function withNotLoginRoute(Component: any) {
  return (props: any) => {
    const router = useRouter();
    const { isLogin, _hasHydrated } = useAuthStore();

    if (!_hasHydrated) return null;

    if (isLogin) {
      return router.push(PATH.DASHBOARD);
    }

    return <Component {...props} />;
  };
}
