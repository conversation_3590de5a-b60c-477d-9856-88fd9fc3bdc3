# frozen_string_literal: true

module V1
  class LlmConsiderationsController < ApiController
    authorize_auth_token! :all

    def index
      result = service.index(query_params)

      render_json_array result.llm_considerations,
                        use: :format
    end

    private

    def default_output
      ::V1::LlmConsiderationOutput
    end

    def service
      @service ||= ::LlmConsiderationService.new(current_user_data)
    end
  end
end
