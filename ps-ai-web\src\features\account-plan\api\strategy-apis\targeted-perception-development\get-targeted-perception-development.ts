import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APTargetedPerceptionDevelopment } from "@/features/account-plan/types/strategy-types";

export const getTargetedPerceptionDevelopmentDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APTargetedPerceptionDevelopment> => {
  return api.get(
    API_ROUTES.ACCOUNT_PLANS_TARGETED_PERCEPTION_DEVELOPMENT_DETAIL(
      accountId,
      id
    )
  );
};

export const getTargetedPerceptionDevelopmentDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_TARGETED_PERCEPTION_DEVELOPMENT,
      id,
    ],
    queryFn: () => getTargetedPerceptionDevelopmentDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseTargetedPerceptionDevelopmentDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getTargetedPerceptionDevelopmentDetail>;
  options?: Partial<
    ReturnType<typeof getTargetedPerceptionDevelopmentDetailQueryOptions>
  >;
};

export const useTargetedPerceptionDevelopmentDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseTargetedPerceptionDevelopmentDetailOptions) => {
  const targetedPerceptionDevelopmentDetailQuery = useQuery({
    ...getTargetedPerceptionDevelopmentDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...targetedPerceptionDevelopmentDetailQuery,
    targetedPerceptionDevelopmentDetail:
      targetedPerceptionDevelopmentDetailQuery.data?.data,
  };
};
