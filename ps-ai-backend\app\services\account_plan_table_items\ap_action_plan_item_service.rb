# frozen_string_literal: true

class AccountPlanTableItems::ApActionPlanItemService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
    @user_data = user_data
  end

  def create(account_plan_id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApActionPlanItem.new

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: 'strategy',
        table_type: 'action_plan',
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item = ::AccountPlanTableItems::ApActionPlanItem.create!(params)
    end

    OpenStruct.new(
      ap_action_plan_item: ap_item
    )
  end

  def update(account_plan_id, id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApActionPlanItem.find(id)

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: 'strategy',
        table_type: 'action_plan',
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item.update!(params)
    end

    OpenStruct.new(
      ap_action_plan_item: ap_item
    )
  end

  def index(account_plan_id, query_params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_table = ApTable.find_by(
      table_category: 'strategy',
      table_type: 'action_plan',
      account_plan_id: account_plan_id
    )

    ap_item_reposity = ::AccountPlanTableItems::ApActionPlanItems.new

    filter = query_params.slice(
      :search, :page, :per_page, :disable_pagination
    )
    filter = filter.merge(
      ap_table_id: ap_table&.id || -1
    )

    filtered_items = ap_item_reposity.filter(filter)

    OpenStruct.new(
      ap_action_plan_items: filtered_items
    )
  end

  def show(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApActionPlanItem.find(id)
    
    OpenStruct.new(
      ap_action_plan_item: ap_item
    ) 
  end

  def destroy(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApActionPlanItem.find(id)
  
    ActiveRecord::Base.transaction do
      ap_item.discard!
    end
  end

  def generate(account_plan_id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_table = ApTable.find_by(
      table_category: 'strategy',
      table_type: 'action_plan',
      account_plan_id: account_plan_id
    )

    exist! ap_table.present?, on_error: 'Table is not created'

    ActiveRecord::Base.transaction do
      ::AccountPlanTableItems::ApActionPlanItem.where(ap_table_id: ap_table.id).discard_all

      desc = generate_action_plan(account_plan_id)

      if !desc.blank?
        create_params = {
          ap_table_id: ap_table.id,
          description: desc
        }

        ::AccountPlanTableItems::ApActionPlanItem.create!(create_params)
      end
    end
  end

  def generate_action_plan(account_plan_id)
    ap_tables = ApTable.where(account_plan_id: account_plan_id)
    action_plan = ""
    current_time = Time.current

    ap_tables.each do |at|
      if at.table_type == 'stakeholder_mapping'
        items = ::AccountPlanTableItems::ApStakeholderMappingItem.where(ap_table_id: at.id).order(:id)

        filled = false
        if items.size < 5
          action_plan += "- There are typically multiple stakeholders from different areas of their business who may influence decision-making and prioritisation. Consider who else should be on your list in your stakeholder mapping.\n"
          filled = true
        end

        all_roles = items.pluck(:role).flatten.compact
        unique_roles = all_roles.uniq

        if !unique_roles.include?("Sponsor")
          action_plan += "- Who, either listed in Stakeholder Mapping or not yet listed, could you develop as a Sponsor?\n"
          filled = true
        end

        if !unique_roles.include?("Coach")
          action_plan += "- Who could you start to develop as a Coach?\n"
          filled = true
        end

        if all_roles.count("Coach") == 1
          action_plan += "- Who else could you develop as an additional Coach?\n"
          filled = true
        end

        if !unique_roles.include?("Anti-sponsor")
          action_plan += "- You should always be looking for the Anti-sponsor; who would be a likely Anti-sponsor?\n"
          filled = true
        end

        items.each_with_index do |item, idx|
          adv = item.advocacy
          perc = item.perception
          cov = item.coverage

          if item.name.present?
            if ['Advocates Us', 'Prefers Us', 'No Preference'].include?(adv) && !unique_roles.include?("Coach")
              action_plan += "- As #{item.name} '#{adv}', what could you do to develop them into a Coach?\n"
              filled = true
            end

            if ['Preferred Supplier', 'Vendor'].include?(perc)
              action_plan += "- How do you advance #{item.name}'s perception of us?\n"
              filled = true
            end

            if ['Prefers Alternative', 'Advocates Alternative'].include?(adv)
              action_plan += "- How do you mitigate #{item.name}'s influence and support for the competition?\n"
              filled = true
            end

            if cov == false
              action_plan += "- You do not have coverage over #{item.name}. How do you engage #{item.name.split(" ")[0]} to develop his/her perception of us?\n"
              filled = true
            end
          end
        end

        if filled
          action_plan += "\n"
        end
      end

      if at.table_type == "svot"
        items = ::AccountPlanTableItems::ApSvotItem.where(ap_table_id: at.id)
        types = ::AccountPlanTableItems::ApSvotItem.item_types.values

        opt = false
        if items.size == 0
          opt = true
        end

        created_types = items.pluck(:item_type)
        not_created_types = types - created_types
        if not_created_types.any?
          opt = true
        end

        items.each_with_index do |item, idx|
          if item.item_type.nil?
            # invalid_rows = true
            next
          elsif item.description.blank?
            opt = true
          end
        end

        if opt
          action_plan += "- Have you reviewed the latest Circumstantial analysis data and completed an updated S.V.O.T Analysis?\n"
        end

        action_plan += "- When considering insights and perspective, who should you be strategically targeting to deliver specific insights and perspective?\n"
        action_plan += "- How will you go about delivering the insights and when will this be completed?\n"
        action_plan += "- From your SVOT analysis, what insights and perspective can you derive to add value, create differentiation and advance stakeholder perceptions?\n\n"
      end

      if at.table_type == "insight_and_perspective"
        items = ::AccountPlanTableItems::ApInsightAndPerspectiveItem.where(ap_table_id: at.id)

        opt = false

        items.each_with_index do |item, idx|
          if item.ap_stakeholder_mapping_item.present?
            opt = true
          end
        end

        if opt
          action_plan += "- How will you deliver the insights and perspective to the stakeholder or group of stakeholders that will bring most value to them?\n\n"
        else
          action_plan += "- When you consider high-value insights and perspective you can bring, which stakeholder(s) will benefit most from the specific Insights and Perspective?\n\n"
        end
      end

      if at.table_type == 'current_revenue'
        items = ::AccountPlanTableItems::ApCurrentRevenueItem.where(ap_table_id: at.id)

        filled = false
        items.each_with_index do |item, idx|
          if item.value.present? && item.product_service_name.present? && item.renewal_date.present?
            if item.renewal_date > current_time && item.renewal_date < current_time + 12.months
              action_plan += "- The renewal date for #{item.product_service_name} is due within 12-months; when do you start preparing and what do you need to do?\n"
              filled = true
            end
          end
        end

        if filled
          action_plan += "\n"
        end
      end

      if at.table_type == 'current_opportunity'
        items = ::AccountPlanTableItems::ApCurrentOpportunityItem.where(ap_table_id: at.id)

        filled = false
        items.each_with_index do |item, idx|
          if item.value.present? && item.product_service_name.present? && item.close_date.present?
            if item.close_date > current_time && item.close_date < current_time + 3.months
              action_plan += "- The Close Date for #{item.product_service_name} is due within 3-months; what work is required to win this opportunity?\n"
              filled = true
            end
          end
        end

        if filled
          action_plan += "\n"
        end

        sum_item_values = items.pluck(:value).map { |v| v.to_i }.sum

        wallet_share_apt = ap_tables.find { |apt| apt.table_type == 'wallet_share' }
        wallet_share_items = ::AccountPlanTableItems::ApWalletShareItem.where(ap_table_id: wallet_share_apt&.id)
        
        available_wallet_share = 0
        wallet_size = wallet_share_items.find { |i| i.item_type == "addressable" }
        existing = wallet_share_items.find { |i| i.item_type == "ours" }
        competition = wallet_share_items.find { |i| i.item_type == "competition" }
        available_wallet_share = wallet_size&.shared_type_analysis.to_i - existing&.shared_type_analysis.to_i - competition&.shared_type_analysis.to_i

        if sum_item_values > available_wallet_share
          action_plan += "- Total Current Opportunities in Play is greater than the total Available Wallet Size\n\n"
        end
      end

      if at.table_type == 'potential_opportunity'
        items = ::AccountPlanTableItems::ApPotentialOpportunityItem.where(ap_table_id: at.id)

        action_plan += "- As you consider the Circumstantial Analysis the SVOT and Insights and Perspective; what other potential upsell or cross sell opportunities can you identify?\n\n"
        sum_item_values = items.pluck(:value).map { |v| v.to_i }.sum

        wallet_share_apt = ap_tables.find { |apt| apt.table_type == 'wallet_share' }
        wallet_share_items = ::AccountPlanTableItems::ApWalletShareItem.where(ap_table_id: wallet_share_apt&.id)
        
        available_wallet_share = 0
        wallet_size = wallet_share_items.find { |i| i.item_type == "addressable" }
        existing = wallet_share_items.find { |i| i.item_type == "ours" }
        competition = wallet_share_items.find { |i| i.item_type == "competition" }
        available_wallet_share = wallet_size&.shared_type_analysis.to_i - existing&.shared_type_analysis.to_i - competition&.shared_type_analysis.to_i

        if sum_item_values > available_wallet_share
          action_plan += "- Total Cross/ Up-sell Opportunities is greater than the total Available Wallet Size\n\n"
        end
      end

      if at.table_type == 'missing_information'
        items = ::AccountPlanTableItems::ApHistoricRevenueItem.where(ap_table_id: at.id)
        
        present = false

        items.each_with_index do |item, idx|
          present = item.description.present?
        end

        if present
          action_plan += "- Consider the Information Needed, how would you prioritise obtaining the information and what actions do you need to take to get that information?\n\n"
        end
      end

      if at.table_type == 'targeted_perception_development'
        items = ::AccountPlanTableItems::ApTargetedPerceptionDevelopmentItem.where(ap_table_id: at.id).order(:ap_stakeholder_mapping_item_id)

        tpd_item_stakeholder_ids = items.pluck(:ap_stakeholder_mapping_item_id)
        
        if tpd_item_stakeholder_ids.size == 0
          action_plan += "- If you were to select one or two stakeholders whereby, if you are able to positively advance their perception of us this would help us address a strategic issue in this relationship, who would that be and how would you do it?\n"
        else
          action_plan += "- If you were to select an additional stakeholder whereby, if you are able to positively advance their perception of us this would help us address a strategic issue in this relationship, who would that be and how would we do it?\n"
        end

        opt = false
        items.each do |item|
          if item.action.blank? || item.result.blank? || item.leverage.blank?
            opt = true
          end
        end

        if opt
          action_plan += "- Using the scripted structure allows you to develop your thinking around the issue you can help them solve; the result you will help them achieve; the strength you will be able to leverage.\n\n"
        else
          action_plan += "\n"
        end
      end

      if at.table_type == 'client_meeting_schedule'
        items = ::AccountPlanTableItems::ApClientMeetingScheduleItem.where(ap_table_id: at.id)
        
        has_meeting = false

        items.each_with_index do |item, idx|
          has_meeting = item.meeting_date.present?
        end

        if !has_meeting
          action_plan += "- There are no future meetings scheduled. When do you intend to meet your stakeholders again and who do you intend to meet and for what purpose?\n\n"
        end
      end
    end

    return action_plan
  end
end
