# frozen_string_literal: true

class AccountPlanTableItems::ApClientMeetingScheduleItemService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
  end

  def create(account_plan_id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    if params.has_key?(:ap_stakeholder_mapping_item_id)
      stakeholder_mapping = ::AccountPlanTableItems::ApStakeholderMappingItem.find_by(id: params[:ap_stakeholder_mapping_item_id])
      verify_ap_stakeholder_mapping_to_account_plan(stakeholder_mapping, account_plan)
    end

    ap_item = ::AccountPlanTableItems::ApClientMeetingScheduleItem.new

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: 'strategy',
        table_type: 'client_meeting_schedule',
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item = ::AccountPlanTableItems::ApClientMeetingScheduleItem.create!(params)
    end

    OpenStruct.new(
      ap_client_meeting_schedule_item: ap_item
    )
  end

  def update(account_plan_id, id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    if params.has_key?(:ap_stakeholder_mapping_item_id)
      stakeholder_mapping = ::AccountPlanTableItems::ApStakeholderMappingItem.find_by(id: params[:ap_stakeholder_mapping_item_id])
      verify_ap_stakeholder_mapping_to_account_plan(stakeholder_mapping, account_plan)
    end

    ap_item = ::AccountPlanTableItems::ApClientMeetingScheduleItem.find(id)

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: 'strategy',
        table_type: 'client_meeting_schedule',
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item.update!(params)
    end

    OpenStruct.new(
      ap_client_meeting_schedule_item: ap_item
    )
  end

  def index(account_plan_id, query_params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_table = ApTable.find_by(
      table_category: 'strategy',
      table_type: 'client_meeting_schedule',
      account_plan_id: account_plan_id
    )

    ap_item_reposity = ::AccountPlanTableItems::ApClientMeetingScheduleItems.new

    filter = query_params.slice(
      :search, :page, :per_page, :disable_pagination
    )
    filter = filter.merge(
      ap_table_id: ap_table&.id || -1,
      exist_stakeholder: true,
      sort_column: 'id',
      sort_direction: 'asc'
    )

    filtered_items = ap_item_reposity.filter(filter)

    OpenStruct.new(
      ap_client_meeting_schedule_items: filtered_items
    )
  end

  def show(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApClientMeetingScheduleItem.find(id)
    
    OpenStruct.new(
      ap_client_meeting_schedule_item: ap_item
    ) 
  end

  def destroy(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApClientMeetingScheduleItem.find(id)

    ActiveRecord::Base.transaction do
      ap_item.discard!
    end
  end
end
