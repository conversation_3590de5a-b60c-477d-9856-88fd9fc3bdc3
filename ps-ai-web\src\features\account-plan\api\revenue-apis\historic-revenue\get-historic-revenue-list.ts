import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APHistoricRevenue } from "@/features/account-plan/types/revenue-types";

type HistoricRevenueListParams = BaseParams;

export const getHistoricRevenueList = ({
  accountId,
  params,
}: {
  accountId: number;
  params?: HistoricRevenueListParams;
}): ApiResponse<APHistoricRevenue[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_HISTORIC_REVENUE(accountId), {
    params,
  });
};

export const getHistoricRevenueListQueryOptions = (
  accountId: number,
  params?: HistoricRevenueListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_HISTORIC_REVENUE,
    ],
    queryFn: () => getHistoricRevenueList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseHistoricRevenueListOptions = {
  params?: HistoricRevenueListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getHistoricRevenueList>;
  options?: Partial<ReturnType<typeof getHistoricRevenueListQueryOptions>>;
};

export const useHistoricRevenueList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UseHistoricRevenueListOptions) => {
  const historicRevenueListQuery = useQuery({
    ...getHistoricRevenueListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...historicRevenueListQuery,
    historicRevenueList: historicRevenueListQuery.data?.data,
  };
};
