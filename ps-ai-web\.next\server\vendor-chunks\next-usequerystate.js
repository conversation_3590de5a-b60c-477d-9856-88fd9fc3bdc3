"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-usequerystate";
exports.ids = ["vendor-chunks/next-usequerystate"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-usequerystate/dist/chunk-MJO6WWF3.js":
/*!****************************************************************!*\
  !*** ./node_modules/next-usequerystate/dist/chunk-MJO6WWF3.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createParser: () => (/* binding */ createParser),\n/* harmony export */   createSerializer: () => (/* binding */ createSerializer),\n/* harmony export */   debug: () => (/* binding */ debug),\n/* harmony export */   error: () => (/* binding */ error),\n/* harmony export */   getDefaultThrottle: () => (/* binding */ getDefaultThrottle),\n/* harmony export */   parseAsArrayOf: () => (/* binding */ parseAsArrayOf),\n/* harmony export */   parseAsBoolean: () => (/* binding */ parseAsBoolean),\n/* harmony export */   parseAsFloat: () => (/* binding */ parseAsFloat),\n/* harmony export */   parseAsHex: () => (/* binding */ parseAsHex),\n/* harmony export */   parseAsInteger: () => (/* binding */ parseAsInteger),\n/* harmony export */   parseAsIsoDateTime: () => (/* binding */ parseAsIsoDateTime),\n/* harmony export */   parseAsJson: () => (/* binding */ parseAsJson),\n/* harmony export */   parseAsNumberLiteral: () => (/* binding */ parseAsNumberLiteral),\n/* harmony export */   parseAsString: () => (/* binding */ parseAsString),\n/* harmony export */   parseAsStringEnum: () => (/* binding */ parseAsStringEnum),\n/* harmony export */   parseAsStringLiteral: () => (/* binding */ parseAsStringLiteral),\n/* harmony export */   parseAsTimestamp: () => (/* binding */ parseAsTimestamp),\n/* harmony export */   renderQueryString: () => (/* binding */ renderQueryString),\n/* harmony export */   safeParse: () => (/* binding */ safeParse)\n/* harmony export */ });\n// src/debug.ts\nvar enabled = isDebugEnabled();\nfunction debug(message, ...args) {\n  if (!enabled) {\n    return;\n  }\n  const msg = sprintf(message, ...args);\n  performance.mark(msg);\n  console.log(message, ...args);\n}\nfunction warn(message, ...args) {\n  if (!enabled) {\n    return;\n  }\n  console.warn(message, ...args);\n}\nfunction sprintf(base, ...args) {\n  return base.replace(/%[sfdO]/g, (match) => {\n    const arg = args.shift();\n    if (match === \"%O\" && arg) {\n      return JSON.stringify(arg).replace(/\"([^\"]+)\":/g, \"$1:\");\n    } else {\n      return String(arg);\n    }\n  });\n}\nfunction isDebugEnabled() {\n  try {\n    if (typeof localStorage === \"undefined\") {\n      return false;\n    }\n    const test = \"nuqs-localStorage-test\";\n    localStorage.setItem(test, test);\n    const isStorageAvailable = localStorage.getItem(test) === test;\n    localStorage.removeItem(test);\n    if (!isStorageAvailable) {\n      return false;\n    }\n  } catch (error2) {\n    console.error(\n      \"[nuqs]: debug mode is disabled (localStorage unavailable).\",\n      error2\n    );\n    return false;\n  }\n  const debug2 = localStorage.getItem(\"debug\") ?? \"\";\n  return debug2.includes(\"nuqs\") || debug2.includes(\"next-usequerystate\");\n}\n\n// src/utils.ts\nfunction safeParse(parser, value, key) {\n  try {\n    return parser(value);\n  } catch (error2) {\n    warn(\n      \"[nuqs] Error while parsing value `%s`: %O\" + (key ? \" (for key `%s`)\" : \"\"),\n      value,\n      error2,\n      key\n    );\n    return null;\n  }\n}\nfunction getDefaultThrottle() {\n  if (typeof window === \"undefined\") return 50;\n  const isSafari = Boolean(window.GestureEvent);\n  if (!isSafari) {\n    return 50;\n  }\n  try {\n    const match = navigator.userAgent?.match(/version\\/([\\d\\.]+) safari/i);\n    return parseFloat(match[1]) >= 17 ? 120 : 320;\n  } catch {\n    return 320;\n  }\n}\n\n// src/parsers.ts\nfunction createParser(parser) {\n  function parseServerSideNullable(value) {\n    if (typeof value === \"undefined\") {\n      return null;\n    }\n    let str = \"\";\n    if (Array.isArray(value)) {\n      if (value[0] === void 0) {\n        return null;\n      }\n      str = value[0];\n    }\n    if (typeof value === \"string\") {\n      str = value;\n    }\n    return safeParse(parser.parse, str);\n  }\n  return {\n    eq: (a, b) => a === b,\n    ...parser,\n    parseServerSide: parseServerSideNullable,\n    withDefault(defaultValue) {\n      return {\n        ...this,\n        defaultValue,\n        parseServerSide(value) {\n          return parseServerSideNullable(value) ?? defaultValue;\n        }\n      };\n    },\n    withOptions(options) {\n      return {\n        ...this,\n        ...options\n      };\n    }\n  };\n}\nvar parseAsString = createParser({\n  parse: (v) => v,\n  serialize: (v) => `${v}`\n});\nvar parseAsInteger = createParser({\n  parse: (v) => {\n    const int = parseInt(v);\n    if (Number.isNaN(int)) {\n      return null;\n    }\n    return int;\n  },\n  serialize: (v) => Math.round(v).toFixed()\n});\nvar parseAsHex = createParser({\n  parse: (v) => {\n    const int = parseInt(v, 16);\n    if (Number.isNaN(int)) {\n      return null;\n    }\n    return int;\n  },\n  serialize: (v) => {\n    const hex = Math.round(v).toString(16);\n    return hex.padStart(hex.length + hex.length % 2, \"0\");\n  }\n});\nvar parseAsFloat = createParser({\n  parse: (v) => {\n    const float = parseFloat(v);\n    if (Number.isNaN(float)) {\n      return null;\n    }\n    return float;\n  },\n  serialize: (v) => v.toString()\n});\nvar parseAsBoolean = createParser({\n  parse: (v) => v === \"true\",\n  serialize: (v) => v ? \"true\" : \"false\"\n});\nvar parseAsTimestamp = createParser({\n  parse: (v) => {\n    const ms = parseInt(v);\n    if (Number.isNaN(ms)) {\n      return null;\n    }\n    return new Date(ms);\n  },\n  serialize: (v) => v.valueOf().toString()\n});\nvar parseAsIsoDateTime = createParser({\n  parse: (v) => {\n    const date = new Date(v);\n    if (Number.isNaN(date.valueOf())) {\n      return null;\n    }\n    return date;\n  },\n  serialize: (v) => v.toISOString()\n});\nfunction parseAsStringEnum(validValues) {\n  return createParser({\n    parse: (query) => {\n      const asEnum = query;\n      if (validValues.includes(asEnum)) {\n        return asEnum;\n      }\n      return null;\n    },\n    serialize: (value) => value.toString()\n  });\n}\nfunction parseAsStringLiteral(validValues) {\n  return createParser({\n    parse: (query) => {\n      const asConst = query;\n      if (validValues.includes(asConst)) {\n        return asConst;\n      }\n      return null;\n    },\n    serialize: (value) => value.toString()\n  });\n}\nfunction parseAsNumberLiteral(validValues) {\n  return createParser({\n    parse: (query) => {\n      const asConst = parseFloat(query);\n      if (validValues.includes(asConst)) {\n        return asConst;\n      }\n      return null;\n    },\n    serialize: (value) => value.toString()\n  });\n}\nfunction parseAsJson(parser) {\n  return createParser({\n    parse: (query) => {\n      try {\n        const obj = JSON.parse(query);\n        if (typeof parser === \"function\") {\n          return parser(obj);\n        }\n        return obj;\n      } catch {\n        return null;\n      }\n    },\n    serialize: (value) => JSON.stringify(value),\n    eq(a, b) {\n      return a === b || JSON.stringify(a) === JSON.stringify(b);\n    }\n  });\n}\nfunction parseAsArrayOf(itemParser, separator = \",\") {\n  const itemEq = itemParser.eq ?? ((a, b) => a === b);\n  const encodedSeparator = encodeURIComponent(separator);\n  return createParser({\n    parse: (query) => {\n      if (query === \"\") {\n        return [];\n      }\n      return query.split(separator).map(\n        (item, index) => safeParse(\n          itemParser.parse,\n          item.replaceAll(encodedSeparator, separator),\n          `[${index}]`\n        )\n      ).filter((value) => value !== null && value !== void 0);\n    },\n    serialize: (values) => values.map((value) => {\n      const str = itemParser.serialize ? itemParser.serialize(value) : String(value);\n      return str.replaceAll(separator, encodedSeparator);\n    }).join(separator),\n    eq(a, b) {\n      if (a === b) {\n        return true;\n      }\n      if (a.length !== b.length) {\n        return false;\n      }\n      return a.every((value, index) => itemEq(value, b[index]));\n    }\n  });\n}\n\n// src/url-encoding.ts\nfunction renderQueryString(search) {\n  if (search.size === 0) {\n    return \"\";\n  }\n  const query = [];\n  for (const [key, value] of search.entries()) {\n    const safeKey = key.replace(/#/g, \"%23\").replace(/&/g, \"%26\").replace(/\\+/g, \"%2B\").replace(/=/g, \"%3D\").replace(/\\?/g, \"%3F\");\n    query.push(`${safeKey}=${encodeQueryValue(value)}`);\n  }\n  return \"?\" + query.join(\"&\");\n}\nfunction encodeQueryValue(input) {\n  return input.replace(/%/g, \"%25\").replace(/\\+/g, \"%2B\").replace(/ /g, \"+\").replace(/#/g, \"%23\").replace(/&/g, \"%26\").replace(/\"/g, \"%22\").replace(/'/g, \"%27\").replace(/`/g, \"%60\").replace(/</g, \"%3C\").replace(/>/g, \"%3E\");\n}\n\n// src/serializer.ts\nfunction createSerializer(parsers) {\n  function serialize(baseOrValues, values = {}) {\n    const [base, search] = isBase(baseOrValues) ? splitBase(baseOrValues) : [\"\", new URLSearchParams()];\n    const vals = isBase(baseOrValues) ? values : baseOrValues;\n    if (vals === null) {\n      for (const key in parsers) {\n        search.delete(key);\n      }\n      return base + renderQueryString(search);\n    }\n    for (const key in parsers) {\n      const parser = parsers[key];\n      const value = vals[key];\n      if (!parser || value === void 0) {\n        continue;\n      }\n      const isMatchingDefault = parser.defaultValue !== void 0 && (parser.eq ?? ((a, b) => a === b))(value, parser.defaultValue);\n      if (value === null || parser.clearOnDefault && isMatchingDefault) {\n        search.delete(key);\n      } else {\n        search.set(key, parser.serialize(value));\n      }\n    }\n    return base + renderQueryString(search);\n  }\n  return serialize;\n}\nfunction isBase(base) {\n  return typeof base === \"string\" || base instanceof URLSearchParams || base instanceof URL;\n}\nfunction splitBase(base) {\n  if (typeof base === \"string\") {\n    const [path = \"\", search] = base.split(\"?\");\n    return [path, new URLSearchParams(search)];\n  } else if (base instanceof URLSearchParams) {\n    return [\"\", new URLSearchParams(base)];\n  } else {\n    return [\n      base.origin + base.pathname,\n      new URLSearchParams(base.searchParams)\n    ];\n  }\n}\n\n// src/errors.ts\nvar errors = {\n  409: \"Multiple versions of the library are loaded. This may lead to unexpected behavior. Currently using `%s`, but `%s` was about to load on top.\",\n  429: \"URL update rate-limited by the browser. Consider increasing `throttleMs` for key(s) `%s`. %O\",\n  500: \"Empty search params cache. Search params can't be accessed in Layouts.\",\n  501: \"Search params cache already populated. Have you called `parse` twice?\"\n};\nfunction error(code) {\n  return `[nuqs] ${errors[code]}\n  See https://err.47ng.com/NUQS-${code}`;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-usequerystate/dist/chunk-MJO6WWF3.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-usequerystate/dist/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/next-usequerystate/dist/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createParser: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.createParser),\n/* harmony export */   createSerializer: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.createSerializer),\n/* harmony export */   parseAsArrayOf: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsArrayOf),\n/* harmony export */   parseAsBoolean: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsBoolean),\n/* harmony export */   parseAsFloat: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsFloat),\n/* harmony export */   parseAsHex: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsHex),\n/* harmony export */   parseAsInteger: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsInteger),\n/* harmony export */   parseAsIsoDateTime: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsIsoDateTime),\n/* harmony export */   parseAsJson: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsJson),\n/* harmony export */   parseAsNumberLiteral: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsNumberLiteral),\n/* harmony export */   parseAsString: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsString),\n/* harmony export */   parseAsStringEnum: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsStringEnum),\n/* harmony export */   parseAsStringLiteral: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsStringLiteral),\n/* harmony export */   parseAsTimestamp: () => (/* reexport safe */ _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsTimestamp),\n/* harmony export */   queryTypes: () => (/* binding */ queryTypes),\n/* harmony export */   subscribeToQueryUpdates: () => (/* binding */ subscribeToQueryUpdates),\n/* harmony export */   useQueryState: () => (/* binding */ useQueryState),\n/* harmony export */   useQueryStates: () => (/* binding */ useQueryStates)\n/* harmony export */ });\n/* harmony import */ var _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-MJO6WWF3.js */ \"(ssr)/./node_modules/next-usequerystate/dist/chunk-MJO6WWF3.js\");\n/* harmony import */ var mitt__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mitt */ \"(ssr)/./node_modules/mitt/dist/mitt.mjs\");\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation.js */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\n// src/deprecated.ts\nvar queryTypes = {\n  /**\n   * @deprecated use `parseAsString` instead.\n   */\n  string: _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsString,\n  /**\n   * @deprecated use `parseAsInteger` instead.\n   */\n  integer: _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsInteger,\n  /**\n   * @deprecated use `parseAsFloat` instead.\n   */\n  float: _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsFloat,\n  /**\n   * @deprecated use `parseAsBoolean` instead.\n   */\n  boolean: _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsBoolean,\n  /**\n   * @deprecated use `parseAsTimestamp` instead.\n   */\n  timestamp: _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsTimestamp,\n  /**\n   * @deprecated use `parseAsIsoDateTime` instead.\n   */\n  isoDateTime: _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsIsoDateTime,\n  /**\n   * @deprecated use `parseAsStringEnum` instead.\n   */\n  stringEnum: _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsStringEnum,\n  /**\n   * @deprecated use `parseAsJson` instead.\n   */\n  json: _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsJson,\n  /**\n   * @deprecated use `parseAsArrayOf` instead.\n   */\n  array: _chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.parseAsArrayOf\n};\n\n// src/update-queue.ts\nvar FLUSH_RATE_LIMIT_MS = (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.getDefaultThrottle)();\nvar updateQueue = /* @__PURE__ */ new Map();\nvar queueOptions = {\n  history: \"replace\",\n  scroll: false,\n  shallow: true,\n  throttleMs: FLUSH_RATE_LIMIT_MS\n};\nvar transitionsQueue = /* @__PURE__ */ new Set();\nvar lastFlushTimestamp = 0;\nvar flushPromiseCache = null;\nfunction enqueueQueryStringUpdate(key, value, serialize, options) {\n  const serializedOrNull = value === null ? null : serialize(value);\n  (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuqs queue] Enqueueing %s=%s %O\", key, serializedOrNull, options);\n  updateQueue.set(key, serializedOrNull);\n  if (options.history === \"push\") {\n    queueOptions.history = \"push\";\n  }\n  if (options.scroll) {\n    queueOptions.scroll = true;\n  }\n  if (options.shallow === false) {\n    queueOptions.shallow = false;\n  }\n  if (options.startTransition) {\n    transitionsQueue.add(options.startTransition);\n    queueOptions.shallow = false;\n  }\n  queueOptions.throttleMs = Math.max(\n    options.throttleMs ?? FLUSH_RATE_LIMIT_MS,\n    Number.isFinite(queueOptions.throttleMs) ? queueOptions.throttleMs : 0\n  );\n  return serializedOrNull;\n}\nfunction getQueuedValue(key) {\n  return updateQueue.get(key) ?? null;\n}\nfunction scheduleFlushToURL(router) {\n  if (flushPromiseCache === null) {\n    flushPromiseCache = new Promise((resolve, reject) => {\n      if (!Number.isFinite(queueOptions.throttleMs)) {\n        (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuqs queue] Skipping flush due to throttleMs=Infinity\");\n        resolve(new URLSearchParams(location.search));\n        setTimeout(() => {\n          flushPromiseCache = null;\n        }, 0);\n        return;\n      }\n      function flushNow() {\n        lastFlushTimestamp = performance.now();\n        const [search, error2] = flushUpdateQueue(router);\n        if (error2 === null) {\n          resolve(search);\n        } else {\n          reject(search);\n        }\n        flushPromiseCache = null;\n      }\n      function runOnNextTick() {\n        const now = performance.now();\n        const timeSinceLastFlush = now - lastFlushTimestamp;\n        const throttleMs = queueOptions.throttleMs;\n        const flushInMs = Math.max(\n          0,\n          Math.min(throttleMs, throttleMs - timeSinceLastFlush)\n        );\n        (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\n          \"[nuqs queue] Scheduling flush in %f ms. Throttled at %f ms\",\n          flushInMs,\n          throttleMs\n        );\n        if (flushInMs === 0) {\n          flushNow();\n        } else {\n          setTimeout(flushNow, flushInMs);\n        }\n      }\n      setTimeout(runOnNextTick, 0);\n    });\n  }\n  return flushPromiseCache;\n}\nfunction flushUpdateQueue(router) {\n  const search = new URLSearchParams(location.search);\n  if (updateQueue.size === 0) {\n    return [search, null];\n  }\n  const items = Array.from(updateQueue.entries());\n  const options = { ...queueOptions };\n  const transitions = Array.from(transitionsQueue);\n  updateQueue.clear();\n  transitionsQueue.clear();\n  queueOptions.history = \"replace\";\n  queueOptions.scroll = false;\n  queueOptions.shallow = true;\n  queueOptions.throttleMs = FLUSH_RATE_LIMIT_MS;\n  (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuqs queue] Flushing queue %O with options %O\", items, options);\n  for (const [key, value] of items) {\n    if (value === null) {\n      search.delete(key);\n    } else {\n      search.set(key, value);\n    }\n  }\n  try {\n    const nextRouter = window.next?.router;\n    const isPagesRouter = typeof nextRouter?.state?.asPath === \"string\";\n    if (isPagesRouter) {\n      const url = renderURL(nextRouter.state.asPath.split(\"?\")[0] ?? \"\", search);\n      (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuqs queue (pages)] Updating url: %s\", url);\n      const method = options.history === \"push\" ? nextRouter.push : nextRouter.replace;\n      method.call(nextRouter, url, url, {\n        scroll: options.scroll,\n        shallow: options.shallow\n      });\n    } else {\n      const url = renderURL(location.origin + location.pathname, search);\n      (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuqs queue (app)] Updating url: %s\", url);\n      const updateMethod = options.history === \"push\" ? history.pushState : history.replaceState;\n      const state = (window.next?.version ?? \"\") >= \"14.1.0\" ? null : history.state;\n      updateMethod.call(\n        history,\n        state,\n        // Our own updates have a marker to prevent syncing\n        // when the URL changes (we've already sync'd them up\n        // via `emitter.emit(key, newValue)` above, without\n        // going through the parsers).\n        NOSYNC_MARKER,\n        url\n      );\n      if (options.scroll) {\n        window.scrollTo(0, 0);\n      }\n      if (!options.shallow) {\n        compose(transitions, () => {\n          router.replace(url, {\n            scroll: false\n          });\n        });\n      }\n    }\n    return [search, null];\n  } catch (err) {\n    console.error((0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.error)(429), items.map(([key]) => key).join(), err);\n    return [search, err];\n  }\n}\nfunction renderURL(base, search) {\n  const hashlessBase = base.split(\"#\")[0] ?? \"\";\n  const query = (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.renderQueryString)(search);\n  const hash = location.hash;\n  return hashlessBase + query + hash;\n}\nfunction compose(fns, final) {\n  const recursiveCompose = (index) => {\n    if (index === fns.length) {\n      return final();\n    }\n    const fn = fns[index];\n    if (!fn) {\n      throw new Error(\"Invalid transition function\");\n    }\n    fn(() => recursiveCompose(index + 1));\n  };\n  recursiveCompose(0);\n}\n\n// src/sync.ts\nvar SYNC_EVENT_KEY = Symbol(\"__nuqs__SYNC__\");\nvar NOSYNC_MARKER = \"__nuqs__NO_SYNC__\";\nvar NOTIFY_EVENT_KEY = Symbol(\"__nuqs__NOTIFY__\");\nvar emitter = (0,mitt__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\nfunction subscribeToQueryUpdates(callback) {\n  emitter.on(NOTIFY_EVENT_KEY, callback);\n  return () => emitter.off(NOTIFY_EVENT_KEY, callback);\n}\nif (typeof history === \"object\") {\n  patchHistory();\n}\nfunction patchHistory() {\n  const version = \"1.20.0\";\n  const patched = history.__nuqs_patched;\n  if (patched) {\n    if (patched !== version) {\n      console.error((0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.error)(409), patched, version);\n    }\n    return;\n  }\n  (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuqs] Patching history with %s\", version);\n  for (const method of [\"pushState\", \"replaceState\"]) {\n    const original = history[method].bind(history);\n    history[method] = function nuqs_patchedHistory(state, title, url) {\n      if (!url) {\n        (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuqs] history.%s(null) (%s) %O\", method, title, state);\n        return original(state, title, url);\n      }\n      const source = title === NOSYNC_MARKER ? \"internal\" : \"external\";\n      const search = new URL(url, location.origin).searchParams;\n      (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuqs] history.%s(%s) (%s) %O\", method, url, source, state);\n      if (source === \"external\") {\n        for (const [key, value] of search.entries()) {\n          const queueValue = getQueuedValue(key);\n          if (queueValue !== null && queueValue !== value) {\n            (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\n              \"[nuqs] Overwrite detected for key: %s, Server: %s, queue: %s\",\n              key,\n              value,\n              queueValue\n            );\n            search.set(key, queueValue);\n          }\n        }\n        setTimeout(() => {\n          (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\n            \"[nuqs] External history.%s call: triggering sync with %s\",\n            method,\n            search\n          );\n          emitter.emit(SYNC_EVENT_KEY, search);\n          emitter.emit(NOTIFY_EVENT_KEY, { search, source });\n        }, 0);\n      } else {\n        setTimeout(() => {\n          emitter.emit(NOTIFY_EVENT_KEY, { search, source });\n        }, 0);\n      }\n      return original(state, title === NOSYNC_MARKER ? \"\" : title, url);\n    };\n  }\n  Object.defineProperty(history, \"__nuqs_patched\", {\n    value: version,\n    writable: false,\n    enumerable: false,\n    configurable: false\n  });\n}\nfunction useQueryState(key, {\n  history: history2 = \"replace\",\n  shallow = true,\n  scroll = false,\n  throttleMs = FLUSH_RATE_LIMIT_MS,\n  parse = (x) => x,\n  serialize = String,\n  eq = (a, b) => a === b,\n  defaultValue = void 0,\n  clearOnDefault = false,\n  startTransition\n} = {\n  history: \"replace\",\n  scroll: false,\n  shallow: true,\n  throttleMs: FLUSH_RATE_LIMIT_MS,\n  parse: (x) => x,\n  serialize: String,\n  eq: (a, b) => a === b,\n  clearOnDefault: false,\n  defaultValue: void 0\n}) {\n  const router = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n  const initialSearchParams = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n  const queryRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef(null);\n  const [internalState, setInternalState] = react__WEBPACK_IMPORTED_MODULE_3__.useState(() => {\n    const queueValue = getQueuedValue(key);\n    const urlValue = initialSearchParams?.get(key) ?? null;\n    const value = queueValue ?? urlValue;\n    queryRef.current = value;\n    return value === null ? null : (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.safeParse)(parse, value, key);\n  });\n  const stateRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef(internalState);\n  (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\n    \"[nuqs `%s`] render - state: %O, iSP: %s\",\n    key,\n    internalState,\n    initialSearchParams?.get(key) ?? null\n  );\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(() => {\n    if (window.next?.version !== \"14.0.3\") {\n      return;\n    }\n    const query = initialSearchParams.get(key) ?? null;\n    if (query === queryRef.current) {\n      return;\n    }\n    const state = query === null ? null : (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.safeParse)(parse, query, key);\n    (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuqs `%s`] syncFromUseSearchParams %O\", key, state);\n    stateRef.current = state;\n    queryRef.current = query;\n    setInternalState(state);\n  }, [initialSearchParams?.get(key), key]);\n  react__WEBPACK_IMPORTED_MODULE_3__.useInsertionEffect(() => {\n    function updateInternalState({ state, query }) {\n      (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuqs `%s`] updateInternalState %O\", key, state);\n      stateRef.current = state;\n      queryRef.current = query;\n      setInternalState(state);\n    }\n    function syncFromURL(search) {\n      const query = search.get(key);\n      if (query === queryRef.current) {\n        return;\n      }\n      const state = query === null ? null : (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.safeParse)(parse, query, key);\n      (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuqs `%s`] syncFromURL %O\", key, state);\n      updateInternalState({ state, query });\n    }\n    (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuqs `%s`] subscribing to sync\", key);\n    emitter.on(SYNC_EVENT_KEY, syncFromURL);\n    emitter.on(key, updateInternalState);\n    return () => {\n      (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuqs `%s`] unsubscribing from sync\", key);\n      emitter.off(SYNC_EVENT_KEY, syncFromURL);\n      emitter.off(key, updateInternalState);\n    };\n  }, [key]);\n  const update = react__WEBPACK_IMPORTED_MODULE_3__.useCallback(\n    (stateUpdater, options = {}) => {\n      let newValue = isUpdaterFunction(stateUpdater) ? stateUpdater(stateRef.current ?? defaultValue ?? null) : stateUpdater;\n      if ((options.clearOnDefault ?? clearOnDefault) && newValue !== null && defaultValue !== void 0 && eq(newValue, defaultValue)) {\n        newValue = null;\n      }\n      queryRef.current = enqueueQueryStringUpdate(key, newValue, serialize, {\n        // Call-level options take precedence over hook declaration options.\n        history: options.history ?? history2,\n        shallow: options.shallow ?? shallow,\n        scroll: options.scroll ?? scroll,\n        throttleMs: options.throttleMs ?? throttleMs,\n        startTransition: options.startTransition ?? startTransition\n      });\n      emitter.emit(key, { state: newValue, query: queryRef.current });\n      return scheduleFlushToURL(router);\n    },\n    [key, history2, shallow, scroll, throttleMs, startTransition]\n  );\n  return [internalState ?? defaultValue ?? null, update];\n}\nfunction isUpdaterFunction(stateUpdater) {\n  return typeof stateUpdater === \"function\";\n}\nvar defaultUrlKeys = {};\nfunction useQueryStates(keyMap, {\n  history: history2 = \"replace\",\n  scroll = false,\n  shallow = true,\n  throttleMs = FLUSH_RATE_LIMIT_MS,\n  clearOnDefault = false,\n  startTransition,\n  urlKeys = defaultUrlKeys\n} = {}) {\n  const stateKeys = Object.keys(keyMap).join(\",\");\n  const resolvedUrlKeys = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(\n    () => Object.fromEntries(\n      Object.keys(keyMap).map((key) => [key, urlKeys[key] ?? key])\n    ),\n    [stateKeys, urlKeys]\n  );\n  const router = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n  const initialSearchParams = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n  const queryRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef({});\n  const [internalState, setInternalState] = react__WEBPACK_IMPORTED_MODULE_3__.useState(() => {\n    const source = initialSearchParams ?? new URLSearchParams();\n    queryRef.current = Object.fromEntries(source.entries());\n    return parseMap(keyMap, urlKeys, source);\n  });\n  const stateRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef(internalState);\n  (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\n    \"[nuq+ `%s`] render - state: %O, iSP: %s\",\n    stateKeys,\n    internalState,\n    initialSearchParams\n  );\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(() => {\n    if (window.next?.version !== \"14.0.3\") {\n      return;\n    }\n    const state = parseMap(\n      keyMap,\n      urlKeys,\n      initialSearchParams,\n      queryRef.current,\n      stateRef.current\n    );\n    setInternalState(state);\n  }, [\n    Object.keys(resolvedUrlKeys).map((key) => initialSearchParams?.get(key)).join(\"&\"),\n    stateKeys\n  ]);\n  react__WEBPACK_IMPORTED_MODULE_3__.useInsertionEffect(() => {\n    function updateInternalState(state) {\n      (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuq+ `%s`] updateInternalState %O\", stateKeys, state);\n      stateRef.current = state;\n      setInternalState(state);\n    }\n    function syncFromURL(search) {\n      const state = parseMap(\n        keyMap,\n        urlKeys,\n        search,\n        queryRef.current,\n        stateRef.current\n      );\n      (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuq+ `%s`] syncFromURL %O\", stateKeys, state);\n      updateInternalState(state);\n    }\n    const handlers = Object.keys(keyMap).reduce(\n      (handlers2, stateKey) => {\n        handlers2[stateKey] = ({\n          state,\n          query\n        }) => {\n          const { defaultValue } = keyMap[stateKey];\n          const urlKey = resolvedUrlKeys[stateKey];\n          stateRef.current = {\n            ...stateRef.current,\n            [stateKey]: state ?? defaultValue ?? null\n          };\n          queryRef.current[urlKey] = query;\n          (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\n            \"[nuq+ `%s`] Cross-hook key sync %s: %O (default: %O). Resolved: %O\",\n            stateKeys,\n            urlKey,\n            state,\n            defaultValue,\n            stateRef.current\n          );\n          updateInternalState(stateRef.current);\n        };\n        return handlers2;\n      },\n      {}\n    );\n    emitter.on(SYNC_EVENT_KEY, syncFromURL);\n    for (const stateKey of Object.keys(keyMap)) {\n      const urlKey = resolvedUrlKeys[stateKey];\n      (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuq+ `%s`] Subscribing to sync for `%s`\", stateKeys, urlKey);\n      emitter.on(urlKey, handlers[stateKey]);\n    }\n    return () => {\n      emitter.off(SYNC_EVENT_KEY, syncFromURL);\n      for (const stateKey of Object.keys(keyMap)) {\n        const urlKey = resolvedUrlKeys[stateKey];\n        (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuq+ `%s`] Unsubscribing to sync for `%s`\", stateKeys, urlKey);\n        emitter.off(urlKey, handlers[stateKey]);\n      }\n    };\n  }, [keyMap, resolvedUrlKeys]);\n  const update = react__WEBPACK_IMPORTED_MODULE_3__.useCallback(\n    (stateUpdater, callOptions = {}) => {\n      const newState = typeof stateUpdater === \"function\" ? stateUpdater(stateRef.current) : stateUpdater === null ? Object.fromEntries(\n        Object.keys(keyMap).map((key) => [key, null])\n      ) : stateUpdater;\n      (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.debug)(\"[nuq+ `%s`] setState: %O\", stateKeys, newState);\n      for (let [stateKey, value] of Object.entries(newState)) {\n        const parser = keyMap[stateKey];\n        const urlKey = resolvedUrlKeys[stateKey];\n        if (!parser) {\n          continue;\n        }\n        if ((callOptions.clearOnDefault ?? parser.clearOnDefault ?? clearOnDefault) && value !== null && parser.defaultValue !== void 0 && (parser.eq ?? ((a, b) => a === b))(value, parser.defaultValue)) {\n          value = null;\n        }\n        queryRef.current[urlKey] = enqueueQueryStringUpdate(\n          urlKey,\n          value,\n          parser.serialize ?? String,\n          {\n            // Call-level options take precedence over individual parser options\n            // which take precedence over global options\n            history: callOptions.history ?? parser.history ?? history2,\n            shallow: callOptions.shallow ?? parser.shallow ?? shallow,\n            scroll: callOptions.scroll ?? parser.scroll ?? scroll,\n            throttleMs: callOptions.throttleMs ?? parser.throttleMs ?? throttleMs,\n            startTransition: callOptions.startTransition ?? parser.startTransition ?? startTransition\n          }\n        );\n        emitter.emit(urlKey, {\n          state: value,\n          query: queryRef.current[urlKey] ?? null\n        });\n      }\n      return scheduleFlushToURL(router);\n    },\n    [\n      keyMap,\n      history2,\n      shallow,\n      scroll,\n      throttleMs,\n      startTransition,\n      resolvedUrlKeys\n    ]\n  );\n  return [internalState, update];\n}\nfunction parseMap(keyMap, urlKeys, searchParams, cachedQuery, cachedState) {\n  return Object.keys(keyMap).reduce((obj, stateKey) => {\n    const urlKey = urlKeys?.[stateKey] ?? stateKey;\n    const { defaultValue, parse } = keyMap[stateKey];\n    const urlQuery = searchParams?.get(urlKey) ?? null;\n    const queueQuery = getQueuedValue(urlKey);\n    const query = queueQuery ?? urlQuery;\n    if (cachedQuery && cachedState && cachedQuery[urlKey] === query) {\n      obj[stateKey] = cachedState[stateKey] ?? defaultValue ?? null;\n      return obj;\n    }\n    const value = query === null ? null : (0,_chunk_MJO6WWF3_js__WEBPACK_IMPORTED_MODULE_0__.safeParse)(parse, query, stateKey);\n    obj[stateKey] = value ?? defaultValue ?? null;\n    if (cachedQuery) {\n      cachedQuery[urlKey] = query;\n    }\n    return obj;\n  }, {});\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-usequerystate/dist/index.js\n");

/***/ })

};
;