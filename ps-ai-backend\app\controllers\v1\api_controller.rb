# frozen_string_literal: true

module V1
  class ApiController < ActionController::API
    include Response
    include ExceptionHandler
    include ActionController::MimeResponds

    before_action :set_host_for_local_storage

    def self.authorize_auth_token!(*options)
      use AuthTokenMiddleware, *options
    end

    def user_id(id)
      id.to_s.casecmp('me').zero? ? current_user.id : id.to_i
    end

    private

    def query_params
      @query_params ||= request.query_parameters.with_indifferent_access
    end

    def request_body
      @request_body ||= request.request_parameters.symbolize_keys
    end

    def referer
      request.referer || ''
    end    

    def current_user
      @current_user = Current.user[:user]
    rescue StandardError
      @current_user = nil
    end

    def current_user_data
      @current_user_data = Current.user
    rescue StandardError
      @current_user_data = nil
    end

    def auth_token
      request.env['psai.auth_token']
    end

    def validate!(model, on_error = {})
      halt! render_json(model, ErrorOutput, **on_error) unless Array(model).all?(&:valid?)
      model
    end

    def halt!(*_)
      throw(:halt_controller)
    end

    def catch_halt(&block)
      catch(:halt_controller, &block)
    end

    def set_host_for_local_storage
      return unless [:test, :local].include? Rails.application.config.active_storage.service

      ActiveStorage::Current.url_options = request.base_url
    end

    around_action :catch_halt
  end
end
