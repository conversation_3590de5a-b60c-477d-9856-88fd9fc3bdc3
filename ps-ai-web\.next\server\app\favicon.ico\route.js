"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/favicon.ico/route";
exports.ids = ["app/favicon.ico/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_metadata_route_loader_page_2Ffavicon_ico_2Froute_filePath_C_3A_5CUsers_5CACER_5CDesktop_5Cps_full_stack_5Cps_ai_web_5Csrc_5Capp_5Cfavicon_ico_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?page=%2Ffavicon.ico%2Froute&filePath=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web%5Csrc%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__ */ \"(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web%5Csrc%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/favicon.ico/route\",\n        pathname: \"/favicon.ico\",\n        filename: \"favicon\",\n        bundlePath: \"app/favicon.ico/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?page=%2Ffavicon.ico%2Froute&filePath=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web%5Csrc%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_page_2Ffavicon_ico_2Froute_filePath_C_3A_5CUsers_5CACER_5CDesktop_5Cps_full_stack_5Cps_ai_web_5Csrc_5Capp_5Cfavicon_ico_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/favicon.ico/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web%5Csrc%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web%5Csrc%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__ ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(app-metadata-route)/./node_modules/next/dist/api/server.js\");\n/* static asset route */\n\n\nconst contentType = \"image/x-icon\"\nconst buffer = Buffer.from(\"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\", 'base64'\n  )\n\nfunction GET() {\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"public, max-age=0, must-revalidate\",\n    },\n  })\n}\n\nconst dynamic = 'force-static'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&filePath=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web%5Csrc%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CACER%5CDesktop%5Cps-full-stack%5Cps-ai-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();