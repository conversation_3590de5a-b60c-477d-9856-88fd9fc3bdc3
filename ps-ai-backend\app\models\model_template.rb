# frozen_string_literal: true

class ModelTemplate < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  enum model: string_enum('gpt-4o', 'gpt-4o-mini')
  enum status: string_enum('active', 'inactive')

  belongs_to :organization, optional: true
  belongs_to :account_plan_group, optional: true
  belongs_to :template_category, optional: true

  belongs_to :creator_organization_user,
             optional: true,
             class_name: 'OrganizationUser',
             foreign_key: 'creator_organization_user_id'

  belongs_to :last_updated_by_organization_user,
             optional: true,
             class_name: 'OrganizationUser',
             foreign_key: 'last_updated_by_organization_user_id'


  def self.create_templates
    category_list = TemplateCategory.global_categories
    tcs = TemplateCategory.where(name: category_list, organization_id: nil, general_category: true)

    if tcs.size != category_list.size
      tcs = TemplateCategory.populate_global_categories
    end

    tcs.each do |tc|
      # create empty template
      version = tc.name.split.map do |word|
        in_bracket_string = word.scan(/\(([^()]*)\)/)
        if in_bracket_string.present?
          " #{in_bracket_string.flatten.first}"
        else
          word.first
        end
      end.join.upcase

      mt = ModelTemplate.find_by(
        organization_id: nil,
        template_category_id: tc.id,
        status: 'active',
        account_plan_group_id: nil
      )

      template_created = false
      if mt.blank?
        mt = ModelTemplate.create(
          name: version,
          organization_id: nil,
          template_category_id: tc.id,
          status: 'active',
          account_plan_group_id: nil,
          model: 'gpt-4o-mini'
        )
        template_created = true
      else
        mt.update(
          name: version
        )
      end

      # populate template via jobs
      if template_created
        UpdateAutoGeneratedModelTemplateJob.perform_later(
          mt,
          tc
        )
      end
    end
  end

  def self.valid_inputs
    [
      'industry',
      'location',
      'company',
      'currency',
      'account_addressable_area'
    ]
  end
end
