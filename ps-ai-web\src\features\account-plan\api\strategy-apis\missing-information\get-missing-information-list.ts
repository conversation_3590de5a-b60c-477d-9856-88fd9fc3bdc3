import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APMissingInformation } from "@/features/account-plan/types/strategy-types";

type MissingInformationtListParams = BaseParams;

export const getMissingInformationList = ({
  accountId,
  params,
}: {
  accountId: number;
  params?: MissingInformationtListParams;
}): ApiResponse<APMissingInformation[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_MISSING_INFORMATION(accountId), {
    params,
  });
};

export const getMissingInformationListQueryOptions = (
  accountId: number,
  params?: MissingInformationtListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_MISSING_INFORMATION,
    ],
    queryFn: () => getMissingInformationList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseMissingInformationListOptions = {
  params?: MissingInformationtListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getMissingInformationList>;
  options?: Partial<ReturnType<typeof getMissingInformationListQueryOptions>>;
};

export const useMissingInformationList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UseMissingInformationListOptions) => {
  const missingInformationListQuery = useQuery({
    ...getMissingInformationListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...missingInformationListQuery,
    missingInformationList: missingInformationListQuery.data?.data,
  };
};
