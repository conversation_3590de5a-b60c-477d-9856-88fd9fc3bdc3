/* eslint-disable react/no-children-prop */

import React, { useMemo } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import rehypeReact from "rehype-react";
import { cn } from "@/lib/utils";

type MarkdownProps = React.HTMLAttributes<HTMLDivElement> & {
  markdown: string;
  highlightedWords?: string[];
};

const Markdown: React.FC<MarkdownProps> = ({ markdown, className }) => {
  const formattedMarkdown = useMemo(() => {
    return markdown.replace(/\n/g, "<br />");
  }, [markdown]);

  return (
    <article
      className={cn(
        "prose:first:mt-0 prose prose-sm dark:prose-invert prose-pre:p-0 max-w-full grow overflow-x-hidden",
        className
      )}
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeReact, rehypeRaw]}
        children={formattedMarkdown}
        components={{
          ol: ({ children }) => (
            <ol className="my-res-y-sm list-inside list-decimal">{children}</ol>
          ),
          li: ({ children }) => (
            <li className="mb-res-y-sm [&>p]:inline">{children}</li>
          ),
          ul: ({ children }) => (
            <ul className="mb-8 list-inside !list-disc">{children}</ul>
          ),
          pre: ({ children }) => (
            <pre className="w-full grow overflow-x-auto !p-4">{children}</pre>
          ),
        }}
      />
    </article>
  );
};

export default Markdown;
