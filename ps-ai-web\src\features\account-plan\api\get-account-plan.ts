import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { AccountPlanData } from "../types";

export const getAccountPlan = ({
  accountId,
}: {
  accountId: number;
}): ApiResponse<AccountPlanData> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_DETAIL(accountId));
};

export const getAccountPlanQueryOptions = (accountId: number) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.ACCOUNT_PLANS, accountId],
    queryFn: () => getAccountPlan({ accountId }),
    enabled: !!accountId,
  });
};

type UseAccountPlanOptions = {
  accountId: number;
  queryConfig?: QueryConfig<typeof getAccountPlan>;
  options?: Partial<ReturnType<typeof getAccountPlanQueryOptions>>;
};

export const useAccountPlan = ({
  accountId,
  queryConfig,
  options,
}: UseAccountPlanOptions) => {
  const accountPlanQuery = useQuery({
    ...getAccountPlanQueryOptions(accountId),
    ...queryConfig,
    ...options,
  });

  return {
    ...accountPlanQuery,
    accountPlan: accountPlanQuery.data?.data,
  };
};
