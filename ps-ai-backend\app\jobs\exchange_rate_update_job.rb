class ExchangeRateUpdateJob < ApplicationJob
  queue_as :default

  def perform(*args)
    current_time = Time.current

    ActiveRecord::Base.transaction do
      url = "#{ENV['EXCHANGERATE_HOSTNAME']}/#{ENV['EXCHANGERATE_ACCESS_KEY']}/latest/#{ENV['EXCHANGERATE_BASE_CURRENCY']}"
      response = RestClient.get(url, {})
      data = JSON.parse(response.body)

      base = data['base_code']
      rates = data['conversion_rates']
      rates_keys = rates.keys

      rates_keys.each do |k|
        curr_rate = rates[k]

        obj = CurrencyExchangeRate.find_or_create_by!(base_currency: base, exchange_currency: k)

        decimal = curr_rate.to_i
        fraction = (curr_rate - decimal).to_s

        obj.update(rates: decimal, rates_decimal: fraction)
      end
    end
  end
end
