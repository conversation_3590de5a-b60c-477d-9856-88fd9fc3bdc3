# frozen_string_literal: true

module <PERSON>ionH<PERSON>ler
  extend ActiveSupport::Concern

  class AuthenticationError < StandardError; end

  class Unauthorized < StandardError; end

  class MissingToken < StandardError; end

  class InvalidToken < StandardError; end

  class NotFound < StandardError; end

  included do
    rescue_from ActiveRecord::ConnectionNotEstablished, with: :handle_database_error
    rescue_from ActiveRecord::StatementInvalid, with: :handle_database_error
    rescue_from PG::ConnectionBad, with: :handle_database_error

    rescue_from StandardError, SystemStackError do |e|
      log_error(e)
      render_json e, ExceptionOutput,
                  debug: Rails.env != 'production',
                  namespace: controller_path
    end
  end

  private

  def handle_database_error(e)
    log_error(e)
    render json: { error: 'Database temporarily unavailable. Please try again later.' }, status: :internal_server_error
  end


  def log_error(e)
    backtrace_cleaner = request.get_header 'action_dispatch.backtrace_cleaner'
    wrapper = ActionDispatch::ExceptionWrapper
    annotate = :annoted_source_code
    message = "\n#{e.class} (#{e.message}):\n"
    message << e.public_send(annotate).to_s if e.respond_to?(annotate)
    message << '  '
    message << wrapper.new(backtrace_cleaner, e).application_trace.join("\n  ")
    message << "\n\n"
    logger.error(message)
  end
end
