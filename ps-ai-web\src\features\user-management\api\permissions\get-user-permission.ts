import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { UserPermissions } from "../../types";

export const getUserPermissions = ({
  userId,
}: {
  userId: number;
}): ApiResponse<UserPermissions> => {
  return api.get(API_ROUTES.USER_MANAGEMENTS_USER_PERMISSIONS_DETAIL(userId));
};

export const getUserPermissionsQueryOptions = (userId: number) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.USER_PERMISSIONS, userId],
    queryFn: () => getUserPermissions({ userId }),
    enabled: !!userId,
  });
};

type UseUserPermissionsOptions = {
  userId: number;
  queryConfig?: QueryConfig<typeof getUserPermissions>;
  options?: Partial<ReturnType<typeof getUserPermissionsQueryOptions>>;
};

export const useUserPermissions = ({
  userId,
  queryConfig,
  options,
}: UseUserPermissionsOptions) => {
  const userPermissionsQuery = useQuery({
    ...getUserPermissionsQueryOptions(userId),
    ...queryConfig,
    ...options,
  });

  return {
    ...userPermissionsQuery,
    userPermissions: userPermissionsQuery.data?.data,
  };
};
