# frozen_string_literal: true

class S3Service < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
  end

  def upload_s3(params)
    directory = params['directory']
    extension = params['extension']
    filename = params['filename']
    acl = params['acl']
    bucket_params = {
      acl: acl
    }.compact

    bucket_name = ENV['S3_BUCKET']
    s3 = Aws::S3::Resource.new
    bucket = s3.bucket(bucket_name)

    path_result = build_path(directory, extension, filename)

    presigned_url = bucket.object(path_result[:path]).presigned_url(:put, **bucket_params)

    result = {
      path: path_result[:path],
      presigned_url: presigned_url,
      download_url: presigned_url.sub(/\?.*/, ''),
      filename: path_result[:filename]
    }
    
  end

  private

  def build_path(directory, extension, filename)
    hex_random = SecureRandom.hex(32)

    new_filename = filename.split('.')

    if new_filename.size == 1
      new_filename = new_filename.first.gsub(' ', '_') + '-' + hex_random + '.' + extension
    else
      length = new_filename.size
      new_filename = new_filename[..length-2].join('.').gsub(' ', '_') + '-' + hex_random + '.' + extension
    end

    {
      filename: new_filename,
      path: directory.present? ? directory + '/' + new_filename : new_filename
    }
  end
end
