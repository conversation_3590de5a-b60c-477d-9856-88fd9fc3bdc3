# frozen_string_literal: true

class AccountPlanService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
    @openai_service = OpenaiService.new(user_data)
    @user_data = user_data
  end

  def create(params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    # verify_roles(['owner', 'super_admin'], organization_user)

    owner_user_id = params.delete(:owner_user_id)
    params[:organization_id] = organization_user.organization_id

    owner_organization_user = OrganizationUser.find_by(
      user_id: owner_user_id,
      organization_id: organization_user.organization_id
    )
    if owner_user_id.present?
      authorize! owner_organization_user.present?, on_error: 'User does not exist in current organization'
      params[:owner_organization_user_id] = owner_organization_user.id
    else
      params[:owner_organization_user_id] = organization_user.id
    end

    params[:last_updated_by_organization_user_id] = organization_user.id

    account_plan = AccountPlan.new

    ActiveRecord::Base.transaction do
      if params[:status] == 'active'
        AccountPlan.where(account_plan_group_id: params[:account_plan_group_id]).update_all(status: 'inactive')
      else
        params[:status] = 'inactive'
      end
      account_plan = AccountPlan.create!(params)
    end

    OpenStruct.new(
      account_plan: account_plan,
      organization_users: [account_plan.owner_organization_user, account_plan.last_updated_by_organization_user].compact.uniq
    )
  end

  def update(id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    # verify_roles(['owner', 'super_admin'], organization_user)

    account_plan = AccountPlan.find(id)

    params.delete(:organization_id)
    owner_user_id = params.delete(:owner_user_id)
    authorize! organization_user.organization_id == account_plan.organization_id, on_error: 'Not an organization account plan'
    
    if owner_user_id.present?
      owner_organization_user = OrganizationUser.find_by(
        user_id: owner_user_id,
        organization_id: organization_user.organization_id
      )
      authorize! owner_organization_user.present?, on_error: 'User does not exist in current organization'
      params[:owner_organization_user_id] = owner_organization_user.id
    end

    params[:last_updated_by_organization_user_id] = organization_user.id

    ActiveRecord::Base.transaction do
      analysis = params.delete(:generate_analysis)

      account_plan.update!(params)
      account_plan.reload

      if account_plan.status == 'active'
        AccountPlan
          .where(account_plan_group_id: account_plan.account_plan_group_id)
          .where.not(id: account_plan.id)
          .update_all(status: 'inactive')
      end

      if as_boolean(analysis)
        current_time = Time.current

        # ApTable.ai_response_tables.each do |table_type|
        #   # get ap table
        #   ap_table = ApTable.find_or_create_by(
        #     table_category: 'strategy',
        #     table_type: table_type,
        #     account_plan_id: account_plan.id
        #   )

        #   generate_strategic_analysis(@organization, account_plan, ap_table, table_type)
        # end

        account_plan.update(
          generated_analysis: true,
          last_generated_analysis_at: current_time
          # review_date: current_time
        )

        # NOT NEEDED FOR NOW
        # if account_plan.status == 'active'
        #   GenerateLlmConsiderationApChartsJob.perform_later(
        #     @user_data,
        #     account_plan.id,
        #   )
        # end
      end
    end

    OpenStruct.new(
      account_plan: account_plan,
      account_plan_groups: [account_plan.account_plan_group].compact,
      organization_users: [account_plan.owner_organization_user, account_plan.last_updated_by_organization_user].compact.uniq
    )
  end

  def index(query_params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)

    account_plans = ::AccountPlans.new

    filter = query_params.slice(
      :search, :plan_date, :review_date, :next_review_date, :status,
      :page, :per_page, :disable_pagination
    )
    filter = filter.merge(
      organization_id: organization_user.organization_id
    )

    filtered = account_plans.filter(filter)

    OpenStruct.new(
      account_plans: filtered,
      account_plan_groups: AccountPlanGroup.where(id: filtered.pluck(:account_plan_group_id).compact.uniq),
      organization_users: OrganizationUser.where(
        id: (filtered.pluck(:owner_organization_user_id).compact.uniq + filtered.pluck(:last_updated_by_organization_user_id).compact.uniq)
      )
    )
  end

  def show(id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)

    account_plan = AccountPlan.find(id)
    authorize! organization_user.organization_id == account_plan.organization_id, on_error: 'Not an organization account plan'
    
    OpenStruct.new(
      account_plan: account_plan,
      account_plan_groups: [account_plan.account_plan_group].compact,
      organization_users: [account_plan.owner_organization_user, account_plan.last_updated_by_organization_user].compact.uniq
    )
  end

  def destroy(id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    # verify_roles(['owner', 'super_admin'], organization_user)

    account_plan = AccountPlan.find(id)
    authorize! organization_user.organization_id == account_plan.organization_id, on_error: 'Not an organization account plan'

    ActiveRecord::Base.transaction do
      if account_plan.status == 'active'
        plans = AccountPlan
          .where(account_plan_group_id: account_plan.account_plan_group_id)
          .where.not(id: account_plan.id)
          .order(updated_at: :desc)

        plans.update_all(status: 'inactive')

        new_active_plan = plans.first
        new_active_plan.update(status: 'active')
      end

      account_plan.update(
        discarded_at: Time.current,
        last_updated_by_organization_user_id: organization_user.id
      )
    end
  end

  def active_top_actions
    organization_user = verify_user_organization(@user, @organization_user, @organization)

    active_account_plans = AccountPlan
                            .select('account_plans.id AS id, apg.account_plan_unique_id AS unique_id, apg.company AS company_name')
                            .joins('LEFT JOIN account_plan_groups apg ON apg.id = account_plans.account_plan_group_id')
                            .where(organization_id: organization_user.organization_id, status: 'active')
    
    ap_tables = ApTable.where(
      table_category: 'strategy',
      table_type: 'top_action',
      account_plan_id: active_account_plans.collect(&:id)
    )

    top_actions = ::AccountPlanTableItems::ApTopActionItem.where(
      ap_table_id: ap_tables.ids
    )

    OpenStruct.new(
      active_account_plans: active_account_plans,
      ap_tables: ap_tables,
      top_actions: top_actions
    )
  end

  def generate_strategic_analysis(organization, account_plan, ap_table, table_type)
    category_str = TemplateCategory.ap_table_map_to_categories[table_type]
    template_category_ids = TemplateCategory.where(name: category_str).ids
    authorize! template_category_ids.present?, on_error: 'Template invalid, please contact administrator!'

    if organization.tier == 'premium'
      active_template = ModelTemplate
      .where(
        organization_id: organization.id,
        status: 'active',
        template_category_id: template_category_ids
      )
      .last
    end
    active_template ||= ModelTemplate
      .where(
        organization_id: nil,
        status: 'active',
        template_category_id: template_category_ids
      )
      .last
    
    authorize! active_template.present?, on_error: 'Template invalid, please contact administrator!'

    # get model
    used_model = Model.find_by(model_template_id: active_template.id, model: active_template.model)
    authorize! used_model.present?, on_error: 'Template invalid, please contact administrator!'

    # get query
    query = add_data_message(account_plan, table_type) + send("#{table_type}_ai_query")

    ai_params = {
      query: query,
      account_plan_id: account_plan.id,
      ap_table_id: ap_table.id,
      model_id: used_model.id,
      chat_type: 'ap_table_generated_items'
    }
    @openai_service.generate_ap_table_response(ai_params)
  end

  private

  def add_data_message(account_plan, query_table_type)
    query_data = "'input_data': \n\n"
    ap_tables = ApTable.where(account_plan_id: account_plan.id)
    ap_group = account_plan.account_plan_group

    ap_tables.each do |at|
      if at.table_type == 'stakeholder_mapping'
        items = ::AccountPlanTableItems::ApStakeholderMappingItem.where(ap_table_id: at.id)

        data_message = "'Stakeholder Mapping Data' row_headers("
        if query_table_type != 'missing_information'
          data_message += "stakeholder_mapping_item_id, "
        end
        data_message += "name, job_title, location, influence, role, perception, advocacy, coverage) = rows[\n"

        items.each do |i|
          data_message += "{ "
          if query_table_type != 'missing_information'
            data_message += "stakeholder_mapping_item_id: #{i.id}, "
          end
          data_message += "name: #{i.name}, job_title: #{i.job_title}, location: #{i.location}, influence: #{i.influence}, role: #{i.role}, perception: #{i.perception}, advocacy: #{i.advocacy}, coverage: #{i.coverage} }, "
        end

        data_message += "]\n"

        query_data += data_message
      end

      if at.table_type == 'wallet_share'
        items = ::AccountPlanTableItems::ApWalletShareItem.where(ap_table_id: at.id)

        data_message = "'Wallet Share Data' row_headers(item_type, product_service_name, description, value) = rows[\n"

        items.each do |i|
          if ::AccountPlanTableItems::ApWalletShareItem.item_types.values.include?(i.item_type)
            data_message += "{ item_type: #{i.item_type}, product_service_name: #{i.product_service_name}, description: #{i.description}, value: #{i.shared_type_analysis} }, "
          end
        end

        data_message += "]\n"

        query_data += data_message
      end

      if at.table_type == 'circumstantial_analysis'
        items = ::AccountPlanTableItems::ApCircumstantialAnalysisItem.where(ap_table_id: at.id)

        data_message = "'Circumstantial Analysis Data' row_headers(item_type, description) = rows[\n"

        items.each do |i|
          if ::AccountPlanTableItems::ApCircumstantialAnalysisItem.item_types.values.include?(i.item_type)
            data_message += "{item_type: #{i.item_type}, description: #{i.description} }, "
          end
        end

        data_message += "]\n"

        query_data += data_message 
      end

      if at.table_type == 'svot'
        items = ::AccountPlanTableItems::ApSvotItem.where(ap_table_id: at.id)

        data_message = "'SVOT Data' row_headers(item_type, description) = rows[\n"

        items.each do |i|
          if ::AccountPlanTableItems::ApSvotItem.item_types.values.include?(i.item_type)
            data_message += "{ item_type: #{i.item_type}, description: #{i.description} }, "
          end
        end

        data_message += "]\n"

        query_data += data_message
      end

      if at.table_type == 'insight_and_perspective'
        items = ::AccountPlanTableItems::ApInsightAndPerspectiveItem.where(ap_table_id: at.id)

        data_message = "'Insight and Perspective Data' row_headers(target_name, description) = rows[\n"

        items.each do |i|
          data_message += "{ target_name: #{i.target_name}, description: #{i.description} }, "
        end

        data_message += "]\n"

        query_data += data_message
      end

      if at.table_type == 'historic_revenue'
        items = ::AccountPlanTableItems::ApHistoricRevenueItem.where(ap_table_id: at.id)

        data_message = "'Historic Revenue Data' row_headers(time_month, product_service_name, value) = rows[\n"

        items.each do |i|
          data_message += "{ time_month: #{i.time_month}, product_service_name: #{i.product_service_name}, value: #{i.value} }, "
        end

        data_message += "]\n"

        query_data += data_message
      end

      if at.table_type == 'current_revenue'
        items = ::AccountPlanTableItems::ApCurrentRevenueItem.where(ap_table_id: at.id)

        data_message = "'Current Revenue Data' row_headers(renewal_date, product_service_name, value) = rows[\n"

        items.each do |i|
          data_message += "{ renewal_date: #{i.renewal_date}, product_service_name: #{i.product_service_name}, value: #{i.value} }, "
        end

        data_message += "]\n"

        query_data += data_message
      end

      if at.table_type == 'current_opportunity'
        items = ::AccountPlanTableItems::ApCurrentOpportunityItem.where(ap_table_id: at.id)

        data_message = "'Current Opportunity Data' row_headers(close_date, product_service_name, value) = rows[\n"

        items.each do |i|
          data_message += "{ close_date: #{i.close_date}, product_service_name: #{i.product_service_name}, value: #{i.value} }, "
        end

        data_message += "]\n"

        query_data += data_message
      end

      if at.table_type == 'potential_opportunity'
        items = ::AccountPlanTableItems::ApPotentialOpportunityItem.where(ap_table_id: at.id)

        data_message = "'Potential Opportunity Data' row_headers(close_date, product_service_name, value) = rows[\n"

        items.each do |i|
          data_message += "{ close_date: #{i.close_date}, product_service_name: #{i.product_service_name}, value: #{i.value} }, "
        end

        data_message += "]\n"

        query_data += data_message
      end

      if at.table_type == 'revenue_forecast'
        items = ::AccountPlanTableItems::ApRevenueForecastItem.where(ap_table_id: at.id)

        data_message = "'Revenue Forecast Data' row_headers(timespan, low_scenario, realistic_scenario, high_scenario) = rows[\n"

        items.each do |i|
          data_message += "{ timespan: #{i.timespan}, low_scenario: #{i.low_scenario}, realistic_scenario: #{i.realistic_scenario}, high_scenario: #{i.high_scenario} }, "
        end

        data_message += "]\n"

        query_data += data_message
      end
    end

    query_data = query_data + "\n\n "
    query_data += "each '%Table Name% Data' from 'input_data' is a table, with the content of tuple row_headers() as Columns Name and each item in rows[] as json with key from Columns Name. if value does not exist, then it is NULL data. \n\n"
    query_data
  end

  # def missing_information_ai_query
  #   json_format = {
  #     data: {
  #       result: String
  #     }
  #   }.to_json

  #   "'message' = with given 'input_data', generate numbered list of incomplete input in 'input_data' using complete input criterias below as 'result'. " \
  #   "complete input criterias: \n\n" \
  #   "1. 'Stakeholder Mapping Data', 'Insight and Perspective Data', 'Historic Revenue Data', 'Current Revenue Data', 'Current Opportunity Data', 'Potential Opportunity Data' and 'Revenue Forecast Data' should have at least 1 row and all row Column is not (NULL or false).\n" \
  #   "2. 'Wallet Share Data' should have exact 4 rows, all row Column should not be (NULL or false) and item_type for 4 rows is each of (addressable, ours, competition, available)\n" \
  #   "3. 'Circumstantial Analysis Data' should have exact 3 rows, all row Column should not be (NULL or false) & item_type for 3 rows is each of ('macro', 'industry', 'business')\n" \
  #   "4. 'SVOT Data' should have exact 4 rows, all row Column should not be (NULL or false) & item_type for 4 rows is each of ('strength', 'vulnerability', 'opportunity', 'threat')\n" \
  #   "For each criteria, if criteria not met, explain what is missing on what Row and what Column Name.\n" \
  #   "\n\n" \
  #   "'result' content should follow closely to the format and style of Reference Output from instruction or Markdown format if Reference Output does not exist or not valid. " \
  #   "if a string has underscore, replace all underscore to whitespace in 'result' content then capitalize each word from this string only. " \
  #   "'result' content cannot mentioning any quoted string from 'input_data' " \
  #   "Generate response using json format that can be directly parsed: #{json_format}"
  # end

  # def targeted_perception_development_ai_query
  #   json_format = {
  #     data: [
  #       {
  #         stakeholder_mapping_item_id: Integer,
  #         action: String,
  #         result: String,
  #         leverage: String
  #       }
  #     ]
  #   }.to_json

  #   "'message' = with given data and input, for each stakeholder represented by 'stakeholder_mapping_item_id', generate best 'action', 'result' & 'leverage' for this plan. If there is underscore character replace with space, and without mentioning any quoted string input and data id. Generate response using json format that can be directly parsed: #{json_format}"
  # end

  # def action_plan_ai_query
  #   json_format = {
  #     data: {
  #       action: String
  #     }
  #   }.to_json

  #   "'message' = with given 'input_data', generate 'action' following closely to the format and style of Reference Output from instruction. " \
  #   "replace all underscore to whitespace in 'action' content. " \
  #   "'action' content cannot mentioning any quoted string from 'input_data' " \
  #   "Generate response using json format that can be directly parsed: #{json_format}"
  # end

  # def top_action_ai_query
  #   json_format = {
  #     data: [
  #       {
  #         priority: Integer,
  #         action: String,
  #         action_target: String
  #       }
  #     ]
  #   }.to_json

  #   "'message' = with given data and input, generate random amount between 3 and 5 of top action 'action' with smaller 'priority' means more important and 'action_target' as current action's target from stakeholder name for this plan. If there is underscore character replace with space, and without mentioning any quoted string input and data id. Generate response using json format that can be directly parsed: #{json_format}"
  # end
end
