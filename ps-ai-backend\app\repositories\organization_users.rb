# frozen_string_literal: true

class OrganizationUsers < ::ApplicationRepository
  def default_scope
    ::OrganizationUser.all
  end

  def include_users
    @scope.includes(:user)
  end

  def include_organizations
    @scope.includes(:organization)
  end

  def include_team_leader_users
    @scope.includes(team_leader_organization_user: :user)
  end

  def filter_by_organization_id(organization_id)
    @scope.where(organization_id: organization_id)
  end

  def filter_by_search(search)
    @scope.joins(:user, :organization).where("(COALESCE(users.name, '') || ' ' || COALESCE(users.last_name, '') || ', ' || COALESCE(users.email, '') || ', ' || COALESCE(organizations.organization_code, '') || organization_users.organization_identifier_id) ilike ?", "%#{search}%")
  end

  def filter_by_status(status)
    @scope.joins(:user).where(status: status)
  end
end
