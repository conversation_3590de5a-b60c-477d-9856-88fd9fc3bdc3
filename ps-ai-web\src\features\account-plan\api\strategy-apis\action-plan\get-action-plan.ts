import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APActionPlan } from "@/features/account-plan/types/strategy-types";

export const getActionPlanDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APActionPlan> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_ACTION_PLAN_DETAIL(accountId, id));
};

export const getActionPlanDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_ACTION_PLAN,
      id,
    ],
    queryFn: () => getActionPlanDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseActionPlanDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getActionPlanDetail>;
  options?: Partial<ReturnType<typeof getActionPlanDetailQueryOptions>>;
};

export const useActionPlanDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseActionPlanDetailOptions) => {
  const actionPlanDetailQuery = useQuery({
    ...getActionPlanDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...actionPlanDetailQuery,
    actionPlanDetail: actionPlanDetailQuery.data?.data,
  };
};
