import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import _, { set } from "lodash";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { AccountPlanTableType } from "@/features/account-plan/types";
import { useStakeholderMappingList } from "@/features/account-plan/api/position-apis/stakeholder-mapping/get-stakeholder-mapping-list";
import {
  APStakeholderAdvocacy,
  APStakeholderInfluence,
  APStakeholderMapping,
  APStakeholderPerception,
  APStakeholderRole,
} from "@/features/account-plan/types/position-types";
import DataTable, { DataTableMeta } from "@/components/ui/data-table";
import {
  EditableCell,
  MultiSelectCell,
  SelectCell,
} from "@/components/ui/data-table/data-table-components";
import { Table, TableCell, TableHeader, TableRow } from "@/components/ui/table";
import { useCreateStakeholderMapping } from "@/features/account-plan/api/position-apis/stakeholder-mapping/create-stakeholder-mapping";
import { useUpdateStakeholderMapping } from "@/features/account-plan/api/position-apis/stakeholder-mapping/update-stakeholder-mapping";
import { useDeleteStakeholderMapping } from "@/features/account-plan/api/position-apis/stakeholder-mapping/delete-stakeholder-mapping";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { QUERY_KEYS } from "@/constants/query-keys";
import { AccountTable, AccountTableTitle } from "../base-table";
import { useCrmContacts } from "@/features/account-plan/api/position-apis/crm_contacts/get-crm-contacts";
import { useAuth } from "@/features/auth/api/get-auth";

const getRoleList = () => {
  return [
    {
      value: APStakeholderRole.SPONSOR,
      label: "Sponsor",
      influence: [APStakeholderInfluence.HIGH],
      advocacy: [APStakeholderAdvocacy.ADVOCATES_US],
      perception: [
        APStakeholderPerception.PREFERRED_SUPPLIER,
        APStakeholderPerception.SOLUTION_PROVIDER,
        APStakeholderPerception.STRATEGIC_ADVISOR,
        APStakeholderPerception.TRUSTED_PARTNER,
      ],
      excludes: [APStakeholderRole.ANTI_SPONSOR],
    },
    {
      value: APStakeholderRole.COACH,
      label: "Coach",
      influence: [APStakeholderInfluence.MEDIUM, APStakeholderInfluence.HIGH],
      advocacy: [APStakeholderAdvocacy.ADVOCATES_US],
      perception: [
        APStakeholderPerception.PREFERRED_SUPPLIER,
        APStakeholderPerception.SOLUTION_PROVIDER,
        APStakeholderPerception.STRATEGIC_ADVISOR,
        APStakeholderPerception.TRUSTED_PARTNER,
      ],
      excludes: [APStakeholderRole.ANTI_SPONSOR],
    },
    {
      value: APStakeholderRole.ANTI_SPONSOR,
      label: "Anti-sponsor",
      advocacy: [
        APStakeholderAdvocacy.PREFERS_ALTERNATIVE,
        APStakeholderAdvocacy.ADVOCATES_ALTERNATIVE,
      ],
      perception: [APStakeholderPerception.VENDOR],
      excludes: [
        APStakeholderRole.SPONSOR,
        APStakeholderRole.COACH,
        APStakeholderRole.STAKEHOLDER,
      ],
    },
    {
      value: APStakeholderRole.STAKEHOLDER,
      label: "Stakeholder",
      excludes: [APStakeholderRole.ANTI_SPONSOR],
    },
  ];
};

const getInfluenceList = (role: APStakeholderMapping["role"]) => {
  const activeInfluence = getRoleList().find((v) => role?.includes(v.value))
    ?.influence ?? [
    APStakeholderInfluence.LOW,
    APStakeholderInfluence.MEDIUM,
    APStakeholderInfluence.HIGH,
  ];

  return [
    {
      value: APStakeholderInfluence.LOW,
      label: "Low",
    },
    {
      value: APStakeholderInfluence.MEDIUM,
      label: "Medium",
    },
    {
      value: APStakeholderInfluence.HIGH,
      label: "High",
    },
  ].map((v) => ({ ...v, disabled: !activeInfluence.includes(v.value) }));
};

const getAdvocacyList = (role?: APStakeholderMapping["role"]) => {
  const activeAdvocacy = getRoleList().find((v) => role?.includes(v.value))
    ?.advocacy ?? [
    APStakeholderAdvocacy.ADVOCATES_US,
    APStakeholderAdvocacy.PREFERS_US,
    APStakeholderAdvocacy.NO_PREFERENCE,
    APStakeholderAdvocacy.UNSURE_OF_US,
    APStakeholderAdvocacy.PREFERS_ALTERNATIVE,
    APStakeholderAdvocacy.ADVOCATES_ALTERNATIVE,
  ];

  return [
    {
      value: APStakeholderAdvocacy.ADVOCATES_US,
      label: "Advocates You",
      description:
        "Proactively and exclusively provides information and takes action on your behalf.",
    },
    {
      value: APStakeholderAdvocacy.PREFERS_US,
      label: "Prefers You",
      description:
        "Reactively and exclusively provides information and takes action on your behalf when asked. ",
    },
    {
      value: APStakeholderAdvocacy.NO_PREFERENCE,
      label: "No Preference",
      description:
        "Provides information on a non-exclusive basis and will follow other stakeholders' direction.",
    },
    {
      value: APStakeholderAdvocacy.UNSURE_OF_US,
      label: "Unsure of You",
      description:
        "Approached when products / services are required, frequently engages with low-level stakeholders.",
    },
    {
      value: APStakeholderAdvocacy.PREFERS_ALTERNATIVE,
      label: "Prefers Alternatives",
      description:
        "Reactively and exclusively provides information and takes action on behalf of the competition. ",
    },
    {
      value: APStakeholderAdvocacy.ADVOCATES_ALTERNATIVE,
      label: "Advocates Alternatives",
      description:
        "Proactively and exclusively provides information and takes action on behalf of the competition.",
    },
  ].map((v) => ({ ...v, disabled: !activeAdvocacy.includes(v.value) }));
};

const getPerceptionList = (role: APStakeholderMapping["role"]) => {
  const activePerception = getRoleList().find((v) => role?.includes(v.value))
    ?.perception ?? [
    APStakeholderPerception.STRATEGIC_ADVISOR,
    APStakeholderPerception.TRUSTED_PARTNER,
    APStakeholderPerception.SOLUTION_PROVIDER,
    APStakeholderPerception.PREFERRED_SUPPLIER,
    APStakeholderPerception.VENDOR,
  ];

  return [
    {
      value: APStakeholderPerception.STRATEGIC_ADVISOR,
      label: "Strategic Advisor",
      disabled: !activePerception.includes(
        APStakeholderPerception.STRATEGIC_ADVISOR
      ),
    },
    {
      value: APStakeholderPerception.TRUSTED_PARTNER,
      label: "Trusted Partner",
      disabled: !activePerception.includes(
        APStakeholderPerception.TRUSTED_PARTNER
      ),
    },
    {
      value: APStakeholderPerception.SOLUTION_PROVIDER,
      label: "Solution Provider",
      disabled: !activePerception.includes(
        APStakeholderPerception.SOLUTION_PROVIDER
      ),
    },
    {
      value: APStakeholderPerception.PREFERRED_SUPPLIER,
      label: "Preferred Supplier",
      disabled: !activePerception.includes(
        APStakeholderPerception.PREFERRED_SUPPLIER
      ),
    },
    {
      value: APStakeholderPerception.VENDOR,
      label: "Vendor",
    },
  ].map((v) => ({ ...v, disabled: !activePerception.includes(v.value) }));
};

const RoleTooltip = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Popover open={isOpen}>
      <PopoverTrigger
        onMouseLeave={() => setIsOpen(false)}
        onMouseEnter={() => setIsOpen(true)}
      >
        Role(s)
      </PopoverTrigger>
      <PopoverContent
        className="w-fit flex-wrap whitespace-normal text-start text-lg font-semibold"
        align="start"
        side="bottom"
      >
        <div className="max-w-[60vw]">
          The stakeholder’s decision-making role attributed as part of the
          methodology. <br />
          The definitions are on the table below.
          <Table className="mt-res-y-sm">
            <TableHeader>
              <TableCell colSpan={4} className="text-center">
                Stakeholder Decision-making Roles Definitions and Criteria
              </TableCell>
            </TableHeader>
            <TableHeader>
              <TableCell>Stakeholders</TableCell>
              <TableCell>Sponsors</TableCell>
              <TableCell>Coach</TableCell>
              <TableCell>Anti-Sponsors</TableCell>
            </TableHeader>
            <TableRow>
              <TableCell className="align-top">
                <ul className="list-disc p-res-x-sm">
                  <li>
                    Anyone who has any level of influence over prioritization
                    and decision-making for us or our competition.
                  </li>
                  <li>
                    Have low-high involvement in the relationship with the AAA.
                  </li>
                  <li>
                    May be found in the AAA or the broader organization, or
                    outside of both.
                  </li>
                  <li>Are important “touchpoints” in the AAA.</li>
                  <li>
                    May have some of the characteristics of a Sponsor or
                    Strategic Coach.
                  </li>
                </ul>
              </TableCell>

              <TableCell className="align-top">
                <ul className="list-disc p-res-x-sm">
                  <li>
                    Always has a high level of influence, authority and
                    credibility within the AAA.
                  </li>
                  <li>
                    Promotes our tenure in and champions us within the AAA.
                  </li>
                  <li>
                    Counters the competition’s efforts to develop the
                    relationship.
                  </li>
                  <li>Acts as a strategic ‘un-blocker’</li>
                  <li>They are always ‘Advocating Us’.</li>
                </ul>
              </TableCell>

              <TableCell className="align-top">
                <ul className="list-disc p-res-x-sm">
                  <li>
                    Can have a medium-high degree of influence in this AAA.
                  </li>
                  <li>
                    Provides guidance on the strategic direction of the AAA.
                  </li>
                  <li>Provides information on the Strategic Players.</li>
                  <li>
                    Provides information to guide you on other elements of your
                    strategy.
                  </li>
                  <li>
                    Takes action on your behalf either proactively or when asked
                    that is exclusive to you.
                  </li>
                  <li>Actively and effectively supports your efforts.</li>
                </ul>
              </TableCell>

              <TableCell className="align-top">
                <ul className="list-disc p-res-x-sm">
                  <li>
                    Anyone who has any level of influence over prioritization
                    and decision-making for you or your competition.
                  </li>
                  <li>May sponsor and/or be a coach for the competition.</li>
                  <li>Works against your position in the AAA.</li>
                  <li>
                    Seeks to negate your efforts or the Sponsor’s efforts.
                  </li>
                  <li>
                    Will always either ‘Prefer Alternatives’ or ‘Advocate
                    Alternatives’.
                  </li>
                </ul>
              </TableCell>
            </TableRow>
          </Table>
        </div>
      </PopoverContent>
    </Popover>
  );
};

const PerceptionTooltip = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Popover open={isOpen}>
      <PopoverTrigger
        onMouseLeave={() => setIsOpen(false)}
        onMouseEnter={() => setIsOpen(true)}
      >
        Perception
      </PopoverTrigger>
      <PopoverContent
        className="w-[55vw] flex-wrap whitespace-normal text-start text-lg font-semibold"
        align="start"
        side="bottom"
      >
        Their perception of you (from their perspective) is categorised on a
        5-point scale called the Perception Ladder. Definitions are provided in
        the table below.
        <Table className="mt-res-y-sm">
          <TableHeader>
            <TableCell colSpan={4} className="text-center">
              Perception Ladder
            </TableCell>
          </TableHeader>
          <TableHeader>
            <TableCell>Level</TableCell>
            <TableCell>Definiton</TableCell>
            <TableCell>How you may experience this</TableCell>
          </TableHeader>
          <TableRow>
            <TableCell> Strategic Advisor</TableCell>
            <TableCell>
              Clear contribution to the bottom line, acknowledged for the
              long-term strategic contribution, engages with senior leaders
              regularly.
            </TableCell>
            <TableCell rowSpan={3}>
              <ul className="list-disc p-res-x-sm">
                <li>Seen as more than just your services and products</li>
                <li>Seen as a problem solver</li>
                <li>Highly differentiated</li>
                <li>Usually influences the buying process</li>
              </ul>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Trusted Partner</TableCell>
            <TableCell>
              Regularly approached to help solve business issues, engages at
              mid-mostly senior leadership level.
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Solution Provider</TableCell>
            <TableCell>
              Collaborative relationship, perceived as a key solution provider,
              engages with and has access to mid-level leadership.
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Preferred Supplier</TableCell>
            <TableCell>
              Approached when services and products are required, frequently
              engages with low-level stakeholders.
            </TableCell>
            <TableCell rowSpan={3}>
              <ul className="list-disc p-res-x-sm">
                <li>High influence of:</li>
                <ul className="list-disc pl-res-x-sm">
                  <li>Competition</li>
                  <li>Pricing</li>
                  <li>Product specifications</li>
                </ul>
                <li>Enters later in the buying process</li>
              </ul>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Vendor</TableCell>
            <TableCell>
              Transactional relationships, infrequent engagement, usually
              conducted at lower levels of the organization.
            </TableCell>
          </TableRow>
        </Table>
      </PopoverContent>
    </Popover>
  );
};

const AdvocacyTooltip = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Popover open={isOpen}>
      <PopoverTrigger
        className="text-center"
        onMouseLeave={() => setIsOpen(false)}
        onMouseEnter={() => setIsOpen(true)}
      >
        Advocacy
      </PopoverTrigger>
      <PopoverContent
        align="start"
        side="bottom"
        className="w-[55vw] flex-wrap whitespace-normal text-start text-lg font-semibold"
      >
        The stakeholder’s level of ‘Evidenced’ advocacy for our tenure in this
        Account Addressable Area. <br /> As represented on the 6-point scale in
        the table below.
        <Table className="mt-res-y-sm">
          <TableHeader>
            <TableCell colSpan={2} className="text-center">
              Advocacy Gauge
            </TableCell>
          </TableHeader>

          {getAdvocacyList().map((advocacy, idx) => (
            <TableRow key={idx}>
              <TableCell>{advocacy.label}</TableCell>
              <TableCell>{advocacy.description}</TableCell>
            </TableRow>
          ))}
        </Table>
      </PopoverContent>
    </Popover>
  );
};

type StakeholderMappingTableData = {
  role: string[];
  id: number;
  name: string | null;
  job_title: string | null;
  location: string | null;
  influence: APStakeholderInfluence | null;
  perception: string | null;
  advocacy: string | null;
  coverage: boolean | null;
  idx: number;
};

export const StakeholderMappingTable = () => {
  const { user } = useAuth({});
  const [tableData, setTableData] = useState<APStakeholderMapping[]>([]);
  const [edittedRolesId, setEdittedRolesId] = useState<string[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [selectedCRM, setSelectedCRM] = useState<string>("");

  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { stakeholderMappingList } = useStakeholderMappingList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });

  const createStakeholderMapping = useCreateStakeholderMapping({});
  const updateStakeholderMapping = useUpdateStakeholderMapping({});
  const deleteStakeholderMapping = useDeleteStakeholderMapping({});

  const crmSelectOptions = useMemo(
    () =>
      user.organization?.organization_crms?.map((crm) => ({
        value: crm.crm_type,
        label: crm.crm_type.charAt(0).toUpperCase() + crm.crm_type.slice(1),
      })) ?? [],
    [user]
  );

  const { crmContacts } = useCrmContacts({
    accountId,
    params: { crm_type: selectedCRM as "hubspot" | "salesforce" },
  });

  const queryClient = useQueryClient();

  useEffect(() => {
    if (!stakeholderMappingList) return;

    const newStakeholderMappingRows = stakeholderMappingList
      .map((v, idx) => ({
        idx,
        ...v,
        role: v.role ?? [],
      }))
      .sort((a, b) => a.id - b.id);

    if (selectedCRM && crmContacts?.length) {
      const addContacts = async () => {
        try {
          const results = await Promise.all(
            crmContacts.map((contact) =>
              createStakeholderMapping.mutateAsync({
                accountId,
                data: {
                  name: contact.name,
                  job_title: contact.job_title,
                  location: contact.location,
                },
              })
            )
          );

          const appendedRows = results.map((res, i) => ({
            idx: newStakeholderMappingRows.length + i,
            ...res.data,
          }));

          setTableData([...newStakeholderMappingRows, ...appendedRows]);
        } catch {
          toast("An unexpected error occurred when adding data");
          setTableData(newStakeholderMappingRows);
        }
      };

      addContacts();
    } else {
      setTableData(newStakeholderMappingRows);
    }
  }, [stakeholderMappingList, selectedCRM, crmContacts]);

  const roleList = useMemo(() => getRoleList(), []);
  const selectedRows = Object.keys(rowSelection)
    .filter((rowId) => rowSelection[rowId])
    .map((idx) => tableData[parseInt(idx)]);

  const onChangeData = async (
    data: Partial<APStakeholderMapping>,
    id: number
  ) => {
    try {
      setTableData((prevUsers) =>
        prevUsers.map((u) => (u.id === id ? { ...u, ...data } : u))
      );

      await updateStakeholderMapping.mutateAsync({
        accountId,
        id,
        data,
      });
    } catch (_) {
      toast("An unexpected error occured when modifying data");
    }
  };

  const onAddRow = async () => {
    try {
      const res = await createStakeholderMapping.mutateAsync({
        accountId,
      });

      setTableData((prev) => [...prev, res.data]);
    } catch (_) {
      toast("An unexpected error occured when adding data");
    }
  };

  const onDeleteRows = async () => {
    try {
      const promises = [];

      setTableData(
        tableData.filter((row) => !selectedRows.find((v) => v.id === row.id))
      );

      setRowSelection({});

      promises.push(
        selectedRows.map(async (row) => {
          if (!!row?.id) {
            return deleteStakeholderMapping.mutateAsync({
              id: row.id,
              accountId,
            });
          }
        })
      );
      await Promise.all(promises);
    } catch (_) {
      toast("An unexpected error occured when deleting rows");
    }
  };

  const onCloseTable = async () => {
    try {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          accountId,
          QUERY_KEYS.ACCOUNT_PLANS_STAKEHOLDER_MAPPING,
        ],
      });

      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          accountId,
          QUERY_KEYS.ACCOUNT_PLANS_TARGETED_PERCEPTION_DEVELOPMENT,
        ],
      });

      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          accountId,
          QUERY_KEYS.ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE,
        ],
      });

      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          accountId,
          QUERY_KEYS.ACCOUNT_PLANS_CLIENT_MEETING_SCHEDULE,
        ],
      });
    } catch (_) {}
  };

  const columns: ColumnDef<APStakeholderMapping>[] = [
    {
      accessorKey: "name",
      header: "Name",
      size: 125,
      cell: ({ row }) => {
        const user = row.original;

        return (
          <EditableCell
            value={user.name ?? ""}
            onChange={(name) => {
              onChangeData({ name }, user.id);
            }}
            className="break-all text-lg"
          />
        );
      },
      meta: {
        tooltip: "The name of the stakeholder associated with the AAA",
      },
    },
    {
      accessorKey: "job_title",
      header: "Job Title",
      size: 125,
      cell: ({ row }) => {
        const user = row.original;

        return (
          <EditableCell
            className="text-lg"
            value={user.job_title ?? ""}
            onChange={(job_title) => {
              onChangeData({ job_title }, user.id);
            }}
          />
        );
      },
      meta: {
        tooltip:
          "The professional designation of the stakeholder in their organization.",
      },
    },
    {
      accessorKey: "location",
      header: "Location",
      size: 125,
      cell: ({ row }) => {
        const user = row.original;
        return (
          <EditableCell
            className="text-lg"
            value={user.location ?? ""}
            onChange={(location) => {
              onChangeData({ location }, user.id);
            }}
          />
        );
      },
      meta: {
        tooltip: "The stakeholder's primary location where they are based.",
      },
    },
    {
      accessorKey: "influence",
      header: "Influence",
      size: 110,
      cell: ({ row, table }) => {
        const isPreview =
          (table.options.meta as DataTableMeta)?.isPreview ?? false;
        const user = row.original;

        return (
          <SelectCell
            className="text-lg"
            isPreview={isPreview}
            value={user.influence}
            onChange={(influence) => {
              onChangeData(
                { influence: influence as APStakeholderInfluence },
                user.id
              );
            }}
            options={getInfluenceList(user.role)}
          />
        );
      },
      meta: {
        tooltip:
          "The level of influence the stakeholder has over prioritization and decision-making when it comes to you and your competitors.",
      },
    },
    {
      accessorKey: "role",
      header: "Role(s)",
      size: 140,
      cell: ({ row, table }) => {
        const isPreview =
          (table.options.meta as DataTableMeta)?.isPreview ?? false;
        const user = row.original;

        return (
          <MultiSelectCell
            className="text-lg"
            isPreview={isPreview}
            open={edittedRolesId.includes(row.id)}
            onOpenChange={(value) =>
              setEdittedRolesId((prev) => {
                const newIds = value ? [row.id] : _.without(prev, row.id);

                return newIds;
              })
            }
            options={roleList}
            selected={user.role ?? []}
            onChange={(role) => {
              const matchedRole = roleList.find((v) => v.value === role[0]);
              const oldData = tableData.find((v) => v.id === user.id);

              const influence =
                !matchedRole?.influence ||
                !!matchedRole?.influence.find((v) => v === oldData?.influence)
                  ? oldData?.influence
                  : matchedRole.influence[0];

              const advocacy =
                !matchedRole?.advocacy ||
                !!matchedRole?.advocacy.find((v) => v === oldData?.advocacy)
                  ? oldData?.advocacy
                  : matchedRole.advocacy[0];

              const perception =
                !matchedRole?.perception ||
                !!matchedRole?.perception.find((v) => v === oldData?.perception)
                  ? oldData?.perception
                  : matchedRole.perception[0];

              onChangeData({ role, influence, advocacy, perception }, user.id);
            }}
          />
        );
      },
      meta: {
        tooltip: RoleTooltip,
      },
    },
    {
      accessorKey: "perception",
      header: "Perception",
      size: 125,
      cell: ({ row, table }) => {
        const isPreview =
          (table.options.meta as DataTableMeta)?.isPreview ?? false;
        const user = row.original;

        return (
          <SelectCell
            className="text-lg"
            isPreview={isPreview}
            value={user.perception}
            onChange={(perception) => {
              onChangeData({ perception }, user.id);
            }}
            options={getPerceptionList(user.role)}
          />
        );
      },
      meta: {
        tooltip: PerceptionTooltip,
      },
    },
    {
      accessorKey: "advocacy",
      header: "Advocacy",
      size: 125,
      cell: ({ row, table }) => {
        const isPreview =
          (table.options.meta as DataTableMeta)?.isPreview ?? false;
        const user = row.original;

        return (
          <SelectCell
            className="text-lg"
            isPreview={isPreview}
            value={user.advocacy}
            onChange={(advocacy) => {
              onChangeData({ advocacy }, user.id);
            }}
            options={getAdvocacyList(user.role)}
          />
        );
      },
      meta: {
        tooltip: AdvocacyTooltip,
      },
    },
    {
      accessorKey: "coverage",
      header: "Coverage",
      size: 125,
      cell: ({ row, table }) => {
        const isPreview =
          (table.options.meta as DataTableMeta)?.isPreview ?? false;
        const user = row.original;

        return (
          <SelectCell
            className="text-lg"
            isPreview={isPreview}
            value={user.coverage ? "yes" : "no"}
            onChange={(coverage) => {
              onChangeData({ coverage: coverage === "yes" }, user.id);
            }}
            options={[
              { value: "yes", label: "Yes" },
              { value: "no", label: "No" },
            ]}
          />
        );
      },
      meta: {
        tooltip:
          "Does this stakeholder have a relationship with a member of your team in a way that positively advances the relationship?",
      },
    },
  ];

  return (
    <AccountTable
      type={AccountPlanTableType.STAKEHOLDER_MAPPING}
      footer={
        <>
          <Button
            onClick={onAddRow}
            isLoading={createStakeholderMapping.isPending}
          >
            Add row
          </Button>
          <Button
            variant="destructive"
            disabled={selectedRows.length === 0}
            onClick={onDeleteRows}
          >
            Delete Row
          </Button>
        </>
      }
      onClose={onCloseTable}
    >
      <div className="flex items-center justify-end">
        <SelectCell
          className="text-md mt-2 w-60 rounded-lg border border-2 border-gray-300"
          value={selectedCRM}
          onChange={(value) => setSelectedCRM(value)}
          options={crmSelectOptions}
        />
      </div>

      <DataTable
        columns={columns}
        data={tableData}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
      />
    </AccountTable>
  );
};
