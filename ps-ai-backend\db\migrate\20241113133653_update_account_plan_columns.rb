class UpdateAccountPlanColumns < ActiveRecord::Migration[7.0]
  def up
    add_column :account_plans, :currency, :string
    add_column :account_plans, :company, :string
    add_column :account_plans, :location, :string
    add_column :account_plans, :industry, :string
    add_column :account_plans, :function, :string
    add_column :account_plans, :last_updated_by_organization_user_id, :bigint, :index => true
    add_reference :account_plans, :account_plan_group

    remove_column :account_plans, :considerations
    remove_column :account_plans, :plan_date

    change_column :account_plans, :version, :integer, using: "substring(version FROM '([0-9]+)')::INTEGER"
  end

  def down
    remove_column :account_plans, :currency
    remove_column :account_plans, :company
    remove_column :account_plans, :location
    remove_column :account_plans, :industry
    remove_column :account_plans, :function
    remove_column :account_plans, :last_updated_by_organization_user_id
    remove_column :account_plans, :account_plan_group_id

    add_column :account_plans, :considerations, :string, :array => true, :default => []
    add_column :account_plans, :plan_date, :datetime

    change_column :account_plans, :version, :string
  end
end
