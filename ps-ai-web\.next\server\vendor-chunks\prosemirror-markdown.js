"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-markdown";
exports.ids = ["vendor-chunks/prosemirror-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-markdown/dist/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/prosemirror-markdown/dist/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarkdownParser: () => (/* binding */ MarkdownParser),\n/* harmony export */   MarkdownSerializer: () => (/* binding */ MarkdownSerializer),\n/* harmony export */   MarkdownSerializerState: () => (/* binding */ MarkdownSerializerState),\n/* harmony export */   defaultMarkdownParser: () => (/* binding */ defaultMarkdownParser),\n/* harmony export */   defaultMarkdownSerializer: () => (/* binding */ defaultMarkdownSerializer),\n/* harmony export */   schema: () => (/* binding */ schema)\n/* harmony export */ });\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var markdown_it__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! markdown-it */ \"(ssr)/./node_modules/markdown-it/index.mjs\");\n\n\n\n/**\nDocument schema for the data model used by CommonMark.\n*/\nconst schema = new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Schema({\n    nodes: {\n        doc: {\n            content: \"block+\"\n        },\n        paragraph: {\n            content: \"inline*\",\n            group: \"block\",\n            parseDOM: [{ tag: \"p\" }],\n            toDOM() { return [\"p\", 0]; }\n        },\n        blockquote: {\n            content: \"block+\",\n            group: \"block\",\n            parseDOM: [{ tag: \"blockquote\" }],\n            toDOM() { return [\"blockquote\", 0]; }\n        },\n        horizontal_rule: {\n            group: \"block\",\n            parseDOM: [{ tag: \"hr\" }],\n            toDOM() { return [\"div\", [\"hr\"]]; }\n        },\n        heading: {\n            attrs: { level: { default: 1 } },\n            content: \"(text | image)*\",\n            group: \"block\",\n            defining: true,\n            parseDOM: [{ tag: \"h1\", attrs: { level: 1 } },\n                { tag: \"h2\", attrs: { level: 2 } },\n                { tag: \"h3\", attrs: { level: 3 } },\n                { tag: \"h4\", attrs: { level: 4 } },\n                { tag: \"h5\", attrs: { level: 5 } },\n                { tag: \"h6\", attrs: { level: 6 } }],\n            toDOM(node) { return [\"h\" + node.attrs.level, 0]; }\n        },\n        code_block: {\n            content: \"text*\",\n            group: \"block\",\n            code: true,\n            defining: true,\n            marks: \"\",\n            attrs: { params: { default: \"\" } },\n            parseDOM: [{ tag: \"pre\", preserveWhitespace: \"full\", getAttrs: node => ({ params: node.getAttribute(\"data-params\") || \"\" }) }],\n            toDOM(node) { return [\"pre\", node.attrs.params ? { \"data-params\": node.attrs.params } : {}, [\"code\", 0]]; }\n        },\n        ordered_list: {\n            content: \"list_item+\",\n            group: \"block\",\n            attrs: { order: { default: 1 }, tight: { default: false } },\n            parseDOM: [{ tag: \"ol\", getAttrs(dom) {\n                        return { order: dom.hasAttribute(\"start\") ? +dom.getAttribute(\"start\") : 1,\n                            tight: dom.hasAttribute(\"data-tight\") };\n                    } }],\n            toDOM(node) {\n                return [\"ol\", { start: node.attrs.order == 1 ? null : node.attrs.order,\n                        \"data-tight\": node.attrs.tight ? \"true\" : null }, 0];\n            }\n        },\n        bullet_list: {\n            content: \"list_item+\",\n            group: \"block\",\n            attrs: { tight: { default: false } },\n            parseDOM: [{ tag: \"ul\", getAttrs: dom => ({ tight: dom.hasAttribute(\"data-tight\") }) }],\n            toDOM(node) { return [\"ul\", { \"data-tight\": node.attrs.tight ? \"true\" : null }, 0]; }\n        },\n        list_item: {\n            content: \"block+\",\n            defining: true,\n            parseDOM: [{ tag: \"li\" }],\n            toDOM() { return [\"li\", 0]; }\n        },\n        text: {\n            group: \"inline\"\n        },\n        image: {\n            inline: true,\n            attrs: {\n                src: {},\n                alt: { default: null },\n                title: { default: null }\n            },\n            group: \"inline\",\n            draggable: true,\n            parseDOM: [{ tag: \"img[src]\", getAttrs(dom) {\n                        return {\n                            src: dom.getAttribute(\"src\"),\n                            title: dom.getAttribute(\"title\"),\n                            alt: dom.getAttribute(\"alt\")\n                        };\n                    } }],\n            toDOM(node) { return [\"img\", node.attrs]; }\n        },\n        hard_break: {\n            inline: true,\n            group: \"inline\",\n            selectable: false,\n            parseDOM: [{ tag: \"br\" }],\n            toDOM() { return [\"br\"]; }\n        }\n    },\n    marks: {\n        em: {\n            parseDOM: [\n                { tag: \"i\" }, { tag: \"em\" },\n                { style: \"font-style=italic\" },\n                { style: \"font-style=normal\", clearMark: m => m.type.name == \"em\" }\n            ],\n            toDOM() { return [\"em\"]; }\n        },\n        strong: {\n            parseDOM: [\n                { tag: \"strong\" },\n                { tag: \"b\", getAttrs: node => node.style.fontWeight != \"normal\" && null },\n                { style: \"font-weight=400\", clearMark: m => m.type.name == \"strong\" },\n                { style: \"font-weight\", getAttrs: value => /^(bold(er)?|[5-9]\\d{2,})$/.test(value) && null }\n            ],\n            toDOM() { return [\"strong\"]; }\n        },\n        link: {\n            attrs: {\n                href: {},\n                title: { default: null }\n            },\n            inclusive: false,\n            parseDOM: [{ tag: \"a[href]\", getAttrs(dom) {\n                        return { href: dom.getAttribute(\"href\"), title: dom.getAttribute(\"title\") };\n                    } }],\n            toDOM(node) { return [\"a\", node.attrs]; }\n        },\n        code: {\n            code: true,\n            parseDOM: [{ tag: \"code\" }],\n            toDOM() { return [\"code\"]; }\n        }\n    }\n});\n\n// @ts-ignore\nfunction maybeMerge(a, b) {\n    if (a.isText && b.isText && prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Mark.sameSet(a.marks, b.marks))\n        return a.withText(a.text + b.text);\n}\n// Object used to track the context of a running parse.\nclass MarkdownParseState {\n    constructor(schema, tokenHandlers) {\n        this.schema = schema;\n        this.tokenHandlers = tokenHandlers;\n        this.stack = [{ type: schema.topNodeType, attrs: null, content: [], marks: prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Mark.none }];\n    }\n    top() {\n        return this.stack[this.stack.length - 1];\n    }\n    push(elt) {\n        if (this.stack.length)\n            this.top().content.push(elt);\n    }\n    // Adds the given text to the current position in the document,\n    // using the current marks as styling.\n    addText(text) {\n        if (!text)\n            return;\n        let top = this.top(), nodes = top.content, last = nodes[nodes.length - 1];\n        let node = this.schema.text(text, top.marks), merged;\n        if (last && (merged = maybeMerge(last, node)))\n            nodes[nodes.length - 1] = merged;\n        else\n            nodes.push(node);\n    }\n    // Adds the given mark to the set of active marks.\n    openMark(mark) {\n        let top = this.top();\n        top.marks = mark.addToSet(top.marks);\n    }\n    // Removes the given mark from the set of active marks.\n    closeMark(mark) {\n        let top = this.top();\n        top.marks = mark.removeFromSet(top.marks);\n    }\n    parseTokens(toks) {\n        for (let i = 0; i < toks.length; i++) {\n            let tok = toks[i];\n            let handler = this.tokenHandlers[tok.type];\n            if (!handler)\n                throw new Error(\"Token type `\" + tok.type + \"` not supported by Markdown parser\");\n            handler(this, tok, toks, i);\n        }\n    }\n    // Add a node at the current position.\n    addNode(type, attrs, content) {\n        let top = this.top();\n        let node = type.createAndFill(attrs, content, top ? top.marks : []);\n        if (!node)\n            return null;\n        this.push(node);\n        return node;\n    }\n    // Wrap subsequent content in a node of the given type.\n    openNode(type, attrs) {\n        this.stack.push({ type: type, attrs: attrs, content: [], marks: prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Mark.none });\n    }\n    // Close and return the node that is currently on top of the stack.\n    closeNode() {\n        let info = this.stack.pop();\n        return this.addNode(info.type, info.attrs, info.content);\n    }\n}\nfunction attrs(spec, token, tokens, i) {\n    if (spec.getAttrs)\n        return spec.getAttrs(token, tokens, i);\n    // For backwards compatibility when `attrs` is a Function\n    else if (spec.attrs instanceof Function)\n        return spec.attrs(token);\n    else\n        return spec.attrs;\n}\n// Code content is represented as a single token with a `content`\n// property in Markdown-it.\nfunction noCloseToken(spec, type) {\n    return spec.noCloseToken || type == \"code_inline\" || type == \"code_block\" || type == \"fence\";\n}\nfunction withoutTrailingNewline(str) {\n    return str[str.length - 1] == \"\\n\" ? str.slice(0, str.length - 1) : str;\n}\nfunction noOp() { }\nfunction tokenHandlers(schema, tokens) {\n    let handlers = Object.create(null);\n    for (let type in tokens) {\n        let spec = tokens[type];\n        if (spec.block) {\n            let nodeType = schema.nodeType(spec.block);\n            if (noCloseToken(spec, type)) {\n                handlers[type] = (state, tok, tokens, i) => {\n                    state.openNode(nodeType, attrs(spec, tok, tokens, i));\n                    state.addText(withoutTrailingNewline(tok.content));\n                    state.closeNode();\n                };\n            }\n            else {\n                handlers[type + \"_open\"] = (state, tok, tokens, i) => state.openNode(nodeType, attrs(spec, tok, tokens, i));\n                handlers[type + \"_close\"] = state => state.closeNode();\n            }\n        }\n        else if (spec.node) {\n            let nodeType = schema.nodeType(spec.node);\n            handlers[type] = (state, tok, tokens, i) => state.addNode(nodeType, attrs(spec, tok, tokens, i));\n        }\n        else if (spec.mark) {\n            let markType = schema.marks[spec.mark];\n            if (noCloseToken(spec, type)) {\n                handlers[type] = (state, tok, tokens, i) => {\n                    state.openMark(markType.create(attrs(spec, tok, tokens, i)));\n                    state.addText(withoutTrailingNewline(tok.content));\n                    state.closeMark(markType);\n                };\n            }\n            else {\n                handlers[type + \"_open\"] = (state, tok, tokens, i) => state.openMark(markType.create(attrs(spec, tok, tokens, i)));\n                handlers[type + \"_close\"] = state => state.closeMark(markType);\n            }\n        }\n        else if (spec.ignore) {\n            if (noCloseToken(spec, type)) {\n                handlers[type] = noOp;\n            }\n            else {\n                handlers[type + \"_open\"] = noOp;\n                handlers[type + \"_close\"] = noOp;\n            }\n        }\n        else {\n            throw new RangeError(\"Unrecognized parsing spec \" + JSON.stringify(spec));\n        }\n    }\n    handlers.text = (state, tok) => state.addText(tok.content);\n    handlers.inline = (state, tok) => state.parseTokens(tok.children);\n    handlers.softbreak = handlers.softbreak || (state => state.addText(\" \"));\n    return handlers;\n}\n/**\nA configuration of a Markdown parser. Such a parser uses\n[markdown-it](https://github.com/markdown-it/markdown-it) to\ntokenize a file, and then runs the custom rules it is given over\nthe tokens to create a ProseMirror document tree.\n*/\nclass MarkdownParser {\n    /**\n    Create a parser with the given configuration. You can configure\n    the markdown-it parser to parse the dialect you want, and provide\n    a description of the ProseMirror entities those tokens map to in\n    the `tokens` object, which maps token names to descriptions of\n    what to do with them. Such a description is an object, and may\n    have the following properties:\n    */\n    constructor(\n    /**\n    The parser's document schema.\n    */\n    schema, \n    /**\n    This parser's markdown-it tokenizer.\n    */\n    tokenizer, \n    /**\n    The value of the `tokens` object used to construct this\n    parser. Can be useful to copy and modify to base other parsers\n    on.\n    */\n    tokens) {\n        this.schema = schema;\n        this.tokenizer = tokenizer;\n        this.tokens = tokens;\n        this.tokenHandlers = tokenHandlers(schema, tokens);\n    }\n    /**\n    Parse a string as [CommonMark](http://commonmark.org/) markup,\n    and create a ProseMirror document as prescribed by this parser's\n    rules.\n    \n    The second argument, when given, is passed through to the\n    [Markdown\n    parser](https://markdown-it.github.io/markdown-it/#MarkdownIt.parse).\n    */\n    parse(text, markdownEnv = {}) {\n        let state = new MarkdownParseState(this.schema, this.tokenHandlers), doc;\n        state.parseTokens(this.tokenizer.parse(text, markdownEnv));\n        do {\n            doc = state.closeNode();\n        } while (state.stack.length);\n        return doc || this.schema.topNodeType.createAndFill();\n    }\n}\nfunction listIsTight(tokens, i) {\n    while (++i < tokens.length)\n        if (tokens[i].type != \"list_item_open\")\n            return tokens[i].hidden;\n    return false;\n}\n/**\nA parser parsing unextended [CommonMark](http://commonmark.org/),\nwithout inline HTML, and producing a document in the basic schema.\n*/\nconst defaultMarkdownParser = new MarkdownParser(schema, (0,markdown_it__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"commonmark\", { html: false }), {\n    blockquote: { block: \"blockquote\" },\n    paragraph: { block: \"paragraph\" },\n    list_item: { block: \"list_item\" },\n    bullet_list: { block: \"bullet_list\", getAttrs: (_, tokens, i) => ({ tight: listIsTight(tokens, i) }) },\n    ordered_list: { block: \"ordered_list\", getAttrs: (tok, tokens, i) => ({\n            order: +tok.attrGet(\"start\") || 1,\n            tight: listIsTight(tokens, i)\n        }) },\n    heading: { block: \"heading\", getAttrs: tok => ({ level: +tok.tag.slice(1) }) },\n    code_block: { block: \"code_block\", noCloseToken: true },\n    fence: { block: \"code_block\", getAttrs: tok => ({ params: tok.info || \"\" }), noCloseToken: true },\n    hr: { node: \"horizontal_rule\" },\n    image: { node: \"image\", getAttrs: tok => ({\n            src: tok.attrGet(\"src\"),\n            title: tok.attrGet(\"title\") || null,\n            alt: tok.children[0] && tok.children[0].content || null\n        }) },\n    hardbreak: { node: \"hard_break\" },\n    em: { mark: \"em\" },\n    strong: { mark: \"strong\" },\n    link: { mark: \"link\", getAttrs: tok => ({\n            href: tok.attrGet(\"href\"),\n            title: tok.attrGet(\"title\") || null\n        }) },\n    code_inline: { mark: \"code\", noCloseToken: true }\n});\n\nconst blankMark = { open: \"\", close: \"\", mixable: true };\n/**\nA specification for serializing a ProseMirror document as\nMarkdown/CommonMark text.\n*/\nclass MarkdownSerializer {\n    /**\n    Construct a serializer with the given configuration. The `nodes`\n    object should map node names in a given schema to function that\n    take a serializer state and such a node, and serialize the node.\n    */\n    constructor(\n    /**\n    The node serializer functions for this serializer.\n    */\n    nodes, \n    /**\n    The mark serializer info.\n    */\n    marks, options = {}) {\n        this.nodes = nodes;\n        this.marks = marks;\n        this.options = options;\n    }\n    /**\n    Serialize the content of the given node to\n    [CommonMark](http://commonmark.org/).\n    */\n    serialize(content, options = {}) {\n        options = Object.assign({}, this.options, options);\n        let state = new MarkdownSerializerState(this.nodes, this.marks, options);\n        state.renderContent(content);\n        return state.out;\n    }\n}\n/**\nA serializer for the [basic schema](https://prosemirror.net/docs/ref/#schema).\n*/\nconst defaultMarkdownSerializer = new MarkdownSerializer({\n    blockquote(state, node) {\n        state.wrapBlock(\"> \", null, node, () => state.renderContent(node));\n    },\n    code_block(state, node) {\n        // Make sure the front matter fences are longer than any dash sequence within it\n        const backticks = node.textContent.match(/`{3,}/gm);\n        const fence = backticks ? (backticks.sort().slice(-1)[0] + \"`\") : \"```\";\n        state.write(fence + (node.attrs.params || \"\") + \"\\n\");\n        state.text(node.textContent, false);\n        // Add a newline to the current content before adding closing marker\n        state.write(\"\\n\");\n        state.write(fence);\n        state.closeBlock(node);\n    },\n    heading(state, node) {\n        state.write(state.repeat(\"#\", node.attrs.level) + \" \");\n        state.renderInline(node, false);\n        state.closeBlock(node);\n    },\n    horizontal_rule(state, node) {\n        state.write(node.attrs.markup || \"---\");\n        state.closeBlock(node);\n    },\n    bullet_list(state, node) {\n        state.renderList(node, \"  \", () => (node.attrs.bullet || \"*\") + \" \");\n    },\n    ordered_list(state, node) {\n        let start = node.attrs.order || 1;\n        let maxW = String(start + node.childCount - 1).length;\n        let space = state.repeat(\" \", maxW + 2);\n        state.renderList(node, space, i => {\n            let nStr = String(start + i);\n            return state.repeat(\" \", maxW - nStr.length) + nStr + \". \";\n        });\n    },\n    list_item(state, node) {\n        state.renderContent(node);\n    },\n    paragraph(state, node) {\n        state.renderInline(node);\n        state.closeBlock(node);\n    },\n    image(state, node) {\n        state.write(\"![\" + state.esc(node.attrs.alt || \"\") + \"](\" + node.attrs.src.replace(/[\\(\\)]/g, \"\\\\$&\") +\n            (node.attrs.title ? ' \"' + node.attrs.title.replace(/\"/g, '\\\\\"') + '\"' : \"\") + \")\");\n    },\n    hard_break(state, node, parent, index) {\n        for (let i = index + 1; i < parent.childCount; i++)\n            if (parent.child(i).type != node.type) {\n                state.write(\"\\\\\\n\");\n                return;\n            }\n    },\n    text(state, node) {\n        state.text(node.text, !state.inAutolink);\n    }\n}, {\n    em: { open: \"*\", close: \"*\", mixable: true, expelEnclosingWhitespace: true },\n    strong: { open: \"**\", close: \"**\", mixable: true, expelEnclosingWhitespace: true },\n    link: {\n        open(state, mark, parent, index) {\n            state.inAutolink = isPlainURL(mark, parent, index);\n            return state.inAutolink ? \"<\" : \"[\";\n        },\n        close(state, mark, parent, index) {\n            let { inAutolink } = state;\n            state.inAutolink = undefined;\n            return inAutolink ? \">\"\n                : \"](\" + mark.attrs.href.replace(/[\\(\\)\"]/g, \"\\\\$&\") + (mark.attrs.title ? ` \"${mark.attrs.title.replace(/\"/g, '\\\\\"')}\"` : \"\") + \")\";\n        },\n        mixable: true\n    },\n    code: { open(_state, _mark, parent, index) { return backticksFor(parent.child(index), -1); },\n        close(_state, _mark, parent, index) { return backticksFor(parent.child(index - 1), 1); },\n        escape: false }\n});\nfunction backticksFor(node, side) {\n    let ticks = /`+/g, m, len = 0;\n    if (node.isText)\n        while (m = ticks.exec(node.text))\n            len = Math.max(len, m[0].length);\n    let result = len > 0 && side > 0 ? \" `\" : \"`\";\n    for (let i = 0; i < len; i++)\n        result += \"`\";\n    if (len > 0 && side < 0)\n        result += \" \";\n    return result;\n}\nfunction isPlainURL(link, parent, index) {\n    if (link.attrs.title || !/^\\w+:/.test(link.attrs.href))\n        return false;\n    let content = parent.child(index);\n    if (!content.isText || content.text != link.attrs.href || content.marks[content.marks.length - 1] != link)\n        return false;\n    return index == parent.childCount - 1 || !link.isInSet(parent.child(index + 1).marks);\n}\n/**\nThis is an object used to track state and expose\nmethods related to markdown serialization. Instances are passed to\nnode and mark serialization methods (see `toMarkdown`).\n*/\nclass MarkdownSerializerState {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    nodes, \n    /**\n    @internal\n    */\n    marks, \n    /**\n    The options passed to the serializer.\n    */\n    options) {\n        this.nodes = nodes;\n        this.marks = marks;\n        this.options = options;\n        /**\n        @internal\n        */\n        this.delim = \"\";\n        /**\n        @internal\n        */\n        this.out = \"\";\n        /**\n        @internal\n        */\n        this.closed = null;\n        /**\n        @internal\n        */\n        this.inAutolink = undefined;\n        /**\n        @internal\n        */\n        this.atBlockStart = false;\n        /**\n        @internal\n        */\n        this.inTightList = false;\n        if (typeof this.options.tightLists == \"undefined\")\n            this.options.tightLists = false;\n        if (typeof this.options.hardBreakNodeName == \"undefined\")\n            this.options.hardBreakNodeName = \"hard_break\";\n    }\n    /**\n    @internal\n    */\n    flushClose(size = 2) {\n        if (this.closed) {\n            if (!this.atBlank())\n                this.out += \"\\n\";\n            if (size > 1) {\n                let delimMin = this.delim;\n                let trim = /\\s+$/.exec(delimMin);\n                if (trim)\n                    delimMin = delimMin.slice(0, delimMin.length - trim[0].length);\n                for (let i = 1; i < size; i++)\n                    this.out += delimMin + \"\\n\";\n            }\n            this.closed = null;\n        }\n    }\n    /**\n    @internal\n    */\n    getMark(name) {\n        let info = this.marks[name];\n        if (!info) {\n            if (this.options.strict !== false)\n                throw new Error(`Mark type \\`${name}\\` not supported by Markdown renderer`);\n            info = blankMark;\n        }\n        return info;\n    }\n    /**\n    Render a block, prefixing each line with `delim`, and the first\n    line in `firstDelim`. `node` should be the node that is closed at\n    the end of the block, and `f` is a function that renders the\n    content of the block.\n    */\n    wrapBlock(delim, firstDelim, node, f) {\n        let old = this.delim;\n        this.write(firstDelim != null ? firstDelim : delim);\n        this.delim += delim;\n        f();\n        this.delim = old;\n        this.closeBlock(node);\n    }\n    /**\n    @internal\n    */\n    atBlank() {\n        return /(^|\\n)$/.test(this.out);\n    }\n    /**\n    Ensure the current content ends with a newline.\n    */\n    ensureNewLine() {\n        if (!this.atBlank())\n            this.out += \"\\n\";\n    }\n    /**\n    Prepare the state for writing output (closing closed paragraphs,\n    adding delimiters, and so on), and then optionally add content\n    (unescaped) to the output.\n    */\n    write(content) {\n        this.flushClose();\n        if (this.delim && this.atBlank())\n            this.out += this.delim;\n        if (content)\n            this.out += content;\n    }\n    /**\n    Close the block for the given node.\n    */\n    closeBlock(node) {\n        this.closed = node;\n    }\n    /**\n    Add the given text to the document. When escape is not `false`,\n    it will be escaped.\n    */\n    text(text, escape = true) {\n        let lines = text.split(\"\\n\");\n        for (let i = 0; i < lines.length; i++) {\n            this.write();\n            // Escape exclamation marks in front of links\n            if (!escape && lines[i][0] == \"[\" && /(^|[^\\\\])\\!$/.test(this.out))\n                this.out = this.out.slice(0, this.out.length - 1) + \"\\\\!\";\n            this.out += escape ? this.esc(lines[i], this.atBlockStart) : lines[i];\n            if (i != lines.length - 1)\n                this.out += \"\\n\";\n        }\n    }\n    /**\n    Render the given node as a block.\n    */\n    render(node, parent, index) {\n        if (this.nodes[node.type.name]) {\n            this.nodes[node.type.name](this, node, parent, index);\n        }\n        else {\n            if (this.options.strict !== false) {\n                throw new Error(\"Token type `\" + node.type.name + \"` not supported by Markdown renderer\");\n            }\n            else if (!node.type.isLeaf) {\n                if (node.type.inlineContent)\n                    this.renderInline(node);\n                else\n                    this.renderContent(node);\n                if (node.isBlock)\n                    this.closeBlock(node);\n            }\n        }\n    }\n    /**\n    Render the contents of `parent` as block nodes.\n    */\n    renderContent(parent) {\n        parent.forEach((node, _, i) => this.render(node, parent, i));\n    }\n    /**\n    Render the contents of `parent` as inline content.\n    */\n    renderInline(parent, fromBlockStart = true) {\n        this.atBlockStart = fromBlockStart;\n        let active = [], trailing = \"\";\n        let progress = (node, offset, index) => {\n            let marks = node ? node.marks : [];\n            // Remove marks from `hard_break` that are the last node inside\n            // that mark to prevent parser edge cases with new lines just\n            // before closing marks.\n            if (node && node.type.name === this.options.hardBreakNodeName)\n                marks = marks.filter(m => {\n                    if (index + 1 == parent.childCount)\n                        return false;\n                    let next = parent.child(index + 1);\n                    return m.isInSet(next.marks) && (!next.isText || /\\S/.test(next.text));\n                });\n            let leading = trailing;\n            trailing = \"\";\n            // If whitespace has to be expelled from the node, adjust\n            // leading and trailing accordingly.\n            if (node && node.isText && marks.some(mark => {\n                let info = this.getMark(mark.type.name);\n                return info && info.expelEnclosingWhitespace && !mark.isInSet(active);\n            })) {\n                let [_, lead, rest] = /^(\\s*)(.*)$/m.exec(node.text);\n                if (lead) {\n                    leading += lead;\n                    node = rest ? node.withText(rest) : null;\n                    if (!node)\n                        marks = active;\n                }\n            }\n            if (node && node.isText && marks.some(mark => {\n                let info = this.getMark(mark.type.name);\n                return info && info.expelEnclosingWhitespace &&\n                    (index == parent.childCount - 1 || !mark.isInSet(parent.child(index + 1).marks));\n            })) {\n                let [_, rest, trail] = /^(.*?)(\\s*)$/m.exec(node.text);\n                if (trail) {\n                    trailing = trail;\n                    node = rest ? node.withText(rest) : null;\n                    if (!node)\n                        marks = active;\n                }\n            }\n            let inner = marks.length ? marks[marks.length - 1] : null;\n            let noEsc = inner && this.getMark(inner.type.name).escape === false;\n            let len = marks.length - (noEsc ? 1 : 0);\n            // Try to reorder 'mixable' marks, such as em and strong, which\n            // in Markdown may be opened and closed in different order, so\n            // that order of the marks for the token matches the order in\n            // active.\n            outer: for (let i = 0; i < len; i++) {\n                let mark = marks[i];\n                if (!this.getMark(mark.type.name).mixable)\n                    break;\n                for (let j = 0; j < active.length; j++) {\n                    let other = active[j];\n                    if (!this.getMark(other.type.name).mixable)\n                        break;\n                    if (mark.eq(other)) {\n                        if (i > j)\n                            marks = marks.slice(0, j).concat(mark).concat(marks.slice(j, i)).concat(marks.slice(i + 1, len));\n                        else if (j > i)\n                            marks = marks.slice(0, i).concat(marks.slice(i + 1, j)).concat(mark).concat(marks.slice(j, len));\n                        continue outer;\n                    }\n                }\n            }\n            // Find the prefix of the mark set that didn't change\n            let keep = 0;\n            while (keep < Math.min(active.length, len) && marks[keep].eq(active[keep]))\n                ++keep;\n            // Close the marks that need to be closed\n            while (keep < active.length)\n                this.text(this.markString(active.pop(), false, parent, index), false);\n            // Output any previously expelled trailing whitespace outside the marks\n            if (leading)\n                this.text(leading);\n            // Open the marks that need to be opened\n            if (node) {\n                while (active.length < len) {\n                    let add = marks[active.length];\n                    active.push(add);\n                    this.text(this.markString(add, true, parent, index), false);\n                    this.atBlockStart = false;\n                }\n                // Render the node. Special case code marks, since their content\n                // may not be escaped.\n                if (noEsc && node.isText)\n                    this.text(this.markString(inner, true, parent, index) + node.text +\n                        this.markString(inner, false, parent, index + 1), false);\n                else\n                    this.render(node, parent, index);\n                this.atBlockStart = false;\n            }\n            // After the first non-empty text node is rendered, the end of output\n            // is no longer at block start.\n            //\n            // FIXME: If a non-text node writes something to the output for this\n            // block, the end of output is also no longer at block start. But how\n            // can we detect that?\n            if ((node === null || node === void 0 ? void 0 : node.isText) && node.nodeSize > 0) {\n                this.atBlockStart = false;\n            }\n        };\n        parent.forEach(progress);\n        progress(null, 0, parent.childCount);\n        this.atBlockStart = false;\n    }\n    /**\n    Render a node's content as a list. `delim` should be the extra\n    indentation added to all lines except the first in an item,\n    `firstDelim` is a function going from an item index to a\n    delimiter for the first line of the item.\n    */\n    renderList(node, delim, firstDelim) {\n        if (this.closed && this.closed.type == node.type)\n            this.flushClose(3);\n        else if (this.inTightList)\n            this.flushClose(1);\n        let isTight = typeof node.attrs.tight != \"undefined\" ? node.attrs.tight : this.options.tightLists;\n        let prevTight = this.inTightList;\n        this.inTightList = isTight;\n        node.forEach((child, _, i) => {\n            if (i && isTight)\n                this.flushClose(1);\n            this.wrapBlock(delim, firstDelim(i), node, () => this.render(child, node, i));\n        });\n        this.inTightList = prevTight;\n    }\n    /**\n    Escape the given string so that it can safely appear in Markdown\n    content. If `startOfLine` is true, also escape characters that\n    have special meaning only at the start of the line.\n    */\n    esc(str, startOfLine = false) {\n        str = str.replace(/[`*\\\\~\\[\\]_]/g, (m, i) => m == \"_\" && i > 0 && i + 1 < str.length && str[i - 1].match(/\\w/) && str[i + 1].match(/\\w/) ? m : \"\\\\\" + m);\n        if (startOfLine)\n            str = str.replace(/^(\\+[ ]|[\\-*>])/, \"\\\\$&\").replace(/^(\\s*)(#{1,6})(\\s|$)/, '$1\\\\$2$3').replace(/^(\\s*\\d+)\\.\\s/, \"$1\\\\. \");\n        if (this.options.escapeExtraCharacters)\n            str = str.replace(this.options.escapeExtraCharacters, \"\\\\$&\");\n        return str;\n    }\n    /**\n    @internal\n    */\n    quote(str) {\n        let wrap = str.indexOf('\"') == -1 ? '\"\"' : str.indexOf(\"'\") == -1 ? \"''\" : \"()\";\n        return wrap[0] + str + wrap[1];\n    }\n    /**\n    Repeat the given string `n` times.\n    */\n    repeat(str, n) {\n        let out = \"\";\n        for (let i = 0; i < n; i++)\n            out += str;\n        return out;\n    }\n    /**\n    Get the markdown string for a given opening or closing mark.\n    */\n    markString(mark, open, parent, index) {\n        let info = this.getMark(mark.type.name);\n        let value = open ? info.open : info.close;\n        return typeof value == \"string\" ? value : value(this, mark, parent, index);\n    }\n    /**\n    Get leading and trailing whitespace from a string. Values of\n    leading or trailing property of the return object will be undefined\n    if there is no match.\n    */\n    getEnclosingWhitespace(text) {\n        return {\n            leading: (text.match(/^(\\s+)/) || [undefined])[0],\n            trailing: (text.match(/(\\s+)$/) || [undefined])[0]\n        };\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-markdown/dist/index.js\n");

/***/ })

};
;