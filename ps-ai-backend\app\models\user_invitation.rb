# frozen_string_literal: true

class UserInvitation < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  belongs_to :organization
  belongs_to :invited_by_organization_user,  
             class_name: 'OrganizationUser',
             foreign_key: 'invited_by_organization_user_id'
  belongs_to :assigned_role,  
             class_name: 'Role',
             foreign_key: 'assigned_role_id'
end
