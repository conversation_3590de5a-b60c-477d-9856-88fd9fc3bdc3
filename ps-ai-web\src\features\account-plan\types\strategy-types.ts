import { APStakeholderMapping } from "./position-types";

/* Account Plan Missing Information */
export type APMissingInformationBaseData = Partial<
  Omit<APMissingInformation, "id">
>;
export type APMissingInformation = {
  id: number;
  description: string;
};

/* Account Plan Targeted Perception Development */
export type APTargetedPerceptionDevelopmentBaseData = Partial<
  Omit<APTargetedPerceptionDevelopment, "id">
> & { ap_stakeholder_mapping_item_id?: number };

export type APTargetedPerceptionDevelopment = {
  id: number;
  action: string;
  result: string;
  leverage: string;
  ap_stakeholder_mapping_item: APStakeholderMapping | null;
};

/* Account Plan Action Plan */
export type APActionPlanBaseData = Partial<Omit<APActionPlan, "id">>;

export type APActionPlan = {
  id: number;
  description: string;
};

/* Account Plan Top Action */
export type APTopActionBaseData = Partial<Omit<APTopAction, "id">>;

export type APTopAction = {
  id: number;
  account_plan_id: number;
  description: string;
  how: string;
  action_target: string;
  action_date: string | null;
  order: number;
  completed: boolean;
};

export type APActiveTopAction = Array<{
  id: number;
  account_plan_unique_id: string;
  company_name: string;
  top_actions: Array<APTopAction>;
}>;

/* Account Plan Client Meeting Schedule */
export type APClientMeetingScheduleBaseData = Partial<
  Omit<APClientMeetingSchedule, "id">
> & { ap_stakeholder_mapping_item_id?: number };

export type APClientMeetingSchedule = {
  id: number;
  meeting_date: string;
  client: string;
  notes: string;
  ap_stakeholder_mapping_item: APStakeholderMapping | null;
};
