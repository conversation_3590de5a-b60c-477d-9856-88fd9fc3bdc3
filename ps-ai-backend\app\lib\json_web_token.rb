# frozen_string_literal: true

class JsonWebToken
  HMAC_SECRET = Rails.application.credentials.jwt_secret
  TOKEN_EXPIRATION_TIME = ENV['TOKEN_EXPIRATION_TIME'].presence

  def self.encode(payload, exp = 365.days)
    expire_time = (TOKEN_EXPIRATION_TIME || exp).to_i
    expire_date = Time.now.to_i + expire_time

    payload[:exp] = expire_date
    JWT.encode(payload, HMAC_SECRET)
  end

  def self.decode(token)
    body = JWT.decode(token, HMAC_SECRET)[0]
    HashWithIndifferentAccess.new body
  rescue JWT::DecodeError => e
    raise ExceptionHandler::InvalidToken, e.message
  end
end
