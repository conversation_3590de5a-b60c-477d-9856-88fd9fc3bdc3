# frozen_string_literal: true

module V1
  class AccountPlanTableItems::ApWalletShareItemOutput < ApiOutput
    def format
      {
        id: @object.id,
        shared_type_analysis: @object.shared_type_analysis,
        item_type: @object.item_type,
        product_service_name: @object.product_service_name,
        description: @object.description,
        currency: @object.currency,
        ap_table_id: @object.ap_table_id
      }
    end
  end
end
