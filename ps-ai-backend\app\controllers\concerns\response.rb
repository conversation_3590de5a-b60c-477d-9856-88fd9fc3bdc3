# frozen_string_literal: true

module Response
  private

  def default_output
    ApiOutput
  end

  def render_json(model, klass = default_output, **options)
    output = klass.new(model, options)

    render json: output.root_json, status: output.status
  end

  def render_error(model_or_string = 'Something went wrong', **options)
    render_json(model_or_string, ErrorOutput, **options)
  end

  def render_empty_json(model, klass = EmptyOutput, **options)
    render_json(model, klass, **options)
  end

  def render_json_array(array, klass = default_output, **options)
    output = klass.array(array, **options)
    render json: output.root_json, status: output.status
  end
end
