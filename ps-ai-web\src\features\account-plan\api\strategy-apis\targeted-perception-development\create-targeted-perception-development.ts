import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APTargetedPerceptionDevelopment,
  APTargetedPerceptionDevelopmentBaseData,
} from "@/features/account-plan/types/strategy-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createTargetedPerceptionDevelopment = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APTargetedPerceptionDevelopmentBaseData;
}): ApiResponse<APTargetedPerceptionDevelopment> => {
  return api.post(
    API_ROUTES.ACCOUNT_PLANS_TARGETED_PERCEPTION_DEVELOPMENT(accountId),
    data
  );
};

type UseCreateTargetedPerceptionDevelopmentOptions = {
  mutationConfig?: MutationConfig<typeof createTargetedPerceptionDevelopment>;
};

export const useCreateTargetedPerceptionDevelopment = ({
  mutationConfig,
}: UseCreateTargetedPerceptionDevelopmentOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_TARGETED_PERCEPTION_DEVELOPMENT,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createTargetedPerceptionDevelopment,
  });
};
