class PasswordChangeRequestMailer < ApplicationMailer
  default from: <PERSON>N<PERSON>['MAILER_EMAIL_ADDRESS']

  def send_email
    @token_request = params[:token_request]
    @user_name = @token_request.user.name
    @base_app = ENV['WEBAPP']

    request_link = ''
    subject = 'Request'

    purpose = @token_request.purpose
    if purpose == 'change_password'
      request_link_path = 'reset-password'
      subject = "Reset Your PerceptionSelling.ai Password"
    end

    @request_link = "#{@base_app}/#{request_link_path}?request_code=#{@token_request.request_code}"

    mail(to: @token_request.email, subject: subject)
  end
end
