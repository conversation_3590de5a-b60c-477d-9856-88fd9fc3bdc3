"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-transform";
exports.ids = ["vendor-chunks/prosemirror-transform"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-transform/dist/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/prosemirror-transform/dist/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddMarkStep: () => (/* binding */ AddMarkStep),\n/* harmony export */   AddNodeMarkStep: () => (/* binding */ AddNodeMarkStep),\n/* harmony export */   AttrStep: () => (/* binding */ AttrStep),\n/* harmony export */   DocAttrStep: () => (/* binding */ DocAttrStep),\n/* harmony export */   MapResult: () => (/* binding */ MapResult),\n/* harmony export */   Mapping: () => (/* binding */ Mapping),\n/* harmony export */   RemoveMarkStep: () => (/* binding */ RemoveMarkStep),\n/* harmony export */   RemoveNodeMarkStep: () => (/* binding */ RemoveNodeMarkStep),\n/* harmony export */   ReplaceAroundStep: () => (/* binding */ ReplaceAroundStep),\n/* harmony export */   ReplaceStep: () => (/* binding */ ReplaceStep),\n/* harmony export */   Step: () => (/* binding */ Step),\n/* harmony export */   StepMap: () => (/* binding */ StepMap),\n/* harmony export */   StepResult: () => (/* binding */ StepResult),\n/* harmony export */   Transform: () => (/* binding */ Transform),\n/* harmony export */   TransformError: () => (/* binding */ TransformError),\n/* harmony export */   canJoin: () => (/* binding */ canJoin),\n/* harmony export */   canSplit: () => (/* binding */ canSplit),\n/* harmony export */   dropPoint: () => (/* binding */ dropPoint),\n/* harmony export */   findWrapping: () => (/* binding */ findWrapping),\n/* harmony export */   insertPoint: () => (/* binding */ insertPoint),\n/* harmony export */   joinPoint: () => (/* binding */ joinPoint),\n/* harmony export */   liftTarget: () => (/* binding */ liftTarget),\n/* harmony export */   replaceStep: () => (/* binding */ replaceStep)\n/* harmony export */ });\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/prosemirror-model/dist/index.js\");\n\n\n// Recovery values encode a range index and an offset. They are\n// represented as numbers, because tons of them will be created when\n// mapping, for example, a large number of decorations. The number's\n// lower 16 bits provide the index, the remaining bits the offset.\n//\n// Note: We intentionally don't use bit shift operators to en- and\n// decode these, since those clip to 32 bits, which we might in rare\n// cases want to overflow. A 64-bit float can represent 48-bit\n// integers precisely.\nconst lower16 = 0xffff;\nconst factor16 = Math.pow(2, 16);\nfunction makeRecover(index, offset) { return index + offset * factor16; }\nfunction recoverIndex(value) { return value & lower16; }\nfunction recoverOffset(value) { return (value - (value & lower16)) / factor16; }\nconst DEL_BEFORE = 1, DEL_AFTER = 2, DEL_ACROSS = 4, DEL_SIDE = 8;\n/**\nAn object representing a mapped position with extra\ninformation.\n*/\nclass MapResult {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The mapped version of the position.\n    */\n    pos, \n    /**\n    @internal\n    */\n    delInfo, \n    /**\n    @internal\n    */\n    recover) {\n        this.pos = pos;\n        this.delInfo = delInfo;\n        this.recover = recover;\n    }\n    /**\n    Tells you whether the position was deleted, that is, whether the\n    step removed the token on the side queried (via the `assoc`)\n    argument from the document.\n    */\n    get deleted() { return (this.delInfo & DEL_SIDE) > 0; }\n    /**\n    Tells you whether the token before the mapped position was deleted.\n    */\n    get deletedBefore() { return (this.delInfo & (DEL_BEFORE | DEL_ACROSS)) > 0; }\n    /**\n    True when the token after the mapped position was deleted.\n    */\n    get deletedAfter() { return (this.delInfo & (DEL_AFTER | DEL_ACROSS)) > 0; }\n    /**\n    Tells whether any of the steps mapped through deletes across the\n    position (including both the token before and after the\n    position).\n    */\n    get deletedAcross() { return (this.delInfo & DEL_ACROSS) > 0; }\n}\n/**\nA map describing the deletions and insertions made by a step, which\ncan be used to find the correspondence between positions in the\npre-step version of a document and the same position in the\npost-step version.\n*/\nclass StepMap {\n    /**\n    Create a position map. The modifications to the document are\n    represented as an array of numbers, in which each group of three\n    represents a modified chunk as `[start, oldSize, newSize]`.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    ranges, \n    /**\n    @internal\n    */\n    inverted = false) {\n        this.ranges = ranges;\n        this.inverted = inverted;\n        if (!ranges.length && StepMap.empty)\n            return StepMap.empty;\n    }\n    /**\n    @internal\n    */\n    recover(value) {\n        let diff = 0, index = recoverIndex(value);\n        if (!this.inverted)\n            for (let i = 0; i < index; i++)\n                diff += this.ranges[i * 3 + 2] - this.ranges[i * 3 + 1];\n        return this.ranges[index * 3] + diff + recoverOffset(value);\n    }\n    mapResult(pos, assoc = 1) { return this._map(pos, assoc, false); }\n    map(pos, assoc = 1) { return this._map(pos, assoc, true); }\n    /**\n    @internal\n    */\n    _map(pos, assoc, simple) {\n        let diff = 0, oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i] - (this.inverted ? diff : 0);\n            if (start > pos)\n                break;\n            let oldSize = this.ranges[i + oldIndex], newSize = this.ranges[i + newIndex], end = start + oldSize;\n            if (pos <= end) {\n                let side = !oldSize ? assoc : pos == start ? -1 : pos == end ? 1 : assoc;\n                let result = start + diff + (side < 0 ? 0 : newSize);\n                if (simple)\n                    return result;\n                let recover = pos == (assoc < 0 ? start : end) ? null : makeRecover(i / 3, pos - start);\n                let del = pos == start ? DEL_AFTER : pos == end ? DEL_BEFORE : DEL_ACROSS;\n                if (assoc < 0 ? pos != start : pos != end)\n                    del |= DEL_SIDE;\n                return new MapResult(result, del, recover);\n            }\n            diff += newSize - oldSize;\n        }\n        return simple ? pos + diff : new MapResult(pos + diff, 0, null);\n    }\n    /**\n    @internal\n    */\n    touches(pos, recover) {\n        let diff = 0, index = recoverIndex(recover);\n        let oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i] - (this.inverted ? diff : 0);\n            if (start > pos)\n                break;\n            let oldSize = this.ranges[i + oldIndex], end = start + oldSize;\n            if (pos <= end && i == index * 3)\n                return true;\n            diff += this.ranges[i + newIndex] - oldSize;\n        }\n        return false;\n    }\n    /**\n    Calls the given function on each of the changed ranges included in\n    this map.\n    */\n    forEach(f) {\n        let oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0, diff = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i], oldStart = start - (this.inverted ? diff : 0), newStart = start + (this.inverted ? 0 : diff);\n            let oldSize = this.ranges[i + oldIndex], newSize = this.ranges[i + newIndex];\n            f(oldStart, oldStart + oldSize, newStart, newStart + newSize);\n            diff += newSize - oldSize;\n        }\n    }\n    /**\n    Create an inverted version of this map. The result can be used to\n    map positions in the post-step document to the pre-step document.\n    */\n    invert() {\n        return new StepMap(this.ranges, !this.inverted);\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return (this.inverted ? \"-\" : \"\") + JSON.stringify(this.ranges);\n    }\n    /**\n    Create a map that moves all positions by offset `n` (which may be\n    negative). This can be useful when applying steps meant for a\n    sub-document to a larger document, or vice-versa.\n    */\n    static offset(n) {\n        return n == 0 ? StepMap.empty : new StepMap(n < 0 ? [0, -n, 0] : [0, 0, n]);\n    }\n}\n/**\nA StepMap that contains no changed ranges.\n*/\nStepMap.empty = new StepMap([]);\n/**\nA mapping represents a pipeline of zero or more [step\nmaps](https://prosemirror.net/docs/ref/#transform.StepMap). It has special provisions for losslessly\nhandling mapping positions through a series of steps in which some\nsteps are inverted versions of earlier steps. (This comes up when\n‘[rebasing](https://prosemirror.net/docs/guide/#transform.rebasing)’ steps for\ncollaboration or history management.)\n*/\nclass Mapping {\n    /**\n    Create a new mapping with the given position maps.\n    */\n    constructor(maps, \n    /**\n    @internal\n    */\n    mirror, \n    /**\n    The starting position in the `maps` array, used when `map` or\n    `mapResult` is called.\n    */\n    from = 0, \n    /**\n    The end position in the `maps` array.\n    */\n    to = maps ? maps.length : 0) {\n        this.mirror = mirror;\n        this.from = from;\n        this.to = to;\n        this._maps = maps || [];\n        this.ownData = !(maps || mirror);\n    }\n    /**\n    The step maps in this mapping.\n    */\n    get maps() { return this._maps; }\n    /**\n    Create a mapping that maps only through a part of this one.\n    */\n    slice(from = 0, to = this.maps.length) {\n        return new Mapping(this._maps, this.mirror, from, to);\n    }\n    /**\n    Add a step map to the end of this mapping. If `mirrors` is\n    given, it should be the index of the step map that is the mirror\n    image of this one.\n    */\n    appendMap(map, mirrors) {\n        if (!this.ownData) {\n            this._maps = this._maps.slice();\n            this.mirror = this.mirror && this.mirror.slice();\n            this.ownData = true;\n        }\n        this.to = this._maps.push(map);\n        if (mirrors != null)\n            this.setMirror(this._maps.length - 1, mirrors);\n    }\n    /**\n    Add all the step maps in a given mapping to this one (preserving\n    mirroring information).\n    */\n    appendMapping(mapping) {\n        for (let i = 0, startSize = this._maps.length; i < mapping._maps.length; i++) {\n            let mirr = mapping.getMirror(i);\n            this.appendMap(mapping._maps[i], mirr != null && mirr < i ? startSize + mirr : undefined);\n        }\n    }\n    /**\n    Finds the offset of the step map that mirrors the map at the\n    given offset, in this mapping (as per the second argument to\n    `appendMap`).\n    */\n    getMirror(n) {\n        if (this.mirror)\n            for (let i = 0; i < this.mirror.length; i++)\n                if (this.mirror[i] == n)\n                    return this.mirror[i + (i % 2 ? -1 : 1)];\n    }\n    /**\n    @internal\n    */\n    setMirror(n, m) {\n        if (!this.mirror)\n            this.mirror = [];\n        this.mirror.push(n, m);\n    }\n    /**\n    Append the inverse of the given mapping to this one.\n    */\n    appendMappingInverted(mapping) {\n        for (let i = mapping.maps.length - 1, totalSize = this._maps.length + mapping._maps.length; i >= 0; i--) {\n            let mirr = mapping.getMirror(i);\n            this.appendMap(mapping._maps[i].invert(), mirr != null && mirr > i ? totalSize - mirr - 1 : undefined);\n        }\n    }\n    /**\n    Create an inverted version of this mapping.\n    */\n    invert() {\n        let inverse = new Mapping;\n        inverse.appendMappingInverted(this);\n        return inverse;\n    }\n    /**\n    Map a position through this mapping.\n    */\n    map(pos, assoc = 1) {\n        if (this.mirror)\n            return this._map(pos, assoc, true);\n        for (let i = this.from; i < this.to; i++)\n            pos = this._maps[i].map(pos, assoc);\n        return pos;\n    }\n    /**\n    Map a position through this mapping, returning a mapping\n    result.\n    */\n    mapResult(pos, assoc = 1) { return this._map(pos, assoc, false); }\n    /**\n    @internal\n    */\n    _map(pos, assoc, simple) {\n        let delInfo = 0;\n        for (let i = this.from; i < this.to; i++) {\n            let map = this._maps[i], result = map.mapResult(pos, assoc);\n            if (result.recover != null) {\n                let corr = this.getMirror(i);\n                if (corr != null && corr > i && corr < this.to) {\n                    i = corr;\n                    pos = this._maps[corr].recover(result.recover);\n                    continue;\n                }\n            }\n            delInfo |= result.delInfo;\n            pos = result.pos;\n        }\n        return simple ? pos : new MapResult(pos, delInfo, null);\n    }\n}\n\nconst stepsByID = Object.create(null);\n/**\nA step object represents an atomic change. It generally applies\nonly to the document it was created for, since the positions\nstored in it will only make sense for that document.\n\nNew steps are defined by creating classes that extend `Step`,\noverriding the `apply`, `invert`, `map`, `getMap` and `fromJSON`\nmethods, and registering your class with a unique\nJSON-serialization identifier using\n[`Step.jsonID`](https://prosemirror.net/docs/ref/#transform.Step^jsonID).\n*/\nclass Step {\n    /**\n    Get the step map that represents the changes made by this step,\n    and which can be used to transform between positions in the old\n    and the new document.\n    */\n    getMap() { return StepMap.empty; }\n    /**\n    Try to merge this step with another one, to be applied directly\n    after it. Returns the merged step when possible, null if the\n    steps can't be merged.\n    */\n    merge(other) { return null; }\n    /**\n    Deserialize a step from its JSON representation. Will call\n    through to the step class' own implementation of this method.\n    */\n    static fromJSON(schema, json) {\n        if (!json || !json.stepType)\n            throw new RangeError(\"Invalid input for Step.fromJSON\");\n        let type = stepsByID[json.stepType];\n        if (!type)\n            throw new RangeError(`No step type ${json.stepType} defined`);\n        return type.fromJSON(schema, json);\n    }\n    /**\n    To be able to serialize steps to JSON, each step needs a string\n    ID to attach to its JSON representation. Use this method to\n    register an ID for your step classes. Try to pick something\n    that's unlikely to clash with steps from other modules.\n    */\n    static jsonID(id, stepClass) {\n        if (id in stepsByID)\n            throw new RangeError(\"Duplicate use of step JSON ID \" + id);\n        stepsByID[id] = stepClass;\n        stepClass.prototype.jsonID = id;\n        return stepClass;\n    }\n}\n/**\nThe result of [applying](https://prosemirror.net/docs/ref/#transform.Step.apply) a step. Contains either a\nnew document or a failure value.\n*/\nclass StepResult {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The transformed document, if successful.\n    */\n    doc, \n    /**\n    The failure message, if unsuccessful.\n    */\n    failed) {\n        this.doc = doc;\n        this.failed = failed;\n    }\n    /**\n    Create a successful step result.\n    */\n    static ok(doc) { return new StepResult(doc, null); }\n    /**\n    Create a failed step result.\n    */\n    static fail(message) { return new StepResult(null, message); }\n    /**\n    Call [`Node.replace`](https://prosemirror.net/docs/ref/#model.Node.replace) with the given\n    arguments. Create a successful result if it succeeds, and a\n    failed one if it throws a `ReplaceError`.\n    */\n    static fromReplace(doc, from, to, slice) {\n        try {\n            return StepResult.ok(doc.replace(from, to, slice));\n        }\n        catch (e) {\n            if (e instanceof prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.ReplaceError)\n                return StepResult.fail(e.message);\n            throw e;\n        }\n    }\n}\n\nfunction mapFragment(fragment, f, parent) {\n    let mapped = [];\n    for (let i = 0; i < fragment.childCount; i++) {\n        let child = fragment.child(i);\n        if (child.content.size)\n            child = child.copy(mapFragment(child.content, f, child));\n        if (child.isInline)\n            child = f(child, parent, i);\n        mapped.push(child);\n    }\n    return prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.fromArray(mapped);\n}\n/**\nAdd a mark to all inline content between two positions.\n*/\nclass AddMarkStep extends Step {\n    /**\n    Create a mark step.\n    */\n    constructor(\n    /**\n    The start of the marked range.\n    */\n    from, \n    /**\n    The end of the marked range.\n    */\n    to, \n    /**\n    The mark to add.\n    */\n    mark) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let oldSlice = doc.slice(this.from, this.to), $from = doc.resolve(this.from);\n        let parent = $from.node($from.sharedDepth(this.to));\n        let slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(mapFragment(oldSlice.content, (node, parent) => {\n            if (!node.isAtom || !parent.type.allowsMarkType(this.mark.type))\n                return node;\n            return node.mark(this.mark.addToSet(node.marks));\n        }, parent), oldSlice.openStart, oldSlice.openEnd);\n        return StepResult.fromReplace(doc, this.from, this.to, slice);\n    }\n    invert() {\n        return new RemoveMarkStep(this.from, this.to, this.mark);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deleted && to.deleted || from.pos >= to.pos)\n            return null;\n        return new AddMarkStep(from.pos, to.pos, this.mark);\n    }\n    merge(other) {\n        if (other instanceof AddMarkStep &&\n            other.mark.eq(this.mark) &&\n            this.from <= other.to && this.to >= other.from)\n            return new AddMarkStep(Math.min(this.from, other.from), Math.max(this.to, other.to), this.mark);\n        return null;\n    }\n    toJSON() {\n        return { stepType: \"addMark\", mark: this.mark.toJSON(),\n            from: this.from, to: this.to };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for AddMarkStep.fromJSON\");\n        return new AddMarkStep(json.from, json.to, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"addMark\", AddMarkStep);\n/**\nRemove a mark from all inline content between two positions.\n*/\nclass RemoveMarkStep extends Step {\n    /**\n    Create a mark-removing step.\n    */\n    constructor(\n    /**\n    The start of the unmarked range.\n    */\n    from, \n    /**\n    The end of the unmarked range.\n    */\n    to, \n    /**\n    The mark to remove.\n    */\n    mark) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let oldSlice = doc.slice(this.from, this.to);\n        let slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(mapFragment(oldSlice.content, node => {\n            return node.mark(this.mark.removeFromSet(node.marks));\n        }, doc), oldSlice.openStart, oldSlice.openEnd);\n        return StepResult.fromReplace(doc, this.from, this.to, slice);\n    }\n    invert() {\n        return new AddMarkStep(this.from, this.to, this.mark);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deleted && to.deleted || from.pos >= to.pos)\n            return null;\n        return new RemoveMarkStep(from.pos, to.pos, this.mark);\n    }\n    merge(other) {\n        if (other instanceof RemoveMarkStep &&\n            other.mark.eq(this.mark) &&\n            this.from <= other.to && this.to >= other.from)\n            return new RemoveMarkStep(Math.min(this.from, other.from), Math.max(this.to, other.to), this.mark);\n        return null;\n    }\n    toJSON() {\n        return { stepType: \"removeMark\", mark: this.mark.toJSON(),\n            from: this.from, to: this.to };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for RemoveMarkStep.fromJSON\");\n        return new RemoveMarkStep(json.from, json.to, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"removeMark\", RemoveMarkStep);\n/**\nAdd a mark to a specific node.\n*/\nclass AddNodeMarkStep extends Step {\n    /**\n    Create a node mark step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The mark to add.\n    */\n    mark) {\n        super();\n        this.pos = pos;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at mark step's position\");\n        let updated = node.type.create(node.attrs, null, this.mark.addToSet(node.marks));\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    invert(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (node) {\n            let newSet = this.mark.addToSet(node.marks);\n            if (newSet.length == node.marks.length) {\n                for (let i = 0; i < node.marks.length; i++)\n                    if (!node.marks[i].isInSet(newSet))\n                        return new AddNodeMarkStep(this.pos, node.marks[i]);\n                return new AddNodeMarkStep(this.pos, this.mark);\n            }\n        }\n        return new RemoveNodeMarkStep(this.pos, this.mark);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new AddNodeMarkStep(pos.pos, this.mark);\n    }\n    toJSON() {\n        return { stepType: \"addNodeMark\", pos: this.pos, mark: this.mark.toJSON() };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for AddNodeMarkStep.fromJSON\");\n        return new AddNodeMarkStep(json.pos, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"addNodeMark\", AddNodeMarkStep);\n/**\nRemove a mark from a specific node.\n*/\nclass RemoveNodeMarkStep extends Step {\n    /**\n    Create a mark-removing step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The mark to remove.\n    */\n    mark) {\n        super();\n        this.pos = pos;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at mark step's position\");\n        let updated = node.type.create(node.attrs, null, this.mark.removeFromSet(node.marks));\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    invert(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node || !this.mark.isInSet(node.marks))\n            return this;\n        return new AddNodeMarkStep(this.pos, this.mark);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new RemoveNodeMarkStep(pos.pos, this.mark);\n    }\n    toJSON() {\n        return { stepType: \"removeNodeMark\", pos: this.pos, mark: this.mark.toJSON() };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for RemoveNodeMarkStep.fromJSON\");\n        return new RemoveNodeMarkStep(json.pos, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"removeNodeMark\", RemoveNodeMarkStep);\n\n/**\nReplace a part of the document with a slice of new content.\n*/\nclass ReplaceStep extends Step {\n    /**\n    The given `slice` should fit the 'gap' between `from` and\n    `to`—the depths must line up, and the surrounding nodes must be\n    able to be joined with the open sides of the slice. When\n    `structure` is true, the step will fail if the content between\n    from and to is not just a sequence of closing and then opening\n    tokens (this is to guard against rebased replace steps\n    overwriting something they weren't supposed to).\n    */\n    constructor(\n    /**\n    The start position of the replaced range.\n    */\n    from, \n    /**\n    The end position of the replaced range.\n    */\n    to, \n    /**\n    The slice to insert.\n    */\n    slice, \n    /**\n    @internal\n    */\n    structure = false) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.slice = slice;\n        this.structure = structure;\n    }\n    apply(doc) {\n        if (this.structure && contentBetween(doc, this.from, this.to))\n            return StepResult.fail(\"Structure replace would overwrite content\");\n        return StepResult.fromReplace(doc, this.from, this.to, this.slice);\n    }\n    getMap() {\n        return new StepMap([this.from, this.to - this.from, this.slice.size]);\n    }\n    invert(doc) {\n        return new ReplaceStep(this.from, this.from + this.slice.size, doc.slice(this.from, this.to));\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deletedAcross && to.deletedAcross)\n            return null;\n        return new ReplaceStep(from.pos, Math.max(from.pos, to.pos), this.slice, this.structure);\n    }\n    merge(other) {\n        if (!(other instanceof ReplaceStep) || other.structure || this.structure)\n            return null;\n        if (this.from + this.slice.size == other.from && !this.slice.openEnd && !other.slice.openStart) {\n            let slice = this.slice.size + other.slice.size == 0 ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty\n                : new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(this.slice.content.append(other.slice.content), this.slice.openStart, other.slice.openEnd);\n            return new ReplaceStep(this.from, this.to + (other.to - other.from), slice, this.structure);\n        }\n        else if (other.to == this.from && !this.slice.openStart && !other.slice.openEnd) {\n            let slice = this.slice.size + other.slice.size == 0 ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty\n                : new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(other.slice.content.append(this.slice.content), other.slice.openStart, this.slice.openEnd);\n            return new ReplaceStep(other.from, this.to, slice, this.structure);\n        }\n        else {\n            return null;\n        }\n    }\n    toJSON() {\n        let json = { stepType: \"replace\", from: this.from, to: this.to };\n        if (this.slice.size)\n            json.slice = this.slice.toJSON();\n        if (this.structure)\n            json.structure = true;\n        return json;\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for ReplaceStep.fromJSON\");\n        return new ReplaceStep(json.from, json.to, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.fromJSON(schema, json.slice), !!json.structure);\n    }\n}\nStep.jsonID(\"replace\", ReplaceStep);\n/**\nReplace a part of the document with a slice of content, but\npreserve a range of the replaced content by moving it into the\nslice.\n*/\nclass ReplaceAroundStep extends Step {\n    /**\n    Create a replace-around step with the given range and gap.\n    `insert` should be the point in the slice into which the content\n    of the gap should be moved. `structure` has the same meaning as\n    it has in the [`ReplaceStep`](https://prosemirror.net/docs/ref/#transform.ReplaceStep) class.\n    */\n    constructor(\n    /**\n    The start position of the replaced range.\n    */\n    from, \n    /**\n    The end position of the replaced range.\n    */\n    to, \n    /**\n    The start of preserved range.\n    */\n    gapFrom, \n    /**\n    The end of preserved range.\n    */\n    gapTo, \n    /**\n    The slice to insert.\n    */\n    slice, \n    /**\n    The position in the slice where the preserved range should be\n    inserted.\n    */\n    insert, \n    /**\n    @internal\n    */\n    structure = false) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.gapFrom = gapFrom;\n        this.gapTo = gapTo;\n        this.slice = slice;\n        this.insert = insert;\n        this.structure = structure;\n    }\n    apply(doc) {\n        if (this.structure && (contentBetween(doc, this.from, this.gapFrom) ||\n            contentBetween(doc, this.gapTo, this.to)))\n            return StepResult.fail(\"Structure gap-replace would overwrite content\");\n        let gap = doc.slice(this.gapFrom, this.gapTo);\n        if (gap.openStart || gap.openEnd)\n            return StepResult.fail(\"Gap is not a flat range\");\n        let inserted = this.slice.insertAt(this.insert, gap.content);\n        if (!inserted)\n            return StepResult.fail(\"Content does not fit in gap\");\n        return StepResult.fromReplace(doc, this.from, this.to, inserted);\n    }\n    getMap() {\n        return new StepMap([this.from, this.gapFrom - this.from, this.insert,\n            this.gapTo, this.to - this.gapTo, this.slice.size - this.insert]);\n    }\n    invert(doc) {\n        let gap = this.gapTo - this.gapFrom;\n        return new ReplaceAroundStep(this.from, this.from + this.slice.size + gap, this.from + this.insert, this.from + this.insert + gap, doc.slice(this.from, this.to).removeBetween(this.gapFrom - this.from, this.gapTo - this.from), this.gapFrom - this.from, this.structure);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        let gapFrom = this.from == this.gapFrom ? from.pos : mapping.map(this.gapFrom, -1);\n        let gapTo = this.to == this.gapTo ? to.pos : mapping.map(this.gapTo, 1);\n        if ((from.deletedAcross && to.deletedAcross) || gapFrom < from.pos || gapTo > to.pos)\n            return null;\n        return new ReplaceAroundStep(from.pos, to.pos, gapFrom, gapTo, this.slice, this.insert, this.structure);\n    }\n    toJSON() {\n        let json = { stepType: \"replaceAround\", from: this.from, to: this.to,\n            gapFrom: this.gapFrom, gapTo: this.gapTo, insert: this.insert };\n        if (this.slice.size)\n            json.slice = this.slice.toJSON();\n        if (this.structure)\n            json.structure = true;\n        return json;\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\" ||\n            typeof json.gapFrom != \"number\" || typeof json.gapTo != \"number\" || typeof json.insert != \"number\")\n            throw new RangeError(\"Invalid input for ReplaceAroundStep.fromJSON\");\n        return new ReplaceAroundStep(json.from, json.to, json.gapFrom, json.gapTo, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.fromJSON(schema, json.slice), json.insert, !!json.structure);\n    }\n}\nStep.jsonID(\"replaceAround\", ReplaceAroundStep);\nfunction contentBetween(doc, from, to) {\n    let $from = doc.resolve(from), dist = to - from, depth = $from.depth;\n    while (dist > 0 && depth > 0 && $from.indexAfter(depth) == $from.node(depth).childCount) {\n        depth--;\n        dist--;\n    }\n    if (dist > 0) {\n        let next = $from.node(depth).maybeChild($from.indexAfter(depth));\n        while (dist > 0) {\n            if (!next || next.isLeaf)\n                return true;\n            next = next.firstChild;\n            dist--;\n        }\n    }\n    return false;\n}\n\nfunction addMark(tr, from, to, mark) {\n    let removed = [], added = [];\n    let removing, adding;\n    tr.doc.nodesBetween(from, to, (node, pos, parent) => {\n        if (!node.isInline)\n            return;\n        let marks = node.marks;\n        if (!mark.isInSet(marks) && parent.type.allowsMarkType(mark.type)) {\n            let start = Math.max(pos, from), end = Math.min(pos + node.nodeSize, to);\n            let newSet = mark.addToSet(marks);\n            for (let i = 0; i < marks.length; i++) {\n                if (!marks[i].isInSet(newSet)) {\n                    if (removing && removing.to == start && removing.mark.eq(marks[i]))\n                        removing.to = end;\n                    else\n                        removed.push(removing = new RemoveMarkStep(start, end, marks[i]));\n                }\n            }\n            if (adding && adding.to == start)\n                adding.to = end;\n            else\n                added.push(adding = new AddMarkStep(start, end, mark));\n        }\n    });\n    removed.forEach(s => tr.step(s));\n    added.forEach(s => tr.step(s));\n}\nfunction removeMark(tr, from, to, mark) {\n    let matched = [], step = 0;\n    tr.doc.nodesBetween(from, to, (node, pos) => {\n        if (!node.isInline)\n            return;\n        step++;\n        let toRemove = null;\n        if (mark instanceof prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.MarkType) {\n            let set = node.marks, found;\n            while (found = mark.isInSet(set)) {\n                (toRemove || (toRemove = [])).push(found);\n                set = found.removeFromSet(set);\n            }\n        }\n        else if (mark) {\n            if (mark.isInSet(node.marks))\n                toRemove = [mark];\n        }\n        else {\n            toRemove = node.marks;\n        }\n        if (toRemove && toRemove.length) {\n            let end = Math.min(pos + node.nodeSize, to);\n            for (let i = 0; i < toRemove.length; i++) {\n                let style = toRemove[i], found;\n                for (let j = 0; j < matched.length; j++) {\n                    let m = matched[j];\n                    if (m.step == step - 1 && style.eq(matched[j].style))\n                        found = m;\n                }\n                if (found) {\n                    found.to = end;\n                    found.step = step;\n                }\n                else {\n                    matched.push({ style, from: Math.max(pos, from), to: end, step });\n                }\n            }\n        }\n    });\n    matched.forEach(m => tr.step(new RemoveMarkStep(m.from, m.to, m.style)));\n}\nfunction clearIncompatible(tr, pos, parentType, match = parentType.contentMatch, clearNewlines = true) {\n    let node = tr.doc.nodeAt(pos);\n    let replSteps = [], cur = pos + 1;\n    for (let i = 0; i < node.childCount; i++) {\n        let child = node.child(i), end = cur + child.nodeSize;\n        let allowed = match.matchType(child.type);\n        if (!allowed) {\n            replSteps.push(new ReplaceStep(cur, end, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty));\n        }\n        else {\n            match = allowed;\n            for (let j = 0; j < child.marks.length; j++)\n                if (!parentType.allowsMarkType(child.marks[j].type))\n                    tr.step(new RemoveMarkStep(cur, end, child.marks[j]));\n            if (clearNewlines && child.isText && parentType.whitespace != \"pre\") {\n                let m, newline = /\\r?\\n|\\r/g, slice;\n                while (m = newline.exec(child.text)) {\n                    if (!slice)\n                        slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(parentType.schema.text(\" \", parentType.allowedMarks(child.marks))), 0, 0);\n                    replSteps.push(new ReplaceStep(cur + m.index, cur + m.index + m[0].length, slice));\n                }\n            }\n        }\n        cur = end;\n    }\n    if (!match.validEnd) {\n        let fill = match.fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, true);\n        tr.replace(cur, cur, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(fill, 0, 0));\n    }\n    for (let i = replSteps.length - 1; i >= 0; i--)\n        tr.step(replSteps[i]);\n}\n\nfunction canCut(node, start, end) {\n    return (start == 0 || node.canReplace(start, node.childCount)) &&\n        (end == node.childCount || node.canReplace(0, end));\n}\n/**\nTry to find a target depth to which the content in the given range\ncan be lifted. Will not go across\n[isolating](https://prosemirror.net/docs/ref/#model.NodeSpec.isolating) parent nodes.\n*/\nfunction liftTarget(range) {\n    let parent = range.parent;\n    let content = parent.content.cutByIndex(range.startIndex, range.endIndex);\n    for (let depth = range.depth;; --depth) {\n        let node = range.$from.node(depth);\n        let index = range.$from.index(depth), endIndex = range.$to.indexAfter(depth);\n        if (depth < range.depth && node.canReplace(index, endIndex, content))\n            return depth;\n        if (depth == 0 || node.type.spec.isolating || !canCut(node, index, endIndex))\n            break;\n    }\n    return null;\n}\nfunction lift(tr, range, target) {\n    let { $from, $to, depth } = range;\n    let gapStart = $from.before(depth + 1), gapEnd = $to.after(depth + 1);\n    let start = gapStart, end = gapEnd;\n    let before = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, openStart = 0;\n    for (let d = depth, splitting = false; d > target; d--)\n        if (splitting || $from.index(d) > 0) {\n            splitting = true;\n            before = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($from.node(d).copy(before));\n            openStart++;\n        }\n        else {\n            start--;\n        }\n    let after = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, openEnd = 0;\n    for (let d = depth, splitting = false; d > target; d--)\n        if (splitting || $to.after(d + 1) < $to.end(d)) {\n            splitting = true;\n            after = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($to.node(d).copy(after));\n            openEnd++;\n        }\n        else {\n            end++;\n        }\n    tr.step(new ReplaceAroundStep(start, end, gapStart, gapEnd, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(before.append(after), openStart, openEnd), before.size - openStart, true));\n}\n/**\nTry to find a valid way to wrap the content in the given range in a\nnode of the given type. May introduce extra nodes around and inside\nthe wrapper node, if necessary. Returns null if no valid wrapping\ncould be found. When `innerRange` is given, that range's content is\nused as the content to fit into the wrapping, instead of the\ncontent of `range`.\n*/\nfunction findWrapping(range, nodeType, attrs = null, innerRange = range) {\n    let around = findWrappingOutside(range, nodeType);\n    let inner = around && findWrappingInside(innerRange, nodeType);\n    if (!inner)\n        return null;\n    return around.map(withAttrs)\n        .concat({ type: nodeType, attrs }).concat(inner.map(withAttrs));\n}\nfunction withAttrs(type) { return { type, attrs: null }; }\nfunction findWrappingOutside(range, type) {\n    let { parent, startIndex, endIndex } = range;\n    let around = parent.contentMatchAt(startIndex).findWrapping(type);\n    if (!around)\n        return null;\n    let outer = around.length ? around[0] : type;\n    return parent.canReplaceWith(startIndex, endIndex, outer) ? around : null;\n}\nfunction findWrappingInside(range, type) {\n    let { parent, startIndex, endIndex } = range;\n    let inner = parent.child(startIndex);\n    let inside = type.contentMatch.findWrapping(inner.type);\n    if (!inside)\n        return null;\n    let lastType = inside.length ? inside[inside.length - 1] : type;\n    let innerMatch = lastType.contentMatch;\n    for (let i = startIndex; innerMatch && i < endIndex; i++)\n        innerMatch = innerMatch.matchType(parent.child(i).type);\n    if (!innerMatch || !innerMatch.validEnd)\n        return null;\n    return inside;\n}\nfunction wrap(tr, range, wrappers) {\n    let content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty;\n    for (let i = wrappers.length - 1; i >= 0; i--) {\n        if (content.size) {\n            let match = wrappers[i].type.contentMatch.matchFragment(content);\n            if (!match || !match.validEnd)\n                throw new RangeError(\"Wrapper type given to Transform.wrap does not form valid content of its parent wrapper\");\n        }\n        content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(wrappers[i].type.create(wrappers[i].attrs, content));\n    }\n    let start = range.start, end = range.end;\n    tr.step(new ReplaceAroundStep(start, end, start, end, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(content, 0, 0), wrappers.length, true));\n}\nfunction setBlockType(tr, from, to, type, attrs) {\n    if (!type.isTextblock)\n        throw new RangeError(\"Type given to setBlockType should be a textblock\");\n    let mapFrom = tr.steps.length;\n    tr.doc.nodesBetween(from, to, (node, pos) => {\n        let attrsHere = typeof attrs == \"function\" ? attrs(node) : attrs;\n        if (node.isTextblock && !node.hasMarkup(type, attrsHere) &&\n            canChangeType(tr.doc, tr.mapping.slice(mapFrom).map(pos), type)) {\n            let convertNewlines = null;\n            if (type.schema.linebreakReplacement) {\n                let pre = type.whitespace == \"pre\", supportLinebreak = !!type.contentMatch.matchType(type.schema.linebreakReplacement);\n                if (pre && !supportLinebreak)\n                    convertNewlines = false;\n                else if (!pre && supportLinebreak)\n                    convertNewlines = true;\n            }\n            // Ensure all markup that isn't allowed in the new node type is cleared\n            if (convertNewlines === false)\n                replaceLinebreaks(tr, node, pos, mapFrom);\n            clearIncompatible(tr, tr.mapping.slice(mapFrom).map(pos, 1), type, undefined, convertNewlines === null);\n            let mapping = tr.mapping.slice(mapFrom);\n            let startM = mapping.map(pos, 1), endM = mapping.map(pos + node.nodeSize, 1);\n            tr.step(new ReplaceAroundStep(startM, endM, startM + 1, endM - 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(type.create(attrsHere, null, node.marks)), 0, 0), 1, true));\n            if (convertNewlines === true)\n                replaceNewlines(tr, node, pos, mapFrom);\n            return false;\n        }\n    });\n}\nfunction replaceNewlines(tr, node, pos, mapFrom) {\n    node.forEach((child, offset) => {\n        if (child.isText) {\n            let m, newline = /\\r?\\n|\\r/g;\n            while (m = newline.exec(child.text)) {\n                let start = tr.mapping.slice(mapFrom).map(pos + 1 + offset + m.index);\n                tr.replaceWith(start, start + 1, node.type.schema.linebreakReplacement.create());\n            }\n        }\n    });\n}\nfunction replaceLinebreaks(tr, node, pos, mapFrom) {\n    node.forEach((child, offset) => {\n        if (child.type == child.type.schema.linebreakReplacement) {\n            let start = tr.mapping.slice(mapFrom).map(pos + 1 + offset);\n            tr.replaceWith(start, start + 1, node.type.schema.text(\"\\n\"));\n        }\n    });\n}\nfunction canChangeType(doc, pos, type) {\n    let $pos = doc.resolve(pos), index = $pos.index();\n    return $pos.parent.canReplaceWith(index, index + 1, type);\n}\n/**\nChange the type, attributes, and/or marks of the node at `pos`.\nWhen `type` isn't given, the existing node type is preserved,\n*/\nfunction setNodeMarkup(tr, pos, type, attrs, marks) {\n    let node = tr.doc.nodeAt(pos);\n    if (!node)\n        throw new RangeError(\"No node at given position\");\n    if (!type)\n        type = node.type;\n    let newNode = type.create(attrs, null, marks || node.marks);\n    if (node.isLeaf)\n        return tr.replaceWith(pos, pos + node.nodeSize, newNode);\n    if (!type.validContent(node.content))\n        throw new RangeError(\"Invalid content for node type \" + type.name);\n    tr.step(new ReplaceAroundStep(pos, pos + node.nodeSize, pos + 1, pos + node.nodeSize - 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(newNode), 0, 0), 1, true));\n}\n/**\nCheck whether splitting at the given position is allowed.\n*/\nfunction canSplit(doc, pos, depth = 1, typesAfter) {\n    let $pos = doc.resolve(pos), base = $pos.depth - depth;\n    let innerType = (typesAfter && typesAfter[typesAfter.length - 1]) || $pos.parent;\n    if (base < 0 || $pos.parent.type.spec.isolating ||\n        !$pos.parent.canReplace($pos.index(), $pos.parent.childCount) ||\n        !innerType.type.validContent($pos.parent.content.cutByIndex($pos.index(), $pos.parent.childCount)))\n        return false;\n    for (let d = $pos.depth - 1, i = depth - 2; d > base; d--, i--) {\n        let node = $pos.node(d), index = $pos.index(d);\n        if (node.type.spec.isolating)\n            return false;\n        let rest = node.content.cutByIndex(index, node.childCount);\n        let overrideChild = typesAfter && typesAfter[i + 1];\n        if (overrideChild)\n            rest = rest.replaceChild(0, overrideChild.type.create(overrideChild.attrs));\n        let after = (typesAfter && typesAfter[i]) || node;\n        if (!node.canReplace(index + 1, node.childCount) || !after.type.validContent(rest))\n            return false;\n    }\n    let index = $pos.indexAfter(base);\n    let baseType = typesAfter && typesAfter[0];\n    return $pos.node(base).canReplaceWith(index, index, baseType ? baseType.type : $pos.node(base + 1).type);\n}\nfunction split(tr, pos, depth = 1, typesAfter) {\n    let $pos = tr.doc.resolve(pos), before = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, after = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty;\n    for (let d = $pos.depth, e = $pos.depth - depth, i = depth - 1; d > e; d--, i--) {\n        before = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($pos.node(d).copy(before));\n        let typeAfter = typesAfter && typesAfter[i];\n        after = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(typeAfter ? typeAfter.type.create(typeAfter.attrs, after) : $pos.node(d).copy(after));\n    }\n    tr.step(new ReplaceStep(pos, pos, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(before.append(after), depth, depth), true));\n}\n/**\nTest whether the blocks before and after a given position can be\njoined.\n*/\nfunction canJoin(doc, pos) {\n    let $pos = doc.resolve(pos), index = $pos.index();\n    return joinable($pos.nodeBefore, $pos.nodeAfter) &&\n        $pos.parent.canReplace(index, index + 1);\n}\nfunction canAppendWithSubstitutedLinebreaks(a, b) {\n    if (!b.content.size)\n        a.type.compatibleContent(b.type);\n    let match = a.contentMatchAt(a.childCount);\n    let { linebreakReplacement } = a.type.schema;\n    for (let i = 0; i < b.childCount; i++) {\n        let child = b.child(i);\n        let type = child.type == linebreakReplacement ? a.type.schema.nodes.text : child.type;\n        match = match.matchType(type);\n        if (!match)\n            return false;\n        if (!a.type.allowsMarks(child.marks))\n            return false;\n    }\n    return match.validEnd;\n}\nfunction joinable(a, b) {\n    return !!(a && b && !a.isLeaf && canAppendWithSubstitutedLinebreaks(a, b));\n}\n/**\nFind an ancestor of the given position that can be joined to the\nblock before (or after if `dir` is positive). Returns the joinable\npoint, if any.\n*/\nfunction joinPoint(doc, pos, dir = -1) {\n    let $pos = doc.resolve(pos);\n    for (let d = $pos.depth;; d--) {\n        let before, after, index = $pos.index(d);\n        if (d == $pos.depth) {\n            before = $pos.nodeBefore;\n            after = $pos.nodeAfter;\n        }\n        else if (dir > 0) {\n            before = $pos.node(d + 1);\n            index++;\n            after = $pos.node(d).maybeChild(index);\n        }\n        else {\n            before = $pos.node(d).maybeChild(index - 1);\n            after = $pos.node(d + 1);\n        }\n        if (before && !before.isTextblock && joinable(before, after) &&\n            $pos.node(d).canReplace(index, index + 1))\n            return pos;\n        if (d == 0)\n            break;\n        pos = dir < 0 ? $pos.before(d) : $pos.after(d);\n    }\n}\nfunction join(tr, pos, depth) {\n    let convertNewlines = null;\n    let { linebreakReplacement } = tr.doc.type.schema;\n    let $before = tr.doc.resolve(pos - depth), beforeType = $before.node().type;\n    if (linebreakReplacement && beforeType.inlineContent) {\n        let pre = beforeType.whitespace == \"pre\";\n        let supportLinebreak = !!beforeType.contentMatch.matchType(linebreakReplacement);\n        if (pre && !supportLinebreak)\n            convertNewlines = false;\n        else if (!pre && supportLinebreak)\n            convertNewlines = true;\n    }\n    let mapFrom = tr.steps.length;\n    if (convertNewlines === false) {\n        let $after = tr.doc.resolve(pos + depth);\n        replaceLinebreaks(tr, $after.node(), $after.before(), mapFrom);\n    }\n    if (beforeType.inlineContent)\n        clearIncompatible(tr, pos + depth - 1, beforeType, $before.node().contentMatchAt($before.index()), convertNewlines == null);\n    let mapping = tr.mapping.slice(mapFrom), start = mapping.map(pos - depth);\n    tr.step(new ReplaceStep(start, mapping.map(pos + depth, -1), prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty, true));\n    if (convertNewlines === true) {\n        let $full = tr.doc.resolve(start);\n        replaceNewlines(tr, $full.node(), $full.before(), tr.steps.length);\n    }\n    return tr;\n}\n/**\nTry to find a point where a node of the given type can be inserted\nnear `pos`, by searching up the node hierarchy when `pos` itself\nisn't a valid place but is at the start or end of a node. Return\nnull if no position was found.\n*/\nfunction insertPoint(doc, pos, nodeType) {\n    let $pos = doc.resolve(pos);\n    if ($pos.parent.canReplaceWith($pos.index(), $pos.index(), nodeType))\n        return pos;\n    if ($pos.parentOffset == 0)\n        for (let d = $pos.depth - 1; d >= 0; d--) {\n            let index = $pos.index(d);\n            if ($pos.node(d).canReplaceWith(index, index, nodeType))\n                return $pos.before(d + 1);\n            if (index > 0)\n                return null;\n        }\n    if ($pos.parentOffset == $pos.parent.content.size)\n        for (let d = $pos.depth - 1; d >= 0; d--) {\n            let index = $pos.indexAfter(d);\n            if ($pos.node(d).canReplaceWith(index, index, nodeType))\n                return $pos.after(d + 1);\n            if (index < $pos.node(d).childCount)\n                return null;\n        }\n    return null;\n}\n/**\nFinds a position at or around the given position where the given\nslice can be inserted. Will look at parent nodes' nearest boundary\nand try there, even if the original position wasn't directly at the\nstart or end of that node. Returns null when no position was found.\n*/\nfunction dropPoint(doc, pos, slice) {\n    let $pos = doc.resolve(pos);\n    if (!slice.content.size)\n        return pos;\n    let content = slice.content;\n    for (let i = 0; i < slice.openStart; i++)\n        content = content.firstChild.content;\n    for (let pass = 1; pass <= (slice.openStart == 0 && slice.size ? 2 : 1); pass++) {\n        for (let d = $pos.depth; d >= 0; d--) {\n            let bias = d == $pos.depth ? 0 : $pos.pos <= ($pos.start(d + 1) + $pos.end(d + 1)) / 2 ? -1 : 1;\n            let insertPos = $pos.index(d) + (bias > 0 ? 1 : 0);\n            let parent = $pos.node(d), fits = false;\n            if (pass == 1) {\n                fits = parent.canReplace(insertPos, insertPos, content);\n            }\n            else {\n                let wrapping = parent.contentMatchAt(insertPos).findWrapping(content.firstChild.type);\n                fits = wrapping && parent.canReplaceWith(insertPos, insertPos, wrapping[0]);\n            }\n            if (fits)\n                return bias == 0 ? $pos.pos : bias < 0 ? $pos.before(d + 1) : $pos.after(d + 1);\n        }\n    }\n    return null;\n}\n\n/**\n‘Fit’ a slice into a given position in the document, producing a\n[step](https://prosemirror.net/docs/ref/#transform.Step) that inserts it. Will return null if\nthere's no meaningful way to insert the slice here, or inserting it\nwould be a no-op (an empty slice over an empty range).\n*/\nfunction replaceStep(doc, from, to = from, slice = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n    if (from == to && !slice.size)\n        return null;\n    let $from = doc.resolve(from), $to = doc.resolve(to);\n    // Optimization -- avoid work if it's obvious that it's not needed.\n    if (fitsTrivially($from, $to, slice))\n        return new ReplaceStep(from, to, slice);\n    return new Fitter($from, $to, slice).fit();\n}\nfunction fitsTrivially($from, $to, slice) {\n    return !slice.openStart && !slice.openEnd && $from.start() == $to.start() &&\n        $from.parent.canReplace($from.index(), $to.index(), slice.content);\n}\n// Algorithm for 'placing' the elements of a slice into a gap:\n//\n// We consider the content of each node that is open to the left to be\n// independently placeable. I.e. in <p(\"foo\"), p(\"bar\")>, when the\n// paragraph on the left is open, \"foo\" can be placed (somewhere on\n// the left side of the replacement gap) independently from p(\"bar\").\n//\n// This class tracks the state of the placement progress in the\n// following properties:\n//\n//  - `frontier` holds a stack of `{type, match}` objects that\n//    represent the open side of the replacement. It starts at\n//    `$from`, then moves forward as content is placed, and is finally\n//    reconciled with `$to`.\n//\n//  - `unplaced` is a slice that represents the content that hasn't\n//    been placed yet.\n//\n//  - `placed` is a fragment of placed content. Its open-start value\n//    is implicit in `$from`, and its open-end value in `frontier`.\nclass Fitter {\n    constructor($from, $to, unplaced) {\n        this.$from = $from;\n        this.$to = $to;\n        this.unplaced = unplaced;\n        this.frontier = [];\n        this.placed = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty;\n        for (let i = 0; i <= $from.depth; i++) {\n            let node = $from.node(i);\n            this.frontier.push({\n                type: node.type,\n                match: node.contentMatchAt($from.indexAfter(i))\n            });\n        }\n        for (let i = $from.depth; i > 0; i--)\n            this.placed = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($from.node(i).copy(this.placed));\n    }\n    get depth() { return this.frontier.length - 1; }\n    fit() {\n        // As long as there's unplaced content, try to place some of it.\n        // If that fails, either increase the open score of the unplaced\n        // slice, or drop nodes from it, and then try again.\n        while (this.unplaced.size) {\n            let fit = this.findFittable();\n            if (fit)\n                this.placeNodes(fit);\n            else\n                this.openMore() || this.dropNode();\n        }\n        // When there's inline content directly after the frontier _and_\n        // directly after `this.$to`, we must generate a `ReplaceAround`\n        // step that pulls that content into the node after the frontier.\n        // That means the fitting must be done to the end of the textblock\n        // node after `this.$to`, not `this.$to` itself.\n        let moveInline = this.mustMoveInline(), placedSize = this.placed.size - this.depth - this.$from.depth;\n        let $from = this.$from, $to = this.close(moveInline < 0 ? this.$to : $from.doc.resolve(moveInline));\n        if (!$to)\n            return null;\n        // If closing to `$to` succeeded, create a step\n        let content = this.placed, openStart = $from.depth, openEnd = $to.depth;\n        while (openStart && openEnd && content.childCount == 1) { // Normalize by dropping open parent nodes\n            content = content.firstChild.content;\n            openStart--;\n            openEnd--;\n        }\n        let slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(content, openStart, openEnd);\n        if (moveInline > -1)\n            return new ReplaceAroundStep($from.pos, moveInline, this.$to.pos, this.$to.end(), slice, placedSize);\n        if (slice.size || $from.pos != this.$to.pos) // Don't generate no-op steps\n            return new ReplaceStep($from.pos, $to.pos, slice);\n        return null;\n    }\n    // Find a position on the start spine of `this.unplaced` that has\n    // content that can be moved somewhere on the frontier. Returns two\n    // depths, one for the slice and one for the frontier.\n    findFittable() {\n        let startDepth = this.unplaced.openStart;\n        for (let cur = this.unplaced.content, d = 0, openEnd = this.unplaced.openEnd; d < startDepth; d++) {\n            let node = cur.firstChild;\n            if (cur.childCount > 1)\n                openEnd = 0;\n            if (node.type.spec.isolating && openEnd <= d) {\n                startDepth = d;\n                break;\n            }\n            cur = node.content;\n        }\n        // Only try wrapping nodes (pass 2) after finding a place without\n        // wrapping failed.\n        for (let pass = 1; pass <= 2; pass++) {\n            for (let sliceDepth = pass == 1 ? startDepth : this.unplaced.openStart; sliceDepth >= 0; sliceDepth--) {\n                let fragment, parent = null;\n                if (sliceDepth) {\n                    parent = contentAt(this.unplaced.content, sliceDepth - 1).firstChild;\n                    fragment = parent.content;\n                }\n                else {\n                    fragment = this.unplaced.content;\n                }\n                let first = fragment.firstChild;\n                for (let frontierDepth = this.depth; frontierDepth >= 0; frontierDepth--) {\n                    let { type, match } = this.frontier[frontierDepth], wrap, inject = null;\n                    // In pass 1, if the next node matches, or there is no next\n                    // node but the parents look compatible, we've found a\n                    // place.\n                    if (pass == 1 && (first ? match.matchType(first.type) || (inject = match.fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(first), false))\n                        : parent && type.compatibleContent(parent.type)))\n                        return { sliceDepth, frontierDepth, parent, inject };\n                    // In pass 2, look for a set of wrapping nodes that make\n                    // `first` fit here.\n                    else if (pass == 2 && first && (wrap = match.findWrapping(first.type)))\n                        return { sliceDepth, frontierDepth, parent, wrap };\n                    // Don't continue looking further up if the parent node\n                    // would fit here.\n                    if (parent && match.matchType(parent.type))\n                        break;\n                }\n            }\n        }\n    }\n    openMore() {\n        let { content, openStart, openEnd } = this.unplaced;\n        let inner = contentAt(content, openStart);\n        if (!inner.childCount || inner.firstChild.isLeaf)\n            return false;\n        this.unplaced = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(content, openStart + 1, Math.max(openEnd, inner.size + openStart >= content.size - openEnd ? openStart + 1 : 0));\n        return true;\n    }\n    dropNode() {\n        let { content, openStart, openEnd } = this.unplaced;\n        let inner = contentAt(content, openStart);\n        if (inner.childCount <= 1 && openStart > 0) {\n            let openAtEnd = content.size - openStart <= openStart + inner.size;\n            this.unplaced = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(dropFromFragment(content, openStart - 1, 1), openStart - 1, openAtEnd ? openStart - 1 : openEnd);\n        }\n        else {\n            this.unplaced = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(dropFromFragment(content, openStart, 1), openStart, openEnd);\n        }\n    }\n    // Move content from the unplaced slice at `sliceDepth` to the\n    // frontier node at `frontierDepth`. Close that frontier node when\n    // applicable.\n    placeNodes({ sliceDepth, frontierDepth, parent, inject, wrap }) {\n        while (this.depth > frontierDepth)\n            this.closeFrontierNode();\n        if (wrap)\n            for (let i = 0; i < wrap.length; i++)\n                this.openFrontierNode(wrap[i]);\n        let slice = this.unplaced, fragment = parent ? parent.content : slice.content;\n        let openStart = slice.openStart - sliceDepth;\n        let taken = 0, add = [];\n        let { match, type } = this.frontier[frontierDepth];\n        if (inject) {\n            for (let i = 0; i < inject.childCount; i++)\n                add.push(inject.child(i));\n            match = match.matchFragment(inject);\n        }\n        // Computes the amount of (end) open nodes at the end of the\n        // fragment. When 0, the parent is open, but no more. When\n        // negative, nothing is open.\n        let openEndCount = (fragment.size + sliceDepth) - (slice.content.size - slice.openEnd);\n        // Scan over the fragment, fitting as many child nodes as\n        // possible.\n        while (taken < fragment.childCount) {\n            let next = fragment.child(taken), matches = match.matchType(next.type);\n            if (!matches)\n                break;\n            taken++;\n            if (taken > 1 || openStart == 0 || next.content.size) { // Drop empty open nodes\n                match = matches;\n                add.push(closeNodeStart(next.mark(type.allowedMarks(next.marks)), taken == 1 ? openStart : 0, taken == fragment.childCount ? openEndCount : -1));\n            }\n        }\n        let toEnd = taken == fragment.childCount;\n        if (!toEnd)\n            openEndCount = -1;\n        this.placed = addToFragment(this.placed, frontierDepth, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(add));\n        this.frontier[frontierDepth].match = match;\n        // If the parent types match, and the entire node was moved, and\n        // it's not open, close this frontier node right away.\n        if (toEnd && openEndCount < 0 && parent && parent.type == this.frontier[this.depth].type && this.frontier.length > 1)\n            this.closeFrontierNode();\n        // Add new frontier nodes for any open nodes at the end.\n        for (let i = 0, cur = fragment; i < openEndCount; i++) {\n            let node = cur.lastChild;\n            this.frontier.push({ type: node.type, match: node.contentMatchAt(node.childCount) });\n            cur = node.content;\n        }\n        // Update `this.unplaced`. Drop the entire node from which we\n        // placed it we got to its end, otherwise just drop the placed\n        // nodes.\n        this.unplaced = !toEnd ? new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(dropFromFragment(slice.content, sliceDepth, taken), slice.openStart, slice.openEnd)\n            : sliceDepth == 0 ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty\n                : new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(dropFromFragment(slice.content, sliceDepth - 1, 1), sliceDepth - 1, openEndCount < 0 ? slice.openEnd : sliceDepth - 1);\n    }\n    mustMoveInline() {\n        if (!this.$to.parent.isTextblock)\n            return -1;\n        let top = this.frontier[this.depth], level;\n        if (!top.type.isTextblock || !contentAfterFits(this.$to, this.$to.depth, top.type, top.match, false) ||\n            (this.$to.depth == this.depth && (level = this.findCloseLevel(this.$to)) && level.depth == this.depth))\n            return -1;\n        let { depth } = this.$to, after = this.$to.after(depth);\n        while (depth > 1 && after == this.$to.end(--depth))\n            ++after;\n        return after;\n    }\n    findCloseLevel($to) {\n        scan: for (let i = Math.min(this.depth, $to.depth); i >= 0; i--) {\n            let { match, type } = this.frontier[i];\n            let dropInner = i < $to.depth && $to.end(i + 1) == $to.pos + ($to.depth - (i + 1));\n            let fit = contentAfterFits($to, i, type, match, dropInner);\n            if (!fit)\n                continue;\n            for (let d = i - 1; d >= 0; d--) {\n                let { match, type } = this.frontier[d];\n                let matches = contentAfterFits($to, d, type, match, true);\n                if (!matches || matches.childCount)\n                    continue scan;\n            }\n            return { depth: i, fit, move: dropInner ? $to.doc.resolve($to.after(i + 1)) : $to };\n        }\n    }\n    close($to) {\n        let close = this.findCloseLevel($to);\n        if (!close)\n            return null;\n        while (this.depth > close.depth)\n            this.closeFrontierNode();\n        if (close.fit.childCount)\n            this.placed = addToFragment(this.placed, close.depth, close.fit);\n        $to = close.move;\n        for (let d = close.depth + 1; d <= $to.depth; d++) {\n            let node = $to.node(d), add = node.type.contentMatch.fillBefore(node.content, true, $to.index(d));\n            this.openFrontierNode(node.type, node.attrs, add);\n        }\n        return $to;\n    }\n    openFrontierNode(type, attrs = null, content) {\n        let top = this.frontier[this.depth];\n        top.match = top.match.matchType(type);\n        this.placed = addToFragment(this.placed, this.depth, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(type.create(attrs, content)));\n        this.frontier.push({ type, match: type.contentMatch });\n    }\n    closeFrontierNode() {\n        let open = this.frontier.pop();\n        let add = open.match.fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, true);\n        if (add.childCount)\n            this.placed = addToFragment(this.placed, this.frontier.length, add);\n    }\n}\nfunction dropFromFragment(fragment, depth, count) {\n    if (depth == 0)\n        return fragment.cutByIndex(count, fragment.childCount);\n    return fragment.replaceChild(0, fragment.firstChild.copy(dropFromFragment(fragment.firstChild.content, depth - 1, count)));\n}\nfunction addToFragment(fragment, depth, content) {\n    if (depth == 0)\n        return fragment.append(content);\n    return fragment.replaceChild(fragment.childCount - 1, fragment.lastChild.copy(addToFragment(fragment.lastChild.content, depth - 1, content)));\n}\nfunction contentAt(fragment, depth) {\n    for (let i = 0; i < depth; i++)\n        fragment = fragment.firstChild.content;\n    return fragment;\n}\nfunction closeNodeStart(node, openStart, openEnd) {\n    if (openStart <= 0)\n        return node;\n    let frag = node.content;\n    if (openStart > 1)\n        frag = frag.replaceChild(0, closeNodeStart(frag.firstChild, openStart - 1, frag.childCount == 1 ? openEnd - 1 : 0));\n    if (openStart > 0) {\n        frag = node.type.contentMatch.fillBefore(frag).append(frag);\n        if (openEnd <= 0)\n            frag = frag.append(node.type.contentMatch.matchFragment(frag).fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, true));\n    }\n    return node.copy(frag);\n}\nfunction contentAfterFits($to, depth, type, match, open) {\n    let node = $to.node(depth), index = open ? $to.indexAfter(depth) : $to.index(depth);\n    if (index == node.childCount && !type.compatibleContent(node.type))\n        return null;\n    let fit = match.fillBefore(node.content, true, index);\n    return fit && !invalidMarks(type, node.content, index) ? fit : null;\n}\nfunction invalidMarks(type, fragment, start) {\n    for (let i = start; i < fragment.childCount; i++)\n        if (!type.allowsMarks(fragment.child(i).marks))\n            return true;\n    return false;\n}\nfunction definesContent(type) {\n    return type.spec.defining || type.spec.definingForContent;\n}\nfunction replaceRange(tr, from, to, slice) {\n    if (!slice.size)\n        return tr.deleteRange(from, to);\n    let $from = tr.doc.resolve(from), $to = tr.doc.resolve(to);\n    if (fitsTrivially($from, $to, slice))\n        return tr.step(new ReplaceStep(from, to, slice));\n    let targetDepths = coveredDepths($from, tr.doc.resolve(to));\n    // Can't replace the whole document, so remove 0 if it's present\n    if (targetDepths[targetDepths.length - 1] == 0)\n        targetDepths.pop();\n    // Negative numbers represent not expansion over the whole node at\n    // that depth, but replacing from $from.before(-D) to $to.pos.\n    let preferredTarget = -($from.depth + 1);\n    targetDepths.unshift(preferredTarget);\n    // This loop picks a preferred target depth, if one of the covering\n    // depths is not outside of a defining node, and adds negative\n    // depths for any depth that has $from at its start and does not\n    // cross a defining node.\n    for (let d = $from.depth, pos = $from.pos - 1; d > 0; d--, pos--) {\n        let spec = $from.node(d).type.spec;\n        if (spec.defining || spec.definingAsContext || spec.isolating)\n            break;\n        if (targetDepths.indexOf(d) > -1)\n            preferredTarget = d;\n        else if ($from.before(d) == pos)\n            targetDepths.splice(1, 0, -d);\n    }\n    // Try to fit each possible depth of the slice into each possible\n    // target depth, starting with the preferred depths.\n    let preferredTargetIndex = targetDepths.indexOf(preferredTarget);\n    let leftNodes = [], preferredDepth = slice.openStart;\n    for (let content = slice.content, i = 0;; i++) {\n        let node = content.firstChild;\n        leftNodes.push(node);\n        if (i == slice.openStart)\n            break;\n        content = node.content;\n    }\n    // Back up preferredDepth to cover defining textblocks directly\n    // above it, possibly skipping a non-defining textblock.\n    for (let d = preferredDepth - 1; d >= 0; d--) {\n        let leftNode = leftNodes[d], def = definesContent(leftNode.type);\n        if (def && !leftNode.sameMarkup($from.node(Math.abs(preferredTarget) - 1)))\n            preferredDepth = d;\n        else if (def || !leftNode.type.isTextblock)\n            break;\n    }\n    for (let j = slice.openStart; j >= 0; j--) {\n        let openDepth = (j + preferredDepth + 1) % (slice.openStart + 1);\n        let insert = leftNodes[openDepth];\n        if (!insert)\n            continue;\n        for (let i = 0; i < targetDepths.length; i++) {\n            // Loop over possible expansion levels, starting with the\n            // preferred one\n            let targetDepth = targetDepths[(i + preferredTargetIndex) % targetDepths.length], expand = true;\n            if (targetDepth < 0) {\n                expand = false;\n                targetDepth = -targetDepth;\n            }\n            let parent = $from.node(targetDepth - 1), index = $from.index(targetDepth - 1);\n            if (parent.canReplaceWith(index, index, insert.type, insert.marks))\n                return tr.replace($from.before(targetDepth), expand ? $to.after(targetDepth) : to, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(closeFragment(slice.content, 0, slice.openStart, openDepth), openDepth, slice.openEnd));\n        }\n    }\n    let startSteps = tr.steps.length;\n    for (let i = targetDepths.length - 1; i >= 0; i--) {\n        tr.replace(from, to, slice);\n        if (tr.steps.length > startSteps)\n            break;\n        let depth = targetDepths[i];\n        if (depth < 0)\n            continue;\n        from = $from.before(depth);\n        to = $to.after(depth);\n    }\n}\nfunction closeFragment(fragment, depth, oldOpen, newOpen, parent) {\n    if (depth < oldOpen) {\n        let first = fragment.firstChild;\n        fragment = fragment.replaceChild(0, first.copy(closeFragment(first.content, depth + 1, oldOpen, newOpen, first)));\n    }\n    if (depth > newOpen) {\n        let match = parent.contentMatchAt(0);\n        let start = match.fillBefore(fragment).append(fragment);\n        fragment = start.append(match.matchFragment(start).fillBefore(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty, true));\n    }\n    return fragment;\n}\nfunction replaceRangeWith(tr, from, to, node) {\n    if (!node.isInline && from == to && tr.doc.resolve(from).parent.content.size) {\n        let point = insertPoint(tr.doc, from, node.type);\n        if (point != null)\n            from = to = point;\n    }\n    tr.replaceRange(from, to, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(node), 0, 0));\n}\nfunction deleteRange(tr, from, to) {\n    let $from = tr.doc.resolve(from), $to = tr.doc.resolve(to);\n    let covered = coveredDepths($from, $to);\n    for (let i = 0; i < covered.length; i++) {\n        let depth = covered[i], last = i == covered.length - 1;\n        if ((last && depth == 0) || $from.node(depth).type.contentMatch.validEnd)\n            return tr.delete($from.start(depth), $to.end(depth));\n        if (depth > 0 && (last || $from.node(depth - 1).canReplace($from.index(depth - 1), $to.indexAfter(depth - 1))))\n            return tr.delete($from.before(depth), $to.after(depth));\n    }\n    for (let d = 1; d <= $from.depth && d <= $to.depth; d++) {\n        if (from - $from.start(d) == $from.depth - d && to > $from.end(d) && $to.end(d) - to != $to.depth - d &&\n            $from.start(d - 1) == $to.start(d - 1) && $from.node(d - 1).canReplace($from.index(d - 1), $to.index(d - 1)))\n            return tr.delete($from.before(d), to);\n    }\n    tr.delete(from, to);\n}\n// Returns an array of all depths for which $from - $to spans the\n// whole content of the nodes at that depth.\nfunction coveredDepths($from, $to) {\n    let result = [], minDepth = Math.min($from.depth, $to.depth);\n    for (let d = minDepth; d >= 0; d--) {\n        let start = $from.start(d);\n        if (start < $from.pos - ($from.depth - d) ||\n            $to.end(d) > $to.pos + ($to.depth - d) ||\n            $from.node(d).type.spec.isolating ||\n            $to.node(d).type.spec.isolating)\n            break;\n        if (start == $to.start(d) ||\n            (d == $from.depth && d == $to.depth && $from.parent.inlineContent && $to.parent.inlineContent &&\n                d && $to.start(d - 1) == start - 1))\n            result.push(d);\n    }\n    return result;\n}\n\n/**\nUpdate an attribute in a specific node.\n*/\nclass AttrStep extends Step {\n    /**\n    Construct an attribute step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The attribute to set.\n    */\n    attr, \n    // The attribute's new value.\n    value) {\n        super();\n        this.pos = pos;\n        this.attr = attr;\n        this.value = value;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at attribute step's position\");\n        let attrs = Object.create(null);\n        for (let name in node.attrs)\n            attrs[name] = node.attrs[name];\n        attrs[this.attr] = this.value;\n        let updated = node.type.create(attrs, null, node.marks);\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    getMap() {\n        return StepMap.empty;\n    }\n    invert(doc) {\n        return new AttrStep(this.pos, this.attr, doc.nodeAt(this.pos).attrs[this.attr]);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new AttrStep(pos.pos, this.attr, this.value);\n    }\n    toJSON() {\n        return { stepType: \"attr\", pos: this.pos, attr: this.attr, value: this.value };\n    }\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\" || typeof json.attr != \"string\")\n            throw new RangeError(\"Invalid input for AttrStep.fromJSON\");\n        return new AttrStep(json.pos, json.attr, json.value);\n    }\n}\nStep.jsonID(\"attr\", AttrStep);\n/**\nUpdate an attribute in the doc node.\n*/\nclass DocAttrStep extends Step {\n    /**\n    Construct an attribute step.\n    */\n    constructor(\n    /**\n    The attribute to set.\n    */\n    attr, \n    // The attribute's new value.\n    value) {\n        super();\n        this.attr = attr;\n        this.value = value;\n    }\n    apply(doc) {\n        let attrs = Object.create(null);\n        for (let name in doc.attrs)\n            attrs[name] = doc.attrs[name];\n        attrs[this.attr] = this.value;\n        let updated = doc.type.create(attrs, doc.content, doc.marks);\n        return StepResult.ok(updated);\n    }\n    getMap() {\n        return StepMap.empty;\n    }\n    invert(doc) {\n        return new DocAttrStep(this.attr, doc.attrs[this.attr]);\n    }\n    map(mapping) {\n        return this;\n    }\n    toJSON() {\n        return { stepType: \"docAttr\", attr: this.attr, value: this.value };\n    }\n    static fromJSON(schema, json) {\n        if (typeof json.attr != \"string\")\n            throw new RangeError(\"Invalid input for DocAttrStep.fromJSON\");\n        return new DocAttrStep(json.attr, json.value);\n    }\n}\nStep.jsonID(\"docAttr\", DocAttrStep);\n\n/**\n@internal\n*/\nlet TransformError = class extends Error {\n};\nTransformError = function TransformError(message) {\n    let err = Error.call(this, message);\n    err.__proto__ = TransformError.prototype;\n    return err;\n};\nTransformError.prototype = Object.create(Error.prototype);\nTransformError.prototype.constructor = TransformError;\nTransformError.prototype.name = \"TransformError\";\n/**\nAbstraction to build up and track an array of\n[steps](https://prosemirror.net/docs/ref/#transform.Step) representing a document transformation.\n\nMost transforming methods return the `Transform` object itself, so\nthat they can be chained.\n*/\nclass Transform {\n    /**\n    Create a transform that starts with the given document.\n    */\n    constructor(\n    /**\n    The current document (the result of applying the steps in the\n    transform).\n    */\n    doc) {\n        this.doc = doc;\n        /**\n        The steps in this transform.\n        */\n        this.steps = [];\n        /**\n        The documents before each of the steps.\n        */\n        this.docs = [];\n        /**\n        A mapping with the maps for each of the steps in this transform.\n        */\n        this.mapping = new Mapping;\n    }\n    /**\n    The starting document.\n    */\n    get before() { return this.docs.length ? this.docs[0] : this.doc; }\n    /**\n    Apply a new step in this transform, saving the result. Throws an\n    error when the step fails.\n    */\n    step(step) {\n        let result = this.maybeStep(step);\n        if (result.failed)\n            throw new TransformError(result.failed);\n        return this;\n    }\n    /**\n    Try to apply a step in this transformation, ignoring it if it\n    fails. Returns the step result.\n    */\n    maybeStep(step) {\n        let result = step.apply(this.doc);\n        if (!result.failed)\n            this.addStep(step, result.doc);\n        return result;\n    }\n    /**\n    True when the document has been changed (when there are any\n    steps).\n    */\n    get docChanged() {\n        return this.steps.length > 0;\n    }\n    /**\n    @internal\n    */\n    addStep(step, doc) {\n        this.docs.push(this.doc);\n        this.steps.push(step);\n        this.mapping.appendMap(step.getMap());\n        this.doc = doc;\n    }\n    /**\n    Replace the part of the document between `from` and `to` with the\n    given `slice`.\n    */\n    replace(from, to = from, slice = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n        let step = replaceStep(this.doc, from, to, slice);\n        if (step)\n            this.step(step);\n        return this;\n    }\n    /**\n    Replace the given range with the given content, which may be a\n    fragment, node, or array of nodes.\n    */\n    replaceWith(from, to, content) {\n        return this.replace(from, to, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(content), 0, 0));\n    }\n    /**\n    Delete the content between the given positions.\n    */\n    delete(from, to) {\n        return this.replace(from, to, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty);\n    }\n    /**\n    Insert the given content at the given position.\n    */\n    insert(pos, content) {\n        return this.replaceWith(pos, pos, content);\n    }\n    /**\n    Replace a range of the document with a given slice, using\n    `from`, `to`, and the slice's\n    [`openStart`](https://prosemirror.net/docs/ref/#model.Slice.openStart) property as hints, rather\n    than fixed start and end points. This method may grow the\n    replaced area or close open nodes in the slice in order to get a\n    fit that is more in line with WYSIWYG expectations, by dropping\n    fully covered parent nodes of the replaced region when they are\n    marked [non-defining as\n    context](https://prosemirror.net/docs/ref/#model.NodeSpec.definingAsContext), or including an\n    open parent node from the slice that _is_ marked as [defining\n    its content](https://prosemirror.net/docs/ref/#model.NodeSpec.definingForContent).\n    \n    This is the method, for example, to handle paste. The similar\n    [`replace`](https://prosemirror.net/docs/ref/#transform.Transform.replace) method is a more\n    primitive tool which will _not_ move the start and end of its given\n    range, and is useful in situations where you need more precise\n    control over what happens.\n    */\n    replaceRange(from, to, slice) {\n        replaceRange(this, from, to, slice);\n        return this;\n    }\n    /**\n    Replace the given range with a node, but use `from` and `to` as\n    hints, rather than precise positions. When from and to are the same\n    and are at the start or end of a parent node in which the given\n    node doesn't fit, this method may _move_ them out towards a parent\n    that does allow the given node to be placed. When the given range\n    completely covers a parent node, this method may completely replace\n    that parent node.\n    */\n    replaceRangeWith(from, to, node) {\n        replaceRangeWith(this, from, to, node);\n        return this;\n    }\n    /**\n    Delete the given range, expanding it to cover fully covered\n    parent nodes until a valid replace is found.\n    */\n    deleteRange(from, to) {\n        deleteRange(this, from, to);\n        return this;\n    }\n    /**\n    Split the content in the given range off from its parent, if there\n    is sibling content before or after it, and move it up the tree to\n    the depth specified by `target`. You'll probably want to use\n    [`liftTarget`](https://prosemirror.net/docs/ref/#transform.liftTarget) to compute `target`, to make\n    sure the lift is valid.\n    */\n    lift(range, target) {\n        lift(this, range, target);\n        return this;\n    }\n    /**\n    Join the blocks around the given position. If depth is 2, their\n    last and first siblings are also joined, and so on.\n    */\n    join(pos, depth = 1) {\n        join(this, pos, depth);\n        return this;\n    }\n    /**\n    Wrap the given [range](https://prosemirror.net/docs/ref/#model.NodeRange) in the given set of wrappers.\n    The wrappers are assumed to be valid in this position, and should\n    probably be computed with [`findWrapping`](https://prosemirror.net/docs/ref/#transform.findWrapping).\n    */\n    wrap(range, wrappers) {\n        wrap(this, range, wrappers);\n        return this;\n    }\n    /**\n    Set the type of all textblocks (partly) between `from` and `to` to\n    the given node type with the given attributes.\n    */\n    setBlockType(from, to = from, type, attrs = null) {\n        setBlockType(this, from, to, type, attrs);\n        return this;\n    }\n    /**\n    Change the type, attributes, and/or marks of the node at `pos`.\n    When `type` isn't given, the existing node type is preserved,\n    */\n    setNodeMarkup(pos, type, attrs = null, marks) {\n        setNodeMarkup(this, pos, type, attrs, marks);\n        return this;\n    }\n    /**\n    Set a single attribute on a given node to a new value.\n    The `pos` addresses the document content. Use `setDocAttribute`\n    to set attributes on the document itself.\n    */\n    setNodeAttribute(pos, attr, value) {\n        this.step(new AttrStep(pos, attr, value));\n        return this;\n    }\n    /**\n    Set a single attribute on the document to a new value.\n    */\n    setDocAttribute(attr, value) {\n        this.step(new DocAttrStep(attr, value));\n        return this;\n    }\n    /**\n    Add a mark to the node at position `pos`.\n    */\n    addNodeMark(pos, mark) {\n        this.step(new AddNodeMarkStep(pos, mark));\n        return this;\n    }\n    /**\n    Remove a mark (or all marks of the given type) from the node at\n    position `pos`.\n    */\n    removeNodeMark(pos, mark) {\n        let node = this.doc.nodeAt(pos);\n        if (!node)\n            throw new RangeError(\"No node at position \" + pos);\n        if (mark instanceof prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Mark) {\n            if (mark.isInSet(node.marks))\n                this.step(new RemoveNodeMarkStep(pos, mark));\n        }\n        else {\n            let set = node.marks, found, steps = [];\n            while (found = mark.isInSet(set)) {\n                steps.push(new RemoveNodeMarkStep(pos, found));\n                set = found.removeFromSet(set);\n            }\n            for (let i = steps.length - 1; i >= 0; i--)\n                this.step(steps[i]);\n        }\n        return this;\n    }\n    /**\n    Split the node at the given position, and optionally, if `depth` is\n    greater than one, any number of nodes above that. By default, the\n    parts split off will inherit the node type of the original node.\n    This can be changed by passing an array of types and attributes to\n    use after the split (with the outermost nodes coming first).\n    */\n    split(pos, depth = 1, typesAfter) {\n        split(this, pos, depth, typesAfter);\n        return this;\n    }\n    /**\n    Add the given mark to the inline content between `from` and `to`.\n    */\n    addMark(from, to, mark) {\n        addMark(this, from, to, mark);\n        return this;\n    }\n    /**\n    Remove marks from inline nodes between `from` and `to`. When\n    `mark` is a single mark, remove precisely that mark. When it is\n    a mark type, remove all marks of that type. When it is null,\n    remove all marks of any type.\n    */\n    removeMark(from, to, mark) {\n        removeMark(this, from, to, mark);\n        return this;\n    }\n    /**\n    Removes all marks and nodes from the content of the node at\n    `pos` that don't match the given new parent node type. Accepts\n    an optional starting [content match](https://prosemirror.net/docs/ref/#model.ContentMatch) as\n    third argument.\n    */\n    clearIncompatible(pos, parentType, match) {\n        clearIncompatible(this, pos, parentType, match);\n        return this;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-transform/dist/index.js\n");

/***/ })

};
;