import { StyleSheet, Text, View, ViewProps } from "@react-pdf/renderer";

export const pdfStyles = StyleSheet.create({
  page: {
    padding: 20,
  },
  heading: {
    fontFamily: "Inter",
    fontSize: 14,
    fontWeight: 600,
    marginBottom: 20,
  },
  metadata: {
    fontFamily: "Inter",
    fontSize: 10,
  },
  table: {
    display: "flex",
    width: "auto",
    borderStyle: "solid",
    borderWidth: 1,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    marginBottom: 20,
  },
  tableTitle: {
    backgroundColor: "#203864",
    color: "white",
    textAlign: "center",
    width: "100%",
    paddingTop: 5,
    paddingBottom: 5,
  },
  tableTitleText: {
    fontSize: 14,
    fontFamily: "Inter",
    fontWeight: 700,
  },
  tableRow: {
    flexDirection: "row",
  },
  tableCol: {
    borderStyle: "solid",
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    padding: 5,
    flex: 1,
  },
  tableHeader: {
    fontSize: 10,
    fontWeight: 600,
    backgroundColor: "#d3dff1",
    textAlign: "center",
  },
  tableCell: {
    fontFamily: "Inter",
    fontSize: 10,
    padding: 2,
  },
});

export const PdfHeading = ({ children }: { children: React.ReactNode }) => {
  return <Text style={pdfStyles.heading}>{children}</Text>;
};

export const PdfMetadataRow = ({ children }: { children: React.ReactNode }) => {
  return (
    <View style={{ display: "flex", flexDirection: "row" }}>{children}</View>
  );
};

export const PdfMetadata = ({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) => {
  return (
    <View style={{ width: 280, marginBottom: 5 }}>
      <Text style={[pdfStyles.metadata, { fontWeight: 600 }]}>{title}: </Text>
      <Text style={pdfStyles.metadata} wrap>
        {children}
      </Text>
    </View>
  );
};

export const PdfMarkdown = ({
  children,
  style = [],
}: {
  children: string;
  style?: ViewProps["style"];
}) => {
  const normalized = children?.replace(/\\n/g, "\n");
  const lines = normalized?.split("\n");

  return (
    <View
      style={[pdfStyles.tableCol, ...(Array.isArray(style) ? style : [style])]}
    >
      {lines
        ?.filter((v) => !!v)
        ?.map((line, idx) => {
          const trimmed = line.trim();

          if (trimmed === "") {
            return <View key={idx} style={{ height: 8 }} />;
          }

          const bulletPrefixes = ["* ", "- ", "\u2022 "];
          const isBullet = bulletPrefixes.some((prefix) =>
            trimmed.startsWith(prefix)
          );

          if (isBullet) {
            const content = trimmed.slice(2).trim();
            return (
              <Text style={[pdfStyles.metadata, { marginBottom: 6 }]} key={idx}>
                {"\u2022"} {content}
              </Text>
            );
          }

          return (
            <Text key={idx} style={[pdfStyles.metadata, { marginBottom: 6 }]}>
              {trimmed}
            </Text>
          );
        })}
    </View>
  );
};

export const PdfTable = ({ children }: { children: React.ReactNode }) => {
  return <View style={pdfStyles.table}>{children}</View>;
};

export const PdfTableTitle = ({ children }: { children: React.ReactNode }) => {
  return (
    <View style={pdfStyles.tableTitle} wrap={false}>
      <Text style={pdfStyles.tableTitleText}>{children}</Text>
    </View>
  );
};

export const PdfTableRow = ({ children }: { children: React.ReactNode }) => {
  return <View style={pdfStyles.tableRow}>{children}</View>;
};

export const PdfTableHeader = ({
  children,
  style = [],
}: {
  style?: ViewProps["style"];
  children: React.ReactNode;
}) => {
  return (
    <View
      style={[
        pdfStyles.tableCol,
        pdfStyles.tableHeader,
        ...(Array.isArray(style) ? style : [style]),
      ]}
    >
      <Text style={pdfStyles.tableCell}>{children}</Text>
    </View>
  );
};

export const PdfTableCell = ({
  style = {},
  variant = "default",
  children,
}: {
  variant?: "primary" | "default";
  style?: ViewProps["style"];
  children: React.ReactNode;
}) => {
  return (
    <View
      style={[
        pdfStyles.tableCol,
        ...(Array.isArray(style) ? style : [style]),
        variant === "primary"
          ? { backgroundColor: "#d3dff1", color: "black" }
          : {},
      ]}
    >
      <Text style={pdfStyles.tableCell}>{children}</Text>
    </View>
  );
};
