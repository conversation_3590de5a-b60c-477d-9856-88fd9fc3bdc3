# frozen_string_literal: true

module V1
    class IndustriesController < ApiController
      authorize_auth_token! :all
  
      def create
        input = ::V1::IndustryCreationInput.new(request_body)
        validate! input, capture_failure: true
  
        industry = service.create(input.output)
  
        render_json industry, use: :format, status: :created
      end

      def update
        input = ::V1::IndustryUpdateInput.new(request_body)
        validate! input, capture_failure: true
  
        industry = service.update(params[:id], input.output)
  
        render_json industry, use: :format, status: :ok
      end
  
      def index
        industries = service.index(query_params)
  
        render_json_array industries, use: :format, status: :ok
      end
  
      def destroy
        service.destroy(params[:id])
  
        render_empty_json({}, status: :ok)
      end
  
      private
  
      def default_output
        ::V1::IndustryOutput
      end

      def service
        @service ||= ::IndustryService.new(current_user_data)
      end
    end
  end
  