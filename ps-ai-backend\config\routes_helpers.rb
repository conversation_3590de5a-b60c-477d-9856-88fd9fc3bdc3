# frozen_string_literal: true

module RoutesHelpers
  def json
    { defaults: { format: :json } }
  end

  def rack_json(status = :ok, json)
    status = Rack::Utils.status_code(status)
    return [status, {}, []] unless json

    string = json.to_json
    [
      status,
      {
        'Content-Type' => 'application/json',
        'Content-Length' => string.bytesize.to_s
      },
      [string]
    ]
  end
end
