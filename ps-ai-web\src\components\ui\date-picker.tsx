"use client";

import { Calendar as CalendarIcon } from "lucide-react";
import * as React from "react";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn, formatDate } from "@/lib/utils";

function DatePicker({
  className,
  date,
  onSelect,
  placeholder = "Pick a date",
  format = "MMMM D, YYYY",
  icon = true,
  onBlur,
  open: controlledOpen,
  onOpenChange,
}: Pick<React.HTMLAttributes<HTMLButtonElement>, "className"> &
  Pick<React.ComponentProps<typeof PopoverContent>, "onBlur"> & {
    placeholder?: string;
    date?: string | null | Date;
    onSelect?: (date?: Date) => void;
    format?: string;
    icon?: boolean;
    open?: boolean;
    onOpenChange?: (open: boolean) => void;
  }) {
  const [uncontrolledOpen, setUncontrolledOpen] = React.useState(false);

  const isControlled = controlledOpen !== undefined;
  const open = isControlled ? controlledOpen : uncontrolledOpen;

  const handleOpenChange = (newOpen: boolean) => {
    if (isControlled) {
      onOpenChange?.(newOpen);
    } else {
      setUncontrolledOpen(newOpen);
    }
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "w-fit justify-start rounded-lg text-left font-normal",
            !date && "text-muted-foreground",
            className
          )}
        >
          {icon && <CalendarIcon className="mr-2 h-4 w-4" />}
          {date ? formatDate(date, format) : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-auto p-0"
        onBlur={(event) => {
          onBlur?.(event);
          handleOpenChange(false);
        }}
        onMouseDown={(event) => event.preventDefault()}
      >
        <Calendar
          mode="single"
          selected={date as Date}
          onSelect={(selectedDate) => {
            onSelect?.(selectedDate);
            handleOpenChange(false);
          }}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}

export { DatePicker };
