# frozen_string_literal: true

module V1
  class MessageOutput < ApiOutput
    def format
      {
        id: @object.id,
        chat_id: @object.chat_id,
        content: @object.content,
        sender: @object.sender,
        created_at: @object.created_at,
        image: url_output(@object.image_url),
        file: url_output(@object.file_url)
      }
    end

    def url_output(url)
      return unless url

      uri = URI.parse(url)
      filename = File.basename(uri.path)
      {
        filename: filename,
        url: url
      }
    end
  end
end
