# frozen_string_literal: true

module V1
  class AccountPlanGroupsController < ApiController
    authorize_auth_token! :all

    def index
      result = service.index(query_params)

      render_json_array result.account_plan_groups,
                        use: :format,
                        account_plans: result.account_plans,
                        organization_users: result.organization_users,
                        industries: result.industries,
                        list_account_plans: true
    end

    def show
      result = service.show(params[:id])

      render_json result.account_plan_group,
                  use: :format,
                  account_plans: result.account_plans,
                  organization_users: result.organization_users,
                  industries: result.industries,
                  list_account_plans: true
    end

    def create
      input = ::V1::AccountPlanGroupCreationInput.new(request_body)
      validate! input, capture_failure: true

      result = service.create(input.output)

      render_json result.account_plan_group,
                  use: :format,
                  status: :created,
                  account_plans: [result.account_plan],
                  list_account_plans: true,
                  organization_users: result.organization_users
    end

    def update
      input = ::V1::AccountPlanGroupUpdateInput.new(request_body)
      validate! input, capture_failure: true

      result = service.update(params[:id], input.output)

      render_json result.account_plan_group,
                  use: :format,
                  status: :ok
    end

    def destroy
      service.destroy(params[:id])

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::AccountPlanGroupOutput
    end

    def service
      @service ||= ::AccountPlanGroupService.new(current_user_data)
    end
  end
end
