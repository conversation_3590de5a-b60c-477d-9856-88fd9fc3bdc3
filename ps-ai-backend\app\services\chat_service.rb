# frozen_string_literal: true

class ChatService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
  end

  def create(params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    
    params = params.slice(
      :account_plan_id, :ap_table_id, :model_id,
      :chat_type, :organization_id, :creator_organization_user_id
    )

    params[:organization_id] = organization_user.organization_id
    params[:creator_organization_user_id] = organization_user.id

    model = Model.find_by(id: params[:model_id])
    authorize! model.openai_assistant_id.present?, on_error: 'Template need to be updated!'

    chat = Chat.new

    ActiveRecord::Base.transaction do
      chat = Chat.create!(params)
    end

    chat
  end

  def update(id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)

    chat = Chat.find(id)

    ActiveRecord::Base.transaction do
      if params[:model_id].present?
        model = Model.find_by(id: params[:model_id])
        authorize! model.openai_assistant_id.present?, on_error: 'Template need to be updated!'
      end

      chat.update(params)
    end

    chat
  end
end
