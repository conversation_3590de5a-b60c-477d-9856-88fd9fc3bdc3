# Login Setup Guide - Connecting Local Development to Fly.io Backend

## 🎯 Problem Summary

The login was failing locally with the error:
```
TypeError: Cannot read properties of undefined (reading '0')
```

But the same credentials worked perfectly on the production Vercel deployment.

## 🔧 Root Causes & Solutions

### 1. **API URL Path Mismatch** ⚠️ **CRITICAL FIX**

**Problem**: The local environment was using the wrong API path.

**Local (Wrong)**: 
```bash
NEXT_PUBLIC_API_URL=https://psai-api-wispy-resonance-3660.fly.dev/api
```

**Production (Correct)**:
```bash
NEXT_PUBLIC_API_URL=https://psai-api-wispy-resonance-3660.fly.dev/v1
```

**Solution**: Updated `.env.local` to use `/v1` path to match production.

### 2. **Error Handling Bug** 🐛 **SECONDARY FIX**

**Problem**: The login form expected error response in format:
```javascript
e.response?.data.errors[0].message
```

But the API might return different error structures, causing the `undefined` error.

**Solution**: Enhanced error handling to support multiple response formats:

```javascript
// Before (Fragile)
const errorMessage = e.response?.data.errors[0].message ?? "";

// After (Robust)
const errorData = e.response?.data;
let errorMessage = "";

if (errorData?.errors && Array.isArray(errorData.errors) && errorData.errors.length > 0) {
  errorMessage = errorData.errors[0].message || "";
} else if (errorData?.message) {
  errorMessage = errorData.message;
} else if (errorData?.error) {
  errorMessage = errorData.error;
}
```

## ✅ Final Working Configuration

### `.env.local` File:
```bash
# Backend API URL - Connect to your Fly.io backend (matching production path)
NEXT_PUBLIC_API_URL=https://psai-api-wispy-resonance-3660.fly.dev/v1

# Development environment flag
NODE_ENV=development

# Optional: Sentry error tracking
SENTRY_AUTH_TOKEN=
```

### Test Credentials:
- **Organization ID**: DoStory
- **Email**: <EMAIL>
- **Password**: DoStoryPS@i

## 🚀 How to Set Up Login Successfully

### Step 1: Environment Configuration
1. Create/update `.env.local` in your project root
2. Set the correct API URL with `/v1` path (NOT `/api`)
3. Ensure the file is named `.env.local` (NOT `.local.env`)

### Step 2: Verify Backend Connection
Your Fly.io backend should be accessible at:
- **Backend API**: `https://psai-api-wispy-resonance-3660.fly.dev`
- **Database**: `perceptionselling-prod-db.fly.dev:5432`

### Step 3: Start Development Server
```bash
npm run dev
```

The server should show:
```
✓ Ready in X.Xs
- Environments: .env.local
```

### Step 4: Test Login
1. Navigate to `http://localhost:3000` (or 3001 if 3000 is busy)
2. Enter your credentials
3. Login should work without errors

## 🔍 Debugging Tips

### If Login Still Fails:

1. **Check Network Tab**: Verify API calls go to `/v1` endpoint
2. **Check Console**: Look for any remaining error messages
3. **Verify Credentials**: Ensure Organization ID, Email, and Password are correct
4. **Test Production**: Confirm credentials work on `https://app.perceptionselling.ai/`

### Common Issues:

- **Wrong API Path**: Must use `/v1`, not `/api`
- **File Name**: Must be `.env.local`, not `.local.env`
- **Case Sensitivity**: Organization ID is case-sensitive
- **Network Issues**: Ensure Fly.io backend is accessible

## 📊 Environment Comparison

| Environment | API URL | Status |
|-------------|---------|--------|
| **Local Dev** | `https://psai-api-wispy-resonance-3660.fly.dev/v1` | ✅ Working |
| **Vercel Production** | `https://api.perceptionselling.ai/v1` | ✅ Working |
| **Vercel Pre-Production** | `https://psai-api.fly.dev/v1` | ✅ Working |

## 🎉 Success Indicators

When everything is working correctly, you should see:
- ✅ No console errors
- ✅ Successful login redirect to dashboard
- ✅ API calls in Network tab show 200 status
- ✅ Authentication token stored properly

---

**Key Takeaway**: The main issue was the API path mismatch (`/api` vs `/v1`). Always ensure your local environment matches your production configuration!
