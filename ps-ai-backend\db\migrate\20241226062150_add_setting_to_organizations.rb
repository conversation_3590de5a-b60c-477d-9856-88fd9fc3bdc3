class AddSettingToOrganizations < ActiveRecord::Migration[7.0]
  def change
    add_column :organizations, :tagline, :text
    add_column :organizations, :message, :text
    add_column :organizations, :image_url, :text
    add_column :organizations, :subdomain, :string
    add_column :organizations, :primary_color, :string, :default => '#203864'
    add_column :organizations, :secondary_color, :string, :default => '#ed7d31'
    add_column :organizations, :ap_wallet_share_color, :string, :default => '#4287f5'
    add_column :organizations, :ap_current_revenue_color, :string, :default => '#4287f5'
    add_column :organizations, :ap_current_opportunity_color, :string, :default => '#4287f5'
    add_column :organizations, :ap_potential_opportunity_color, :string, :default => '#4287f5'
  end
end
