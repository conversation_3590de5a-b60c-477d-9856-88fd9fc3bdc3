class CreateModelTemplates < ActiveRecord::Migration[7.0]
  def change
    create_table :model_templates do |t|
      t.string :name
      t.text :description
      t.integer :max_tokens
      t.float :temperature
      t.text :instruction
      t.text :rules
      t.text :reference_output
      t.references :organization
      t.references :template_category, :null => true
      t.datetime :discarded_at, :index => true

      t.timestamps
    end
  end
end
