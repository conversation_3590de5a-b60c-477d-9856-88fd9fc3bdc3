# frozen_string_literal: true

module V1
  class ModelTemplateCreationInput < ::ApplicationInput
    required(:name)
    optional(:description)
    optional(:max_tokens)
    optional(:temperature)
    optional(:instruction)
    optional(:rules)
    optional(:model)
    optional(:status)
    optional(:reference_output)
    optional(:reference_output_url)
    optional(:inputs)
    optional(:template_category_id)
  end
end
