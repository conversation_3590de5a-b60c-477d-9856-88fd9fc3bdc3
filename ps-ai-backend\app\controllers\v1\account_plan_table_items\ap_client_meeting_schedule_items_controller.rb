# frozen_string_literal: true

module V1
  class AccountPlanTableItems::ApClientMeetingScheduleItemsController < ApiController
    authorize_auth_token! :all

    def index
      account_plan_id = params[:account_plan_id]
      result = service.index(account_plan_id, query_params)

      render_json_array result.ap_client_meeting_schedule_items,
                        use: :format
    end

    def show
      account_plan_id = params[:account_plan_id]
      result = service.show(account_plan_id, params[:id])

      render_json result.ap_client_meeting_schedule_item,
                  use: :format
    end

    def create
      account_plan_id = params[:account_plan_id]
      input = ::V1::AccountPlanTableItems::ApClientMeetingScheduleItemCreationInput.new(request_body)
      validate! input, capture_failure: true

      result = service.create(account_plan_id, input.output)

      render_json result.ap_client_meeting_schedule_item,
                  use: :format,
                  status: :created
    end

    def update
      account_plan_id = params[:account_plan_id]
      input = ::V1::AccountPlanTableItems::ApClientMeetingScheduleItemUpdateInput.new(request_body)
      validate! input, capture_failure: true

      result = service.update(account_plan_id, params[:id], input.output)

      render_json result.ap_client_meeting_schedule_item,
                  use: :format,
                  status: :ok
    end

    def destroy
      account_plan_id = params[:account_plan_id]
      service.destroy(account_plan_id, params[:id])

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::AccountPlanTableItems::ApClientMeetingScheduleItemOutput
    end

    def service
      @service ||= ::AccountPlanTableItems::ApClientMeetingScheduleItemService.new(current_user_data)
    end
  end
end
