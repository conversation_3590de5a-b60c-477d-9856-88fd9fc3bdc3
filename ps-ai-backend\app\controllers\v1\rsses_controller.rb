# frozen_string_literal: true

module V1
  class RssesController < ApiController
    authorize_auth_token! :all, only: [:index]
    authorize_auth_token! :public, only: [:handle_webhook]

    def index
      result = service.index(query_params)

      render_json_array result.rsses,
                        use: :format,
                        rss_feeds: result.rss_feeds,
                        industries: result.industries
    end

    def handle_webhook
      result = service.handle_webhook(params[:access_key], request_body)

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::RssOutput
    end

    def service
      @service ||= ::RssService.new(current_user_data)
    end
  end
end
