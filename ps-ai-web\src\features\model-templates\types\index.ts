export enum ModelTemplateLLM {
  GPT_4 = "gpt-4o",
  GPT_4_MINI = "gpt-4o-mini",
}

export type ModelVariable = {
  id: number;
  model_template_id: number;
  name: string | null;
  description: string | null;
  references?: null;
  input_type: string;
  variable_reference: {
    filename: string;
    url: string;
  };
};

export type ModelCategory = {
  id: number;
  name: string;
  organization_id: number;
};

export enum ModelTemplateStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export enum ModelTemplateInput {
  INDUSTRY = "industry",
  LOCATION = "location",
  COMPANY = "company",
  CURRENCY = "currency",
  ACCOUNT_ADDRESSABLE_AREA = "account_addressable_area",
}

export type ModelTemplateBaseData = {
  name: string;
  temperature: number;
  max_tokens: number;
  model: ModelTemplateLLM;
  rules: string;
  reference_output: string;
  reference_output_url?: string;
  status?: ModelTemplateStatus;
  template_category_id?: number;
  inputs: ModelTemplateInput[] | null;
};

export type ModelTemplate = Omit<
  ModelTemplateBaseData,
  "reference_output_url" | "template_category_id"
> & {
  id: number;
  description: string;
  instruction: string;
  reference_output_file: {
    filename: string;
    url: string;
  };
  rating: number;
  variables: Array<ModelVariable>;
  status: ModelTemplateStatus;
  template_category: {
    id: number;
    name: string;
    organization_id: number;
  } | null;
  last_updated_by: {
    first_name: string | null;
    last_name: string | null;
  };
  updated_at: string;
  account_plan_group: {
    id: number;
    account_plan_unique_id: string | null;
  };
};

export enum ListTemplateCategory {
  CA_MACRO = "ca_macro",
  CA_BUSINESS = "ca_business",
  CA_INDUSTRY = "ca_industry",
  INSIGHT_AND_PERSPECTIVE = "insight_and_perspective",
  MISSING_INFORMATION = "missing_information",
  ACTION_PLAN = "action_plan",
}
