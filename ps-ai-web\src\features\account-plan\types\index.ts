/* Account Plan Base Table */
export enum AccountPlanStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export enum AccountPlanPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
}

type AccountPlanBaseData = {
  account_plan_group: {
    id: number;
    account_plan_unique_id: string | null;
    currency: string | null;
  } | null;
  name: string;
  version: string | null;
  status: AccountPlanStatus | null;
  priority: AccountPlanPriority | null;
  plan_date: string | Date | null;
  review_date: string | Date | null;
  next_review_date: number | null;
  owner_user_id: number | null;
  account_addressable_area: string;
  model_template_id: number;
  generate_analysis: boolean;
  industry: string | null;
  function: string | null;
  company: string | null;
  location: string | null;
  currency: string | null;
};

export type AccountPlanPayload = Partial<
  AccountPlanBaseData & {
    account_plan_group_id: number;
  }
>;

export type AccountPlanData = Omit<
  AccountPlanBaseData,
  "owner_user_id" | "model_template_id" | "generate_analysis"
> & {
  id: number;
  updated_at: string;
  model_template: {
    id: null | number;
    name: null | string;
  };
  owner_user: {
    first_name: string;
    last_name: string | null;
  } | null;
  generated_analysis: boolean;
  last_generated_analysis_at: string;
  last_updated_by: {
    first_name: string | null;
    last_name: string | null;
  };
};

export enum AccountPlanTableType {
  STAKEHOLDER_MAPPING = "stakeholder_mapping",
  WALLET_SHARE = "wallet_share",
  CIRCUMSTANTIAL_ANALYSIS = "circumstantial_analysis",
  SVOT = "svot",
  INSIGHT_AND_PERSPECTIVE = "insight_and_perspective",
  HISTORIC_REVENUE = "historic_revenue",
  CURRENT_REVENUE = "current_revenue",
  CURRENT_OPPORTUNITY = "current_opportunity",
  POTENTIAL_OPPORTUNITY = "potential_opportunity",
  REVENUE_FORECAST = "revenue_forecast",
  MISSING_INFORMATION = "missing_information",
  TARGETED_PERCEPTION_DEVELOPMENT = "targeted_perception_development",
  ACTION_PLAN = "action_plan",
  TOP_ACTION = "top_action",
  CLIENT_MEETING_SCHEDULE = "client_meeting_schedule",
}

/* Account Plan Groups Type */
export enum AccountPlanGroupStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export type AccountPlanGroupsBaseData = {
  account_plan_unique_id: string;
  account_addressable_area: string | null;
  company: string | null;
  location: string | null;
  currency: string | null;
  industry: {
    id: number;
    name: string;
    description: string;
  } | null;
};

export type AccountPlanGroupsPayload = Partial<
  Omit<AccountPlanGroupsBaseData, "industry"> & { industry_id?: number | null }
>;

export type AccountPlanGroupsData = {
  id: number;
  currency: string;
  latest_version_number: number;
  organization_id: number;
  account_plans: Array<AccountPlanData>;
  status: AccountPlanGroupStatus;
} & AccountPlanGroupsBaseData;

/* Industry Type */
export type IndustryBaseData = {
  name: string;
  description: string;
};

export type IndustryPayload = Partial<IndustryBaseData>;

export type IndustryData = IndustryBaseData & { id: number };
