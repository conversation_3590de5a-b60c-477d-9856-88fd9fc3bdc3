# frozen_string_literal: true

class LlmConsiderationService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
    @user_data = user_data
  end

  def index(query_params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    
    considerations = ::LlmConsiderations.new

    filter = query_params.slice(:usage_on, :page, :per_page)
    filter = filter.merge(
      organization_id: organization_user.organization_id
    )

    OpenStruct.new(
      llm_considerations: considerations.filter(filter)
    )
  end

  def generate_ap_charts_considerations(params)
    organization_id = params[:organization_id]
    account_plan_id = params[:account_plan_id]

    ActiveRecord::Base.transaction do
      used_model = Model.find_or_create_by(openai_assistant_id: ENV['BACKEND_GLOBAL_OPENAI_ASSISTANT_ID'])
      account_plan = AccountPlan.find(account_plan_id)
  
      json_format = {
        data: [
          {
            priority: Integer,
            best_actions: String,
          }
        ]
      }.to_json
  
      query = account_plan_consideration_llm_query(account_plan)
      message = "'message' = with given data, " \
                "generate maximum of 5 business best actions 'priority'. with smaller 'priority' means more important. " \
                "Generate response using json format that can be directly parsed: #{json_format}"
      final_query = query + message
  
      ai_params = {
        query: final_query,
        account_plan_id: account_plan.id,
        model_id: used_model.id,
        chat_type: 'llm_consideration_generated_items'
      }

      openai_service = OpenaiService.new(@user_data)
      ai_result = openai_service.generate_considerations(ai_params)
      
      parsed_message = ai_result.json_content['data'].map do |c|
        "#{c['priority']}. #{c['best_actions']}"
      end.join("\n")

      res_data = {
        chat_id: ai_result.assistant_message.chat_id,
        message_id: ai_result.assistant_message.id,
        message: parsed_message,
        account_plan_group_id: account_plan.account_plan_group_id,
        account_plan_name: account_plan.name,
      }

      consideration = LlmConsideration.find_or_create_by(organization_id: organization_id, usage_on: 'ap_charts')
      result = consideration.result

      if result.blank?
        consideration.result = [res_data]
        consideration.save!
      else
        curr_res_data = result.find { |d| d['account_plan_group_id'] == account_plan.account_plan_group_id }
        result.delete(curr_res_data)

        result << res_data

        consideration.result = result
        consideration.save!
      end
    end
  end

  private

  def latest_rss_feed_query(topic)
    rss_service = RssService.new(@user_data)
    result = rss_service.index({topic: topic})

    rss_feeds = result.rss_feeds
    query = rss_feeds.map do |rf|
      "(#{rf.title}, #{rf.description}, #{rf.url}, #{rf.date_published})"
    end.join(',')
  end
end
