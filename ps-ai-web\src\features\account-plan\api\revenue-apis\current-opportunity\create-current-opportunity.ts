import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APCurrentOpportunity,
  APCurrentOpportunityBaseData,
} from "@/features/account-plan/types/revenue-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createCurrentOpportunity = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APCurrentOpportunityBaseData;
}): ApiResponse<APCurrentOpportunity> => {
  return api.post(
    API_ROUTES.ACCOUNT_PLANS_CURRENT_OPPORTUNITY(accountId),
    data
  );
};

type UseCreateCurrentOpportunityOptions = {
  mutationConfig?: MutationConfig<typeof createCurrentOpportunity>;
};

export const useCreateCurrentOpportunity = ({
  mutationConfig,
}: UseCreateCurrentOpportunityOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_CURRENT_OPPORTUNITY,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createCurrentOpportunity,
  });
};
