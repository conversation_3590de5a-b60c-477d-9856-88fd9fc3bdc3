# frozen_string_literal: true

module V1
  class OrganizationUpdateInput < ::ApplicationInput
    optional(:name)
    optional(:unique_id)
    optional(:tier)
    optional(:tagline)
    optional(:message)
    optional(:image_url)
    optional(:logo_url)
    optional(:subdomain)
    optional(:primary_color)
    optional(:secondary_color)
    optional(:ap_wallet_share_color)
    optional(:ap_current_revenue_color)
    optional(:ap_current_opportunity_color)
    optional(:ap_potential_opportunity_color)
    optional(:primary_extra_light)
    optional(:primary_light)
  end
end
