import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APTopAction } from "@/features/account-plan/types/strategy-types";

type TopActionListParams = BaseParams;

export const getTopActionList = ({
  accountId,
  params,
}: {
  accountId: number;
  params?: TopActionListParams;
}): ApiResponse<APTopAction[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_TOP_ACTION(accountId), {
    params,
  });
};

export const getTopActionListQueryOptions = (
  accountId: number,
  params?: TopActionListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_TOP_ACTION,
    ],
    queryFn: () => getTopActionList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseTopActionListOptions = {
  params?: TopActionListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getTopActionList>;
  options?: Partial<ReturnType<typeof getTopActionListQueryOptions>>;
};

export const useTopActionList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UseTopActionListOptions) => {
  const topActionListQuery = useQuery({
    ...getTopActionListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...topActionListQuery,
    topActionList: topActionListQuery.data?.data,
  };
};
