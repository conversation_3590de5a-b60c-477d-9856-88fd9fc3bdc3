# frozen_string_literal: true

class Organization < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  enum tier: string_enum('public', 'free', 'premium', 'superuser'), _suffix: true

  has_many :roles

  has_many :organization_crms, dependent: :destroy

  after_create :create_role

  def create_role
    ['super_admin', 'member'].each do |r|
      Role.find_or_create_by!(organization_id: self.id, name: r)
    end
  end

  def self.role_hierarchy
    {
      'owner' => 0,
      'super_admin' => 1,
      # 'admin' => 2
      'member' => 3
    }
  end

  def self.min_role_subs
    -1
  end

  def self.max_role_subs
    99
  end

  def self.color_fields
    [
      :primary_color,
      :secondary_color,
      :ap_wallet_share_color,
      :ap_current_revenue_color,
      :ap_current_opportunity_color,
      :ap_potential_opportunity_color,
      :primary_extra_light,
      :primary_light
    ]
  end
end
