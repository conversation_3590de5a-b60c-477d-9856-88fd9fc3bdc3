import { useWindowSize } from "@uidotdev/usehooks";

const BASE_RESOLUTION_WIDTH = 1920;
const BASE_RESOLUTION_HEIGHT = 1080;

export const useScalingDimension = () => {
  const { width: windowWidth, height: windowHeight } = useWindowSize();

  const getWidth = (width: number) => {
    return windowWidth ? width * (windowWidth / BASE_RESOLUTION_WIDTH) : width;
  };

  const getHeight = (height: number) => {
    return windowHeight
      ? height * (windowHeight / BASE_RESOLUTION_HEIGHT)
      : height;
  };

  return {
    windowWidth: windowWidth ?? 0,
    windowHeight: windowHeight ?? 0,
    getWidth,
    getHeight,
  };
};
