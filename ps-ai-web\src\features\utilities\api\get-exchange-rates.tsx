import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { ExchangeRatesData } from "../types";

type ExchangeRateParams = {
  currency: string;
  exhange_currency?: string[];
};

export const getExchangeRate = ({
  params,
}: {
  params: ExchangeRateParams;
}): ApiResponse<ExchangeRatesData> => {
  return api.get(API_ROUTES.UTILITIES_CURRENCY_EXCHANGE_RATES, { params });
};

export const getExchangeRateQueryOptions = (params: ExchangeRateParams) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.ACCOUNT_PLANS, params],
    queryFn: () => getExchangeRate({ params }),
  });
};

type UseExchangeRateOptions = {
  params: ExchangeRateParams;
  queryConfig?: QueryConfig<typeof getExchangeRate>;
  options?: Partial<ReturnType<typeof getExchangeRateQueryOptions>>;
};

export const useExchangeRateList = ({
  params,
  queryConfig,
  options,
}: UseExchangeRateOptions) => {
  const exchangeRateQuery = useQuery({
    ...getExchangeRateQueryOptions(params),
    ...queryConfig,
    ...options,
  });

  return {
    ...exchangeRateQuery,
    exchangeRate: exchangeRateQuery.data?.data,
  };
};
