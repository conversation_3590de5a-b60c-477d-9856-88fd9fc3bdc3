# frozen_string_literal: true

class Chat < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  enum chat_type: string_enum('general', 'ap_table_generated_items', 'llm_consideration_generated_items')

  belongs_to :organization
  belongs_to :model
  belongs_to :account_plan, optional: true
  belongs_to :ap_table, optional: true
  belongs_to :creator_organization_user,  
             class_name: 'OrganizationUser',
             foreign_key: 'creator_organization_user_id'
end
