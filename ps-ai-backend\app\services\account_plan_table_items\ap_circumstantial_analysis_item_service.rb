# frozen_string_literal: true

class AccountPlanTableItems::ApCircumstantialAnalysisItemService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
    @user_data = user_data
  end

  def create(account_plan_id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    if params[:item_type].blank?
      params[:item_type] = 'macro'
    end

    ap_item = ::AccountPlanTableItems::ApCircumstantialAnalysisItem.new

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: 'position',
        table_type: 'circumstantial_analysis',
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item = ::AccountPlanTableItems::ApCircumstantialAnalysisItem
                  .find_by(ap_table_id: ap_table.id, item_type: params[:item_type])
      
      if ap_item.blank?
        ap_item = ::AccountPlanTableItems::ApCircumstantialAnalysisItem.create!(params)
      else
        ap_item.update!(params)
      end
    end

    OpenStruct.new(
      ap_circumstantial_analysis_item: ap_item
    )
  end

  def update(account_plan_id, id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApCircumstantialAnalysisItem.find(id)

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: 'position',
        table_type: 'circumstantial_analysis',
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item.update!(params)
    end

    OpenStruct.new(
      ap_circumstantial_analysis_item: ap_item
    )
  end

  def index(account_plan_id, query_params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_table = ApTable.find_by(
      table_category: 'position',
      table_type: 'circumstantial_analysis',
      account_plan_id: account_plan_id
    )

    ap_item_reposity = ::AccountPlanTableItems::ApCircumstantialAnalysisItems.new

    filter = query_params.slice(
      :page, :per_page, :disable_pagination
    )
    filter = filter.merge(
      ap_table_id: ap_table&.id || -1
    )

    filtered_items = ap_item_reposity.filter(filter)

    OpenStruct.new(
      ap_circumstantial_analysis_items: filtered_items
    )
  end

  def show(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApCircumstantialAnalysisItem.find(id)
    
    OpenStruct.new(
      ap_circumstantial_analysis_item: ap_item
    ) 
  end

  def destroy(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApCircumstantialAnalysisItem.find(id)

    ActiveRecord::Base.transaction do
      ap_item.discard!
    end
  end

  def generate(account_plan_id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_table = ApTable.find_by(
      table_category: 'position',
      table_type: 'circumstantial_analysis',
      account_plan_id: account_plan_id
    )

    exist! ap_table.present?, on_error: 'Table is not created'

    ActiveRecord::Base.transaction do
      category_hash = TemplateCategory.ap_table_map_to_categories[ap_table.table_type]
      used_hash = {}

      category_hash.each do |k, v|
        template_category_ids = TemplateCategory.where(name: v).ids
        authorize! template_category_ids.present?, on_error: 'Template invalid, please contact administrator!'

        if @organization.tier == 'premium'
          active_template = ModelTemplate
          .where(
            organization_id: @organization.id,
            status: 'active',
            template_category_id: template_category_ids
          )
          .last
        end

        active_template ||= ModelTemplate
          .where(
            organization_id: nil,
            status: 'active',
            template_category_id: template_category_ids
          )
          .last
        authorize! active_template.present?, on_error: 'Template invalid, please contact administrator!'

        # get model
        used_model = Model.find_by(model_template_id: active_template.id, model: active_template.model)
        authorize! used_model.present?, on_error: 'Template invalid, please contact administrator!'

        used_hash[k] = {
          model_template: active_template,
          model: used_model
        }
      end

      # Delete existing row
      ::AccountPlanTableItems::ApCircumstantialAnalysisItem.where(ap_table_id: ap_table.id).discard_all

      # TODO usage data from model_template.inputs
      used_hash.each do |k, v|
        active_template = v[:model_template]
        used_model = v[:model]

        query = account_plan_consideration_llm_query(account_plan)

        # need _analysis because GPT is bad
        json_format = {
          data: {
            "#{k}_analysis".to_sym => String
          }
        }.to_json
        

        message = "'message' = with given data, " \
                  "generate business circumstantial analysis with each '_analysis' from json format following closely to the format and style of Reference Output from instruction. " \
                  "replace <@I-industry>, <@I-location>, <@I-company>, <@I-currency>, <@I-account_addressable_area> in the response with its value. " \
                  "Generate response using strict json format #{json_format} without any additional key that can be directly parsed"
        final_query = query + message

        ai_params = {
          query: final_query,
          account_plan_id: account_plan.id,
          ap_table_id: ap_table.id,
          model_id: used_model.id,
          chat_type: 'ap_table_generated_items'
        }

        openai_service = OpenaiService.new(@user_data)
        openai_service.generate_ap_table_response(ai_params)
      end
    end
  end
end
