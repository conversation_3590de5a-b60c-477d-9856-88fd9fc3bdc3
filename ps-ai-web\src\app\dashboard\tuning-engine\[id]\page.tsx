"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { IconChevronDown } from "@tabler/icons-react";
import React, { useEffect, useMemo, useState } from "react";
import { Control, useFieldArray, useForm, useWatch } from "react-hook-form";
import { z } from "zod";

import { Button } from "@/components/ui/button";

import { Slider } from "@/components/ui/slider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import MultipleSelector from "@/components/ui/multiple-selector";

import { Textarea } from "@/components/ui/textarea";
import { LLMList } from "@/features/model-templates/constants";
import {
  ModelTemplateBaseData,
  ModelTemplateInput,
  ModelTemplateLLM,
  ModelVariable,
} from "@/features/model-templates/types";
import { cn } from "@/lib/utils";
import { useParams, useRouter } from "next/navigation";
import { useModelDetail } from "@/features/model-templates/api/get-model";
import { useUpdateModelDetail } from "@/features/model-templates/api/update-model";
import { useCreateModel } from "@/features/model-templates/api/create-model";
import { UploadAttachment } from "@/features/upload/components/upload-attachment";
import { useUpdateModelVariable } from "@/features/model-templates/api/update-model-variable";
import { useCreateModelVariable } from "@/features/model-templates/api/create-model-variable";
import _, { snakeCase } from "lodash";
import { PATH } from "@/constants/path";
import Link from "next/link";
import { toast } from "sonner";
import { mentionRegex, Tiptap } from "@/components/ui/tiptap";
import { FieldTooltip } from "@/components/ui/tooltip/field-tooltip";
import { useIsHydrated } from "@/lib/hooks/use-hydrated";
import { useModelCategoryList } from "@/features/model-templates/api/get-model-category-list";
import { isRequestError } from "@/lib/api-client";

const MAX_TOKEN = 16000;
const DEFAULT_VARIABLES_COUNT = 3;

const inputOptions = [
  { label: "Industry", value: ModelTemplateInput.INDUSTRY },
  { label: "Location", value: ModelTemplateInput.LOCATION },
  { label: "Company", value: ModelTemplateInput.COMPANY },
  { label: "Currency", value: ModelTemplateInput.CURRENCY },
  {
    label: "Account Addressable Area",
    value: ModelTemplateInput.ACCOUNT_ADDRESSABLE_AREA,
  },
];

const formSchema = z.object({
  name: z
    .string()
    .min(1, { message: "Please fill out the template name." })
    .max(64, { message: "Maximum 64 Characters" }),
  category: z.string(),
  model: z.nativeEnum(ModelTemplateLLM),
  temperature: z.number().min(0).max(2),
  max_tokens: z.number().min(0).max(MAX_TOKEN),
  rules: z.string({ message: "Please fill out the rules" }),
  reference_output: z.string({
    message: "Please fill out the reference output",
  }),
  inputs: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
      })
    )
    .min(1, "Please select at least one input"),
  reference_output_file: z
    .object({
      file: z
        .instanceof(File)
        .refine((file) => file.size < ********, {
          message: "File must be less than 10MB.",
        })
        .optional(),
      filename: z.string().optional(),
      url: z.string().optional(),
    })
    .nullish()
    .optional(),
  variables: z.array(
    z.object({
      id: z.number().optional().nullish(),
      name: z.string().optional(),
      description: z.string().optional(),
      variable_reference: z
        .object({
          filename: z.string().optional(),
          url: z.string().optional(),
        })
        .nullish()
        .optional(),
    })
  ),
});

type TuningEngineForm = z.infer<typeof formSchema>;

function VariableField({
  control,
  idx,
}: {
  idx: number;
  control: Control<TuningEngineForm>;
}) {
  const fieldValues = useWatch({
    control,
    name: "variables",
  });

  const name = fieldValues?.[idx]?.name;

  return (
    <div className="mb-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild className="rounded-xl">
          <Button
            variant="outline"
            className={cn(
              "w-full justify-between font-normal",
              !name && "text-neutral-200"
            )}
          >
            {name || "Input Name"}
            <IconChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="start"
          className="absolute left-0 w-[var(--radix-dropdown-menu-trigger-width)] bg-white"
        >
          <div className="grid gap-4 p-4">
            <FormField
              control={control}
              name={`variables.${idx}.name`}
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      {...field}
                      className="rounded-xl"
                      placeholder="Name"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`variables.${idx}.description`}
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      className="rounded-xl"
                      {...field}
                      placeholder="Description"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`variables.${idx}.variable_reference`}
              render={({ field }) => (
                <FormItem className="mt-1">
                  <FormControl>
                    <UploadAttachment {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

const defaultVariable = {
  id: null,
  name: "",
  description: "",
};

const defaultVariables = Array.from(
  { length: DEFAULT_VARIABLES_COUNT },
  () => defaultVariable
);

function ModifyTuningEngine() {
  const router = useRouter();
  const { isHydrated } = useIsHydrated();

  const { id } = useParams<{ id: string }>();
  const isCreateModel = id === "create";
  const modelId = isCreateModel ? 0 : parseInt(id);

  const [isLoadingSubmit, setIsLoadingSubmit] = useState(false);

  const { modelDetail, isSuccess } = useModelDetail({
    modelId,
    options: {
      enabled: !!modelId,
    },
  });
  const { modelCategoryList } = useModelCategoryList({});

  const updateModelDetail = useUpdateModelDetail({
    mutationConfig: {
      invalidate: true,
    },
  });
  const createModel = useCreateModel({});
  const updateVariable = useUpdateModelVariable({});
  const createVariable = useCreateModelVariable({});

  const form = useForm<TuningEngineForm>({
    resolver: zodResolver(formSchema),
    reValidateMode: "onSubmit",
    defaultValues: {
      name: "",
      temperature: 1,
      max_tokens: 16000,
      model: ModelTemplateLLM.GPT_4,
      variables: defaultVariables,
      inputs: [],
    },
  });

  const { update: updateVariables } = useFieldArray({
    control: form.control,
    name: "variables",
  });

  useEffect(() => {
    if (!isSuccess || !modelDetail) return;

    const fields = Object.keys(formSchema.shape) as (keyof TuningEngineForm)[];

    for (const field of fields) {
      if (field === "variables") {
        modelDetail["variables"]?.map((v, idx) => {
          const filteredValue = _.omitBy(
            v,
            (v) => v === null || v === undefined
          );

          updateVariables(idx, filteredValue);
        });
      } else if (field === "inputs") {
        const modelInputs = modelDetail["inputs"]
          ?.map((input) =>
            inputOptions.find((options) => options.value === input)
          )
          .filter((v) => !!v);

        form.setValue("inputs", modelInputs ?? []);
      } else if (field === "category") {
        form.setValue(
          "category",
          modelDetail["template_category"]?.name ?? modelCategoryList[0].name
        );
      } else {
        form.setValue(field, modelDetail[field]);
      }
    }
  }, [isSuccess, modelDetail, updateVariables, modelCategoryList, form]);

  const inputSuggestions = useMemo(() => {
    return (
      inputOptions?.map((v, idx) => ({
        id: -1 - idx,
        label: v.label,
        idx,
      })) || []
    );
  }, []);

  const mentionSuggestion = useMemo(() => {
    const variableSuggestions =
      form.watch("variables")?.map((v, idx) => ({
        id: v.id ?? null,
        label: v.name ?? "",
        idx: inputSuggestions.length + idx + 1,
      })) || [];

    return [...inputSuggestions, ...variableSuggestions];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(form.watch("variables")), inputSuggestions]);

  const onSubmit = async (values: TuningEngineForm) => {
    const {
      variables,
      reference_output_file,
      inputs,
      category,
      ...baseValues
    } = values;
    let _modelId = modelId;

    setIsLoadingSubmit(true);

    try {
      const uploadData: ModelTemplateBaseData = {
        ...baseValues,
        template_category_id: modelCategoryList.find(
          (categoryList) => categoryList.name === category
        )?.id,
        reference_output_url: reference_output_file?.url,
        inputs: inputs.map((v) => v.value as ModelTemplateInput),
      };

      if (isCreateModel) {
        const { data } = await createModel.mutateAsync({
          data: uploadData,
        });

        _modelId = data.id;
      }

      const promises = (variables || []).map(
        async ({ id, variable_reference, ...rest }) => {
          if (!!id) {
            return updateVariable.mutateAsync({
              data: {
                ...rest,
                id,
                variable_reference_url: variable_reference?.url,
              },
            });
          } else if (rest.name || rest.description) {
            return createVariable.mutateAsync({
              data: {
                ...rest,
                model_template_id: _modelId,
                variable_reference_url: variable_reference?.url,
              },
            });
          } else {
            return {};
          }
        }
      );
      const variableData = (await Promise.all(promises)) as {
        data?: ModelVariable;
      }[];

      const breakRegex = /\\\n/g;
      const replaceMentionAndBreak = (markdown: string) => {
        return markdown
          .replace(mentionRegex, (__, dataId, dataIdx) => {
            const id = dataId ?? variableData[dataIdx]?.data?.id;
            const matchedInput = inputSuggestions.find(
              (v) => v.id === parseInt(dataId)
            );

            return matchedInput
              ? `<@I-${snakeCase(matchedInput.label)}>`
              : `<@V${id}>`;
          })
          .replace(breakRegex, "  \n");
      };

      await updateModelDetail.mutateAsync({
        modelId: _modelId,
        data: {
          ...uploadData,
          rules: replaceMentionAndBreak(uploadData.rules),
        },
      });

      toast(
        `The model has been successfully ${isCreateModel ? "created" : "updated"}.`
      );

      if (isCreateModel) {
        router.push(PATH.DASHBOARD_TUNING_ENGINE_EDIT(_modelId));
      }

      await Promise.all(promises);
    } catch (e) {
      if (isRequestError(e)) {
        const errorMessage = e.response?.data.errors[0].message ?? "";
        toast.error(errorMessage);
      } else {
        toast.error("An error occured while saving the template");
      }
    } finally {
      setIsLoadingSubmit(false);
    }
  };

  if (!isHydrated) return null;

  return (
    <Form {...form}>
      <form className="w-full" onSubmit={form.handleSubmit(onSubmit)}>
        <div className="mb-res-y-lg flex items-center justify-between">
          <h1 className="text-2xl font-bold text-primary-500">
            {isCreateModel
              ? "Create"
              : (modelDetail?.template_category?.name ?? "-")}{" "}
            Template
          </h1>
          <div className="flex">
            <Link href={PATH.DASHBOARD_TUNING_ENGINE}>
              <Button variant="ghost" className="mr-2">
                Back to List
              </Button>
            </Link>
            <Button className="bg-gradient" isLoading={isLoadingSubmit}>
              Save Template
            </Button>
          </div>
        </div>

        <div className="grid gap-res-y-lg">
          <div className="mb-2 grid w-full grid-cols-5 justify-between gap-x-res-x-lg gap-y-res-y-lg">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem className="grow basis-1">
                  <FormControl>
                    <Input
                      className="h-12 rounded-xl"
                      placeholder="Input your template name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="model"
              render={({ field }) => (
                <FormItem className="basis-1/5">
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger className="h-12">
                        <SelectValue placeholder="LLM Model Selection" />
                      </SelectTrigger>
                      <SelectContent className="max-h-72">
                        {LLMList.map((v, idx) => (
                          <SelectItem key={idx} value={v.value}>
                            {v.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem className="basis-1/5">
                  <FormControl>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={!isCreateModel}
                    >
                      <SelectTrigger className="h-12">
                        <SelectValue placeholder="Category" />
                      </SelectTrigger>
                      <SelectContent className="max-h-72">
                        {modelCategoryList
                          .filter((v) => !!v.name)
                          .map((v, idx) => (
                            <SelectItem key={idx} value={v.name}>
                              {v.name}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="temperature"
              render={({ field: { value, onChange } }) => (
                <FormItem className="flex basis-1/5 flex-col">
                  <FormLabel className="flex items-center font-bold text-primary-500">
                    Temperature
                    <FieldTooltip>
                      Adjust the temperature to control the randomness of the
                      model's output. Lower values make output more
                      deterministic, while higher values increase
                      variabilitAdjust the temperature to control the randomness
                      of the model's output. Lower values make output more
                      deterministic, while higher values increase variability
                    </FieldTooltip>
                    <p className="ml-auto font-medium">{value}</p>
                  </FormLabel>
                  <FormControl>
                    <Slider
                      className="!mb-2 !mt-auto"
                      color="orange"
                      min={0}
                      max={2}
                      step={0.1}
                      value={[value]}
                      onValueChange={(vals) => {
                        onChange(vals[0]);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="max_tokens"
              render={({ field: { value, onChange } }) => (
                <FormItem className="flex basis-1/5 flex-col">
                  <FormLabel className="flex items-center font-bold text-primary-500">
                    Maximum Tokens
                    <FieldTooltip>
                      Set the maximum number of tokens (words or characters)
                      that the model can generate in response to a prompt.
                    </FieldTooltip>
                    <p className="ml-auto font-medium">{value}</p>
                  </FormLabel>
                  <FormControl>
                    <Slider
                      className="!mb-2 !mt-auto"
                      min={0}
                      max={MAX_TOKEN}
                      step={1}
                      value={[value]}
                      onValueChange={(vals) => {
                        onChange(vals[0]);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="inputs"
            render={({ field }) => (
              <FormItem className="mb-2 w-[25vw]">
                <FormControl>
                  <MultipleSelector
                    hidePlaceholderWhenSelected
                    className="overflow-hidden rounded-xl"
                    value={field.value}
                    onChange={field.onChange}
                    defaultOptions={inputOptions}
                    placeholder="Select tuning engine input"
                    emptyIndicator={
                      <p className="text-center leading-10 text-gray-600 dark:text-gray-400">
                        No results found.
                      </p>
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="variables"
            render={({ field: { value } }) => (
              <div className="grid gap-res-y-lg">
                {value.map((v, idx) => (
                  <VariableField key={idx} idx={idx} control={form.control} />
                ))}
              </div>
            )}
          />

          <FormField
            control={form.control}
            name="rules"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Tiptap
                    {...field}
                    placeholder="Input a set of rules to ensure the AI-generated responses adhere to certain guidelines
                      and standards."
                    onChange={(val) => field.onChange(val)}
                    mentionSuggestion={mentionSuggestion}
                    className="rounded-xl"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div>
            <FormField
              control={form.control}
              name="reference_output"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Tiptap
                      {...field}
                      placeholder="Input your reference output"
                      onChange={(val) => field.onChange(val)}
                      mentionSuggestion={mentionSuggestion}
                      className="rounded-xl"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="reference_output_file"
              render={({ field }) => (
                <FormItem className="mt-2">
                  <FormControl>
                    <UploadAttachment {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      </form>
    </Form>
  );
}

export default ModifyTuningEngine;
