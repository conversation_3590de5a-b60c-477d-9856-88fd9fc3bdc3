import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APMissingInformation } from "@/features/account-plan/types/strategy-types";

export const getMissingInformationDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APMissingInformation> => {
  return api.get(
    API_ROUTES.ACCOUNT_PLANS_MISSING_INFORMATION_DETAIL(accountId, id)
  );
};

export const getMissingInformationDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_MISSING_INFORMATION,
      id,
    ],
    queryFn: () => getMissingInformationDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseMissingInformationDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getMissingInformationDetail>;
  options?: Partial<ReturnType<typeof getMissingInformationDetailQueryOptions>>;
};

export const useMissingInformationDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseMissingInformationDetailOptions) => {
  const missingInformationDetailQuery = useQuery({
    ...getMissingInformationDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...missingInformationDetailQuery,
    missingInformationDetail: missingInformationDetailQuery.data?.data,
  };
};
