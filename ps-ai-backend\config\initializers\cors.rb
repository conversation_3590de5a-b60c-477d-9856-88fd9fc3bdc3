# frozen_string_literal: true

regex_origin = ENV['ALLOWED_CORS_ORIGIN'].to_s.split(' ').presence || '*'
allowed_cors_origin = if regex_origin != '*'
                        regex_origin.map { |origin| Regexp.new origin }
                      else
                        regex_origin
                      end

Rails.application.config.middleware.insert_before 0, Rack::Cors do
  allow do
    origins(*allowed_cors_origin)
    resource '*',
             headers: :any,
             methods: %i[get post put patch delete options head],
             credentials: true
  end
end
