# frozen_string_literal: true

module V1
  class AuthOutput < ApiOutput
    def format
      {
        auth_token: @object,
        authenticated: current_user.persisted? && organization_user.persisted?,
        user: user_output
      }
    end

    def user_output
      return unless current_user.persisted? && organization_user.persisted?

      V1::UserOutput.new(
        current_user,
        show_organization: true,
        organization_user: organization_user,
        organization: organization
      ).format
    end

    private

    def current_user
      @options[:current_user]
    end

    def organization_user
      @options[:organization_user]
    end

    def organization
      @options[:organization]
    end
  end
end
