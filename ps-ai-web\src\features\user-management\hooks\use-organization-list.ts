import { useMemo } from "react";

import { useAuth } from "@/features/auth/api/get-auth";
import { useOrganizationList } from "@/features/organizations/api/get-organization-list";
import { OrganizationData } from "@/features/organizations/types";

export const useOrganizationOptions = () => {
  const { auth } = useAuth({});
  const { organizationList } = useOrganizationList({
    params: { disable_pagination: true },
  });

  const organizationOptions = useMemo(() => {
    const otherOrganizations = organizationList
      .filter((v) => v.id !== auth?.user.organization.id && v.id > 0)
      .map((v) => ({
        ...v,
        label: v.name,
        value: v.id.toString() ?? "",
      }));

    return [
      {
        ...auth?.user.organization,
        label: `${auth?.user.organization.name} (Default)`,
        value: auth?.user.organization.id.toString() ?? "",
      } as OrganizationData & { label: string; value: string },
      ...otherOrganizations,
    ];
  }, [auth, organizationList]);

  return { organizationOptions };
};
