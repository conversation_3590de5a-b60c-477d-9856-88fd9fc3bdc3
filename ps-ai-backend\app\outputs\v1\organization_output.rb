# frozen_string_literal: true

module V1
  class OrganizationOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        tagline: @object.tagline,
        message: @object.message,
        image_url: @object.image_url,
        logo_url: @object.logo_url,
        subdomain: @object.subdomain,
        subdirectory: @object.subdirectory,
        tier: @object.tier,
        unique_id: @object.unique_id,
        primary_color: @object.primary_color,
        secondary_color: @object.secondary_color,
        ap_wallet_share_color: @object.ap_wallet_share_color,
        ap_current_revenue_color: @object.ap_current_revenue_color,
        ap_current_opportunity_color: @object.ap_current_opportunity_color,
        ap_potential_opportunity_color: @object.ap_potential_opportunity_color,
        primary_extra_light: @object.primary_extra_light,
        primary_light: @object.primary_light,
        organization_crms: crm_outputs
      }
    end

    def crm_outputs
      (@object.organization_crms || []).map do |crm|
        output = {
          id: crm.id,
          crm_type: crm.crm_type,
          access_token: crm.access_token,
          refresh_token: crm.refresh_token
        }
        output[:instance_url] = crm.instance_url if crm.instance_url.present?
        output
      end
    end
  end
end