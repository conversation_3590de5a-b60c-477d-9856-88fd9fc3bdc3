globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js":{"*":{"id":"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/sonner.tsx":{"*":{"id":"(ssr)/./src/components/ui/sonner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/features/landing-page/root-container.tsx":{"*":{"id":"(ssr)/./src/features/landing-page/root-container.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/react-query.tsx":{"*":{"id":"(ssr)/./src/lib/react-query.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/layout.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/account-plan/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/account-plan/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/account-plan/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/account-plan/[id]/page.tsx","name":"*","chunks":[],"async":true}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\node_modules\\@tanstack\\react-query-devtools\\build\\modern\\index.js":{"id":"(app-pages-browser)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\components\\ui\\sonner.tsx":{"id":"(app-pages-browser)/./src/components/ui/sonner.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\features\\landing-page\\root-container.tsx":{"id":"(app-pages-browser)/./src/features/landing-page/root-container.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\lib\\react-query.tsx":{"id":"(app-pages-browser)/./src/lib/react-query.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\app\\dashboard\\layout.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/layout.tsx","name":"*","chunks":["app/dashboard/layout","static/chunks/app/dashboard/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\app\\dashboard\\account-plan\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/account-plan/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\app\\dashboard\\account-plan\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/account-plan/[id]/page.tsx","name":"*","chunks":[],"async":true}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\":[],"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\app\\dashboard\\page":[],"C:\\Users\\<USER>\\Desktop\\ps-full-stack\\ps-ai-web\\src\\app\\dashboard\\layout":[]}}