import { type ColumnDef } from "@tanstack/react-table";
import { parseAsString, useQueryState } from "next-usequerystate";
import { useCallback, useEffect, useMemo, useState } from "react";
import _ from "lodash";
import { toast } from "sonner";

import DataTable from "@/components/ui/data-table";
import { APTopAction } from "@/features/account-plan/types/strategy-types";
import { useUpdateTopAction } from "@/features/account-plan/api/strategy-apis/top-action/update-top-action";
import { cn, formatDate } from "@/lib/utils";

import { useActiveTopActionList } from "../../api/strategy-apis/top-action/get-active-top-actions";
import { Checkbox } from "@/components/ui/checkbox";
import {
  SelectItem,
  Select,
  SelectContent,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useQueryClient } from "@tanstack/react-query";
import { QUERY_KEYS } from "@/constants/query-keys";
import { AccountPlanData } from "../../types";

type ActiveTopActionData = APTopAction & {
  account_plan_unique_id: string;
  company_name: string;
};

type SortOption = {
  label: string;
  value: string;
  sortFn: (a: ActiveTopActionData, b: ActiveTopActionData) => number;
};

const strategicActionsSortOptions: Array<SortOption> = [
  {
    label: "Due Date",
    value: "due_date",
    sortFn: (a, b) =>
      new Date(a.action_date ?? "").getTime() -
      new Date(b.action_date ?? "").getTime(),
  },
  {
    label: "Company Name",
    value: "company_name",
    sortFn: (a, b) => a.company_name.localeCompare(b?.company_name),
  },
  {
    label: "Owner",
    value: "potential_opportunities",
    sortFn: (a, b) => a.action_target?.localeCompare(b?.action_target),
  },
  {
    label: "Completion Status",
    value: "completed",
    sortFn: (a) => (!a.completed ? 0 : -1),
  },
] as const;

type ShowOption = {
  label: string;
  value: string;
  filterFn: (option: ActiveTopActionData) => boolean;
};

const strategicActionsShowOptions: Array<ShowOption> = [
  {
    label: "Incomplete",
    value: "incomplete",
    filterFn: (option) => !option.completed,
  },
  {
    label: "Completed",
    value: "completed",
    filterFn: (option) => option.completed,
  },
  {
    label: "All",
    value: "all",
    filterFn: (_) => true,
  },
] as const;

export const AllStrategicActionsTable = ({
  accountPlan,
}: {
  accountPlan?: AccountPlanData;
}) => {
  const [tableData, setTableData] = useState<ActiveTopActionData[]>([]);
  const [strategicActionsSort, setStrategicActionsSort] = useQueryState(
    "actions_sort",
    parseAsString.withDefault(strategicActionsSortOptions[0].value)
  );
  const [strategicActionsShow, setStrategicActionsShow] = useQueryState(
    "actions_show",
    parseAsString.withDefault(strategicActionsShowOptions[0].value)
  );

  const { activeTopActionList } = useActiveTopActionList({
    params: {
      disable_pagination: true,
    },
  });
  const queryClient = useQueryClient();
  const updateTopAction = useUpdateTopAction({});

  useEffect(() => {
    if (!activeTopActionList) return;

    let newActiveTopActionRows: ActiveTopActionData[] = [];

    activeTopActionList?.forEach((activeAccount) => {
      const mappedActions = activeAccount.top_actions
        .filter((v) => !!v.action_target)
        .map((action) => ({
          ...action,
          account_plan_unique_id: activeAccount.account_plan_unique_id,
          company_name: activeAccount.company_name,
        }));

      newActiveTopActionRows.push(...mappedActions);
    });

    newActiveTopActionRows = newActiveTopActionRows
      .sort(
        strategicActionsSortOptions.find(
          (v) => v.value === strategicActionsSort
        )?.sortFn ?? ((_) => 0)
      )
      ?.filter(
        strategicActionsShowOptions.find(
          (v) => v.value === strategicActionsShow
        )?.filterFn ?? (() => true)
      );

    setTableData(newActiveTopActionRows);
  }, [
    activeTopActionList,
    setTableData,
    strategicActionsSort,
    strategicActionsShow,
  ]);

  const onChangeData = useCallback(
    async (data: Partial<APTopAction>, accountId: number, id: number) => {
      try {
        setTableData((prev) =>
          prev.map((v) => (v.id === id ? { ...v, ...data } : v))
        );

        await updateTopAction.mutateAsync({
          accountId,
          id,
          data,
        });

        await queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.ACCOUNT_PLANS_ACTIVE_TOP_ACTION],
        });
      } catch (_) {
        toast("An unexpected error occured when modifying data");
      }
    },
    [updateTopAction, queryClient]
  );

  const columns: ColumnDef<ActiveTopActionData>[] = useMemo(
    () => [
      {
        accessorKey: "action_date",
        header: "Due Date",
        size: 100,
        cell: ({ row }) => {
          const rowData = row.original;

          return formatDate(rowData.action_date, "DD/MM/YYYY");
        },
        meta: {
          padding: true,
        },
      },
      {
        accessorKey: "company_name",
        header: "Company Name",
        size: 125,
        cell: ({ row }) => {
          const rowData = row.original;

          return rowData.company_name;
        },
        meta: {
          padding: true,
        },
      },
      {
        accessorKey: "action_target",
        header: "Owner",
        size: 125,
        cell: ({ row }) => {
          const rowData = row.original;

          return rowData.action_target;
        },
        meta: {
          padding: true,
        },
      },
      {
        accessorKey: "description",
        header: "What will you do?",
        size: 500,
        cell: ({ row }) => {
          const rowData = row.original;

          const isHowExist = !!rowData.how;
          const isDescriptionExist = !!rowData.description;

          return (
            <div className="mx-res-x-sm my-res-y-sm">
              <p
                className={cn(
                  "mb-res-y-sm pr-res-x-sm",
                  !isDescriptionExist && "italic text-neutral-500"
                )}
              >
                <b className="gap-res-x-sm">What will you do? </b>
                {rowData.description || "Not specified"}
              </p>

              <p
                className={cn(
                  "mb-res-y-sm gap-res-x-sm",
                  !isHowExist && "italic text-neutral-500"
                )}
              >
                <b>How will you do it? </b>
                {rowData.how || "Not specified"}
              </p>
            </div>
          );
        },
      },
      {
        accessorKey: "completed",
        header: "Completed",
        size: 100,
        cell: ({ row }) => {
          const rowData = row.original;

          return (
            <div className="flex">
              <Checkbox
                className="mx-auto"
                checked={rowData.completed}
                onCheckedChange={(completed) =>
                  onChangeData(
                    { completed: completed as boolean },
                    rowData.account_plan_id,
                    rowData.id
                  )
                }
              />
            </div>
          );
        },
        meta: {
          padding: true,
        },
      },
    ],
    [onChangeData]
  );

  return (
    <section className="mt-4 flex h-[35vh] flex-col">
      <div className="mb-res-y-base flex items-center justify-between">
        <h1 className="text-2xl font-bold text-primary-500">
          Strategic Actions
        </h1>
        <div className="flex gap-res-x-sm">
          <Select
            value={strategicActionsShow}
            onValueChange={setStrategicActionsShow}
          >
            <SelectTrigger
              className="h-[3.5vh] w-[11vw] px-res-x-xs text-left text-xs"
              iconClassName="!ml-[0]"
            >
              <p className="mr-res-x-xs font-bold">Status:</p>{" "}
              <SelectValue placeholder="Sort By" />
            </SelectTrigger>
            <SelectContent>
              {strategicActionsShowOptions.map((v, idx) => (
                <SelectItem key={idx} value={v.value} className="text-xs">
                  {v.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={strategicActionsSort}
            onValueChange={setStrategicActionsSort}
          >
            <SelectTrigger
              className="h-[3.5vh] w-[11vw] px-res-x-xs text-left text-xs"
              iconClassName="!ml-[0]"
            >
              <p className="mr-res-x-xs font-bold">Sort By:</p>{" "}
              <SelectValue placeholder="Sort By" />
            </SelectTrigger>
            <SelectContent>
              {strategicActionsSortOptions.map((v, idx) => (
                <SelectItem key={idx} value={v.value} className="text-xs">
                  {v.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="h-[1.5vh] w-full shrink-0">
        <DataTable
          height="30vh"
          variant="alt1"
          size="m"
          columns={columns}
          data={tableData}
          currency={accountPlan?.account_plan_group?.currency}
        />
      </div>
    </section>
  );
};
