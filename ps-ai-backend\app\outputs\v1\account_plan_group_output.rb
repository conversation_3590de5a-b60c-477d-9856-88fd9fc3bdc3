# frozen_string_literal: true

module V1
  class AccountPlanGroupOutput < ApiOutput
    def format
      {
        id: @object.id,
        account_plan_unique_id: @object.account_plan_unique_id,
        latest_version_number: @object.latest_version_number,
        organization_id: @object.organization_id,
        status: @object.status,
        currency: @object.currency,
        account_addressable_area: @object.account_addressable_area,
        location: @object.location,
        company: @object.company,
        industry: industry_output,
        **maybe(:account_plans, &:account_plans_output)
      }
    end

    def show_account_plans?
      return false if list_account_plans.blank?

      list_account_plans
    end

    def account_plans_output
      curr_account_plans = account_plans.select { |ap| ap.account_plan_group_id == @object.id }

      return [] if curr_account_plans.blank?

      curr_account_plans.map { |ap| ::V1::AccountPlanOutput.new(ap, organization_users: organization_users).format }
    end

    def industry_output
      return if industries.blank?

      curr_industry = industries.find { |i| i.id == @object.industry_id }

      return if curr_industry.blank?

      ::V1::IndustryOutput.new(curr_industry).format
    end

    def account_plans
      @options[:account_plans]
    end

    def organization_users
      @options[:organization_users]
    end

    def list_account_plans
      @options[:list_account_plans]
    end

    def industries
      @options[:industries]
    end
  end
end
