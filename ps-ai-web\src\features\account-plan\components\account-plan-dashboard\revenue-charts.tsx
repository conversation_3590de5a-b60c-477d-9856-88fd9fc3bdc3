"use client";

import React, { useMemo } from "react";
import { <PERSON>, BarChart, Cell, Tooltip, XAxis, YAxi<PERSON> } from "recharts";
import { parseAsString, useQueryState } from "next-usequerystate";

import { ChartConfig, ChartContainer } from "@/components/ui/chart";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { useAccountPlanGroupsList } from "@/features/account-plan/api/account-plan-group/get-account-plan-group-list";
import {
  AccountPlanData,
  AccountPlanGroupsData,
  AccountPlanGroupStatus,
  AccountPlanStatus,
} from "@/features/account-plan/types";
import { useWalletShareLists } from "@/features/account-plan/api/position-apis/wallet-share/get-wallet-share-list";
import { APWalletShareType } from "@/features/account-plan/types/position-types";
import { Progress } from "@/components/ui/progress";
import { useIsHydrated } from "@/lib/hooks/use-hydrated";
import { useOrganization } from "@/features/organizations/api/get-organization";
import { useExchangeRateList } from "@/features/utilities/api/get-exchange-rates";
import { getCurrencyCode } from "@/constants/currencies";
import { useScalingDimension } from "@/lib/hooks/use-scaling-dimension";
import { PUBLIC_ORGANIZATION_ID } from "@/features/organizations/constants";

type ChartData = {
  name: string;
  existing: number;
  competition: number;
  available: number;
  existingValue: number;
  competitionValue: number;
  availableValue: number;
  isEmptyAddressable: boolean;
};

type SortOption = {
  label: string;
  value: string;
  sortFn: (a: ChartData, b: ChartData) => number;
};
const sortOptions: Array<SortOption> = [
  {
    label: "Alphabetical A-Z",
    value: "a_z",
    sortFn: (a, b) => a.name?.localeCompare(b?.name),
  },
  {
    label: "Existing",
    value: "existing",
    sortFn: (a, b) => b.existingValue - a.existingValue,
  },
  {
    label: "Competition",
    value: "competition",
    sortFn: (a, b) => b.competitionValue - a.competitionValue,
  },
  {
    label: "Available",
    value: "available",
    sortFn: (a, b) => b.availableValue - a.availableValue,
  },
];
type ActiveAccountPlanData = Omit<AccountPlanGroupsData, "account_plans"> & {
  active_plan?: AccountPlanData;
};

const getLongestWord = (text: string) => {
  return text
    .split(" ")
    .reduce(
      (longest, word) => (word.length > longest.length ? word : longest),
      ""
    );
};

interface CustomTickProps {
  x: number;
  y: number;
  payload: { value: string };
  dy: number;
}

const PRESERVE_SUFFIXES = [
  "Pte. Ltd.",
  "Co. Ltd.",
  "LLC",
  "Inc.",
  "Ltd.",
  "Corp.",
];

// Function to split name while keeping business suffixes intact
const splitWords = (text: string): string[] => {
  for (const suffix of PRESERVE_SUFFIXES) {
    if (text.includes(suffix)) {
      const [beforeSuffix] = text.split(suffix);
      return [...beforeSuffix.trim().split(" "), suffix];
    }
  }

  return text.split(" ");
};

const CustomYAxisTick = ({
  x = 0,
  y = 0,
  payload,
  dy = 14,
}: CustomTickProps) => {
  return (
    <text x={x} y={y} textAnchor="end" fill="#333">
      {splitWords(payload.value).map((word, index) => (
        <tspan key={index} x={x} dy={index === 0 ? 0 : dy}>
          {word}
        </tspan>
      ))}
    </text>
  );
};

function AccountPlanChart() {
  const [sort, setSort] = useQueryState(
    "wallet_sort",
    parseAsString.withDefault(sortOptions[0].value)
  );
  const { isHydrated } = useIsHydrated();
  const { organization } = useOrganization({
    organizationId: PUBLIC_ORGANIZATION_ID,
  });
  const { accountPlanGroupsList, isFetching: isFetchingAccountPlanGroup } =
    useAccountPlanGroupsList({
      params: {
        status: AccountPlanGroupStatus.ACTIVE,
      },
    });
  const { exchangeRate } = useExchangeRateList({
    params: {
      currency: "USD",
    },
  });
  const { getWidth, getHeight } = useScalingDimension();

  const activeAccountPlans: Array<ActiveAccountPlanData> = useMemo(() => {
    const apData = accountPlanGroupsList.map(({ account_plans, ...group }) => {
      const active_plan = account_plans.find(
        (plan) => plan.status === AccountPlanStatus.ACTIVE
      );
      return { active_plan, ...group };
    });
    return apData;
  }, [accountPlanGroupsList]);

  const activePlansIds = activeAccountPlans
    .map((v) => ({
      accountId: v.active_plan?.id,
    }))
    .filter((v): v is { accountId: number } => !!v.accountId);

  const walletShareLists = useWalletShareLists({
    queries: activePlansIds,
  });

  const chartData: Array<ChartData> = useMemo(() => {
    const mappedRevenueData = activeAccountPlans.map((activeAccount) => {
      const currencyCode = getCurrencyCode(activeAccount.currency);
      const conversionRate = 1 / (exchangeRate?.[currencyCode] ?? 1);
      const walletSizeList = walletShareLists.find(
        (v) => v.query.accountId === activeAccount.active_plan?.id
      )?.walletShareList;

      const addressable =
        walletSizeList?.find(
          (v) => v.item_type === APWalletShareType.ADDRESSABLE
        )?.shared_type_analysis ?? 0;
      const isEmptyAddressable = addressable === 0;

      const existing =
        walletSizeList?.find((v) => v.item_type === APWalletShareType.OURS)
          ?.shared_type_analysis ?? 0;

      const competition =
        walletSizeList?.find(
          (v) => v.item_type === APWalletShareType.COMPETITION
        )?.shared_type_analysis ?? 0;

      const available = isEmptyAddressable
        ? 0
        : addressable - existing - competition;

      return {
        name: !!activeAccount.account_plan_unique_id
          ? `${activeAccount.account_plan_unique_id} - ${activeAccount.company} `
          : "Untitled",
        shortName: activeAccount.company ?? "Untitled",
        existing: isEmptyAddressable ? 0 : (existing / addressable) * 100,
        competition: isEmptyAddressable ? 0 : (competition / addressable) * 100,
        available: isEmptyAddressable ? 100 : (available / addressable) * 100,
        walletValue: addressable * conversionRate,
        existingValue: existing * conversionRate,
        competitionValue: competition * conversionRate,
        availableValue: available * conversionRate,
        isEmptyAddressable,
      };
    });

    return mappedRevenueData.sort(
      sortOptions.find((v) => v.value === sort)?.sortFn
    );
  }, [activeAccountPlans, walletShareLists, sort, exchangeRate]);

  const isLoadingChartData = useMemo(() => {
    const isFetchingWalletShare = walletShareLists.some((v) => v.isFetching);
    return isFetchingWalletShare || isFetchingAccountPlanGroup;
  }, [walletShareLists, isFetchingAccountPlanGroup]);

  const chartConfig = useMemo(
    () =>
      ({
        existing: {
          label: "Existing",
          color:
            organization?.ap_current_revenue_color ?? "var(--secondary-400)",
        },
        competition: {
          label: "Competition",
          color:
            organization?.ap_current_opportunity_color ?? "var(--teal-400)",
        },
        available: {
          label: "Available",
          color:
            organization?.ap_potential_opportunity_color ??
            "var(--neutral-200)",
        },
      }) satisfies ChartConfig,
    [organization]
  );

  const maxTextWidth = useMemo(() => {
    const longestWord = chartData.reduce((max, item) => {
      const longestWordInName = getLongestWord(item.name);
      return longestWordInName.length > max.length ? longestWordInName : max;
    }, "");

    return getWidth(longestWord.length * 7.5);
  }, [chartData, getWidth]);

  if (!isHydrated) return null;

  return (
    <section className="relative flex grow flex-col rounded-md bg-white px-res-x-lg py-res-y-lg">
      <div className="flex items-center">
        <Select value={sort} onValueChange={setSort}>
          <SelectTrigger
            className="h-[3.5vh] w-[11vw] px-res-x-xs text-left text-xs"
            iconClassName="!ml-[0]"
          >
            <SelectValue placeholder="Sort By" />
          </SelectTrigger>
          <SelectContent>
            {sortOptions.map((v, idx) => (
              <SelectItem key={idx} value={v.value} className="text-xs">
                {v.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <div className="ml-res-x-sm flex grow gap-res-x-sm">
          {Object.entries(chartConfig).map((v, idx) => {
            if (!v[1].label) return null;

            return (
              <div key={idx} className="flex gap-res-x-xs">
                <div
                  className="size-res-y-base shrink-0 rounded-full bg-primary-50"
                  style={{ backgroundColor: v[1].color }}
                ></div>
                <p className="text-xs">{v[1].label}</p>
              </div>
            );
          })}
        </div>
      </div>

      <ChartContainer
        config={chartConfig}
        className="e mt-res-y-sm h-[40vh] w-full overflow-y-scroll bg-white"
        width="100%"
        height={getHeight(chartData.length * 100)}
      >
        {isLoadingChartData ? (
          <div className="my-auto flex h-full max-h-[42vh] flex-col items-center justify-center">
            <p
              className={cn(
                "mb-1 text-xs text-neutral-500 transition-opacity duration-500 ease-in-out"
              )}
            >
              Loading Chart Data
            </p>
            <Progress indeterminate className="h-[0.5vh] w-[10vw]" />
          </div>
        ) : (
          <BarChart
            data={chartData}
            layout="vertical"
            height={Math.max(getHeight(chartData.length * 90), getHeight(360))}
          >
            <XAxis
              type="number"
              orientation="top"
              domain={[0, "dataMax"]}
              tickFormatter={(tick) => `${tick}%`}
              padding={{ left: 1 }}
            />
            <YAxis
              dataKey="shortName"
              type="category"
              className="font-bold text-primary-500"
              tickCount={6}
              strokeWidth={2}
              width={maxTextWidth}
              tick={(props) => (
                <CustomYAxisTick dy={getHeight(14)} {...props} />
              )}
            />

            <Tooltip
              content={({ payload }) => {
                if (!payload?.length) return null;

                const data = payload[0]?.payload;
                const walletValue = data?.walletValue ?? 0;

                return (
                  <div className="flex flex-col gap-res-y-xs border bg-white p-res-x-xs">
                    <div className="text-sm font-bold">{data?.name}</div>
                    <div>
                      <strong>Wallet Size :</strong> USD${" "}
                      {walletValue.toLocaleString()}
                    </div>

                    {payload.map((entry, i) => {
                      const valueKey = `${entry.dataKey}Value`;
                      const value = data?.[valueKey] ?? 0;

                      if (entry.name === "background") return null;

                      return (
                        <div key={i} style={{ color: entry.color }}>
                          <strong>{entry.name} :</strong> USD${" "}
                          {value.toLocaleString()}
                        </div>
                      );
                    })}
                  </div>
                );
              }}
            />

            {Object.keys(chartConfig).map((key, idx) => {
              const barProps = {
                dataKey: key,
                stackId: "a",
                layout: "vertical",
                name: chartConfig[key as keyof typeof chartConfig].label,
                barSize: getHeight(70),
                style: { zIndex: -1 },
              } as const;

              if (key === "available") {
                return (
                  <Bar key={idx} {...barProps}>
                    {chartData.map((entry, index) => {
                      return (
                        <Cell
                          key={`cell-${index}`}
                          fill={
                            entry.isEmptyAddressable
                              ? "var(--neutral-400)"
                              : chartConfig.available.color
                          }
                        />
                      );
                    })}
                  </Bar>
                );
              }

              return (
                <Bar
                  key={idx}
                  fill={chartConfig[key as keyof typeof chartConfig].color}
                  {...barProps}
                />
              );
            })}
          </BarChart>
        )}
      </ChartContainer>
    </section>
  );
}

export default AccountPlanChart;
