# frozen_string_literal: true

module V1
  class AccountPlanTableItems::ApStakeholderMappingItemsController < ApiController
    authorize_auth_token! :all

    def index
      account_plan_id = params[:account_plan_id]
      result = service.index(account_plan_id, query_params)

      render_json_array result.ap_stakeholder_mapping_items,
                        use: :format
    end

    def show
      account_plan_id = params[:account_plan_id]
      result = service.show(account_plan_id, params[:id])

      render_json result.ap_stakeholder_mapping_item,
                  use: :format
    end

    def create
      account_plan_id = params[:account_plan_id]
      input = ::V1::AccountPlanTableItems::ApStakeholderMappingItemCreationInput.new(request_body)
      validate! input, capture_failure: true

      result = service.create(account_plan_id, input.output)

      render_json result.ap_stakeholder_mapping_item,
                  use: :format,
                  status: :created
    end

    def import
      account_plan_id = params[:account_plan_id]
      input = request_body.slice('firstname', 'lastname', 'jobtitle', 'city')
        
      ap_table = ApTable.find_or_create_by!(
        table_category: 'position',
        table_type: 'stakeholder_mapping',
        account_plan_id: account_plan_id
      )
        
      ap_item = ::AccountPlanTableItems::ApStakeholderMappingItem.create!(
        ap_table_id: ap_table.id,
        name: "#{input['firstname']} #{input['lastname']}",
        title: input['jobtitle'],
        location: input['city']
      )
        
      render_json ap_item,
                  use: :format,
                  status: :created
    end

    def update
      account_plan_id = params[:account_plan_id]
      input = ::V1::AccountPlanTableItems::ApStakeholderMappingItemUpdateInput.new(request_body)
      validate! input, capture_failure: true

      result = service.update(account_plan_id, params[:id], input.output)

      render_json result.ap_stakeholder_mapping_item,
                  use: :format,
                  status: :ok
    end

    def destroy
      account_plan_id = params[:account_plan_id]
      service.destroy(account_plan_id, params[:id])

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::AccountPlanTableItems::ApStakeholderMappingItemOutput
    end

    def service
      @service ||= ::AccountPlanTableItems::ApStakeholderMappingItemService.new(current_user_data)
    end
  end
end
