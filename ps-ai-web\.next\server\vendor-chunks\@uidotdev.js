"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uidotdev";
exports.ids = ["vendor-chunks/@uidotdev"];
exports.modules = {

/***/ "(ssr)/./node_modules/@uidotdev/usehooks/index.js":
/*!**************************************************!*\
  !*** ./node_modules/@uidotdev/usehooks/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBattery: () => (/* binding */ useBattery),\n/* harmony export */   useClickAway: () => (/* binding */ useClickAway),\n/* harmony export */   useCopyToClipboard: () => (/* binding */ useCopyToClipboard),\n/* harmony export */   useCounter: () => (/* binding */ useCounter),\n/* harmony export */   useDebounce: () => (/* binding */ useDebounce),\n/* harmony export */   useDefault: () => (/* binding */ useDefault),\n/* harmony export */   useDocumentTitle: () => (/* binding */ useDocumentTitle),\n/* harmony export */   useFavicon: () => (/* binding */ useFavicon),\n/* harmony export */   useGeolocation: () => (/* binding */ useGeolocation),\n/* harmony export */   useHistoryState: () => (/* binding */ useHistoryState),\n/* harmony export */   useHover: () => (/* binding */ useHover),\n/* harmony export */   useIdle: () => (/* binding */ useIdle),\n/* harmony export */   useIntersectionObserver: () => (/* binding */ useIntersectionObserver),\n/* harmony export */   useIsClient: () => (/* binding */ useIsClient),\n/* harmony export */   useIsFirstRender: () => (/* binding */ useIsFirstRender),\n/* harmony export */   useList: () => (/* binding */ useList),\n/* harmony export */   useLocalStorage: () => (/* binding */ useLocalStorage),\n/* harmony export */   useLockBodyScroll: () => (/* binding */ useLockBodyScroll),\n/* harmony export */   useLongPress: () => (/* binding */ useLongPress),\n/* harmony export */   useMap: () => (/* binding */ useMap),\n/* harmony export */   useMeasure: () => (/* binding */ useMeasure),\n/* harmony export */   useMediaQuery: () => (/* binding */ useMediaQuery),\n/* harmony export */   useMouse: () => (/* binding */ useMouse),\n/* harmony export */   useNetworkState: () => (/* binding */ useNetworkState),\n/* harmony export */   useObjectState: () => (/* binding */ useObjectState),\n/* harmony export */   useOrientation: () => (/* binding */ useOrientation),\n/* harmony export */   usePreferredLanguage: () => (/* binding */ usePreferredLanguage),\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious),\n/* harmony export */   useQueue: () => (/* binding */ useQueue),\n/* harmony export */   useRenderCount: () => (/* binding */ useRenderCount),\n/* harmony export */   useRenderInfo: () => (/* binding */ useRenderInfo),\n/* harmony export */   useScript: () => (/* binding */ useScript),\n/* harmony export */   useSessionStorage: () => (/* binding */ useSessionStorage),\n/* harmony export */   useSet: () => (/* binding */ useSet),\n/* harmony export */   useThrottle: () => (/* binding */ useThrottle),\n/* harmony export */   useToggle: () => (/* binding */ useToggle),\n/* harmony export */   useVisibilityChange: () => (/* binding */ useVisibilityChange),\n/* harmony export */   useWindowScroll: () => (/* binding */ useWindowScroll),\n/* harmony export */   useWindowSize: () => (/* binding */ useWindowSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\nfunction isShallowEqual(object1, object2) {\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (let key of keys1) {\n    if (object1[key] !== object2[key]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction isTouchEvent({ nativeEvent }) {\n  return window.TouchEvent\n    ? nativeEvent instanceof TouchEvent\n    : \"touches\" in nativeEvent;\n}\n\nfunction isMouseEvent(event) {\n  return event.nativeEvent instanceof MouseEvent;\n}\n\nfunction throttle(cb, ms) {\n  let lastTime = 0;\n  return () => {\n    const now = Date.now();\n    if (now - lastTime >= ms) {\n      cb();\n      lastTime = now;\n    }\n  };\n}\n\nfunction isPlainObject(value) {\n  return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n\nfunction dispatchStorageEvent(key, newValue) {\n  window.dispatchEvent(new StorageEvent(\"storage\", { key, newValue }));\n}\n\nfunction useBattery() {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    supported: true,\n    loading: true,\n    level: null,\n    charging: null,\n    chargingTime: null,\n    dischargingTime: null,\n  });\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (!navigator.getBattery) {\n      setState((s) => ({\n        ...s,\n        supported: false,\n        loading: false,\n      }));\n      return;\n    }\n\n    let battery = null;\n\n    const handleChange = () => {\n      setState({\n        supported: true,\n        loading: false,\n        level: battery.level,\n        charging: battery.charging,\n        chargingTime: battery.chargingTime,\n        dischargingTime: battery.dischargingTime,\n      });\n    };\n\n    navigator.getBattery().then((b) => {\n      battery = b;\n      handleChange();\n\n      b.addEventListener(\"levelchange\", handleChange);\n      b.addEventListener(\"chargingchange\", handleChange);\n      b.addEventListener(\"chargingtimechange\", handleChange);\n      b.addEventListener(\"dischargingtimechange\", handleChange);\n    });\n\n    return () => {\n      if (battery) {\n        battery.removeEventListener(\"levelchange\", handleChange);\n        battery.removeEventListener(\"chargingchange\", handleChange);\n        battery.removeEventListener(\"chargingtimechange\", handleChange);\n        battery.removeEventListener(\"dischargingtimechange\", handleChange);\n      }\n    };\n  }, []);\n\n  return state;\n}\n\nfunction useClickAway(cb) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const refCb = react__WEBPACK_IMPORTED_MODULE_0__.useRef(cb);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    refCb.current = cb;\n  });\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handler = (e) => {\n      const element = ref.current;\n      if (element && !element.contains(e.target)) {\n        refCb.current(e);\n      }\n    };\n\n    document.addEventListener(\"mousedown\", handler);\n    document.addEventListener(\"touchstart\", handler);\n\n    return () => {\n      document.removeEventListener(\"mousedown\", handler);\n      document.removeEventListener(\"touchstart\", handler);\n    };\n  }, []);\n\n  return ref;\n}\n\nfunction oldSchoolCopy(text) {\n  const tempTextArea = document.createElement(\"textarea\");\n  tempTextArea.value = text;\n  document.body.appendChild(tempTextArea);\n  tempTextArea.select();\n  document.execCommand(\"copy\");\n  document.body.removeChild(tempTextArea);\n}\n\nfunction useCopyToClipboard() {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n\n  const copyToClipboard = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((value) => {\n    const handleCopy = async () => {\n      try {\n        if (navigator?.clipboard?.writeText) {\n          await navigator.clipboard.writeText(value);\n          setState(value);\n        } else {\n          throw new Error(\"writeText not supported\");\n        }\n      } catch (e) {\n        oldSchoolCopy(value);\n        setState(value);\n      }\n    };\n\n    handleCopy();\n  }, []);\n\n  return [state, copyToClipboard];\n}\n\nfunction useCounter(startingValue = 0, options = {}) {\n  const { min, max } = options;\n\n  if (typeof min === \"number\" && startingValue < min) {\n    throw new Error(\n      `Your starting value of ${startingValue} is less than your min of ${min}.`\n    );\n  }\n\n  if (typeof max === \"number\" && startingValue > max) {\n    throw new Error(\n      `Your starting value of ${startingValue} is greater than your max of ${max}.`\n    );\n  }\n\n  const [count, setCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(startingValue);\n\n  const increment = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    setCount((c) => {\n      const nextCount = c + 1;\n\n      if (typeof max === \"number\" && nextCount > max) {\n        return c;\n      }\n\n      return nextCount;\n    });\n  }, [max]);\n\n  const decrement = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    setCount((c) => {\n      const nextCount = c - 1;\n\n      if (typeof min === \"number\" && nextCount < min) {\n        return c;\n      }\n\n      return nextCount;\n    });\n  }, [min]);\n\n  const set = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextCount) => {\n      setCount((c) => {\n        if (typeof max === \"number\" && nextCount > max) {\n          return c;\n        }\n\n        if (typeof min === \"number\" && nextCount < min) {\n          return c;\n        }\n\n        return nextCount;\n      });\n    },\n    [max, min]\n  );\n\n  const reset = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    setCount(startingValue);\n  }, [startingValue]);\n\n  return [\n    count,\n    {\n      increment,\n      decrement,\n      set,\n      reset,\n    },\n  ];\n}\n\nfunction useDebounce(value, delay) {\n  const [debouncedValue, setDebouncedValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(value);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n\n  return debouncedValue;\n}\n\nfunction useDefault(initialValue, defaultValue) {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(initialValue);\n\n  if (typeof state === \"undefined\" || state === null) {\n    return [defaultValue, setState];\n  }\n\n  return [state, setState];\n}\n\nfunction useDocumentTitle(title) {\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    document.title = title;\n  }, [title]);\n}\n\nfunction useFavicon(url) {\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    let link = document.querySelector(`link[rel~=\"icon\"]`);\n\n    if (!link) {\n      link = document.createElement(\"link\");\n      link.type = \"image/x-icon\";\n      link.rel = \"icon\";\n      link.href = url;\n      document.head.appendChild(link);\n    } else {\n      link.href = url;\n    }\n  }, [url]);\n}\n\nfunction useGeolocation(options = {}) {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    loading: true,\n    accuracy: null,\n    altitude: null,\n    altitudeAccuracy: null,\n    heading: null,\n    latitude: null,\n    longitude: null,\n    speed: null,\n    timestamp: null,\n    error: null,\n  });\n\n  const optionsRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(options);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const onEvent = ({ coords, timestamp }) => {\n      setState({\n        loading: false,\n        timestamp,\n        latitude: coords.latitude,\n        longitude: coords.longitude,\n        altitude: coords.altitude,\n        accuracy: coords.accuracy,\n        altitudeAccuracy: coords.altitudeAccuracy,\n        heading: coords.heading,\n        speed: coords.speed,\n      });\n    };\n\n    const onEventError = (error) => {\n      setState((s) => ({\n        ...s,\n        loading: false,\n        error,\n      }));\n    };\n\n    navigator.geolocation.getCurrentPosition(\n      onEvent,\n      onEventError,\n      optionsRef.current\n    );\n\n    const watchId = navigator.geolocation.watchPosition(\n      onEvent,\n      onEventError,\n      optionsRef.current\n    );\n\n    return () => {\n      navigator.geolocation.clearWatch(watchId);\n    };\n  }, []);\n\n  return state;\n}\n\nconst initialUseHistoryStateState = {\n  past: [],\n  present: null,\n  future: [],\n};\n\nconst useHistoryStateReducer = (state, action) => {\n  const { past, present, future } = state;\n\n  if (action.type === \"UNDO\") {\n    return {\n      past: past.slice(0, past.length - 1),\n      present: past[past.length - 1],\n      future: [present, ...future],\n    };\n  } else if (action.type === \"REDO\") {\n    return {\n      past: [...past, present],\n      present: future[0],\n      future: future.slice(1),\n    };\n  } else if (action.type === \"SET\") {\n    const { newPresent } = action;\n\n    if (action.newPresent === present) {\n      return state;\n    }\n\n    return {\n      past: [...past, present],\n      present: newPresent,\n      future: [],\n    };\n  } else if (action.type === \"CLEAR\") {\n    return {\n      ...initialUseHistoryStateState,\n      present: action.initialPresent,\n    };\n  } else {\n    throw new Error(\"Unsupported action type\");\n  }\n};\n\nfunction useHistoryState(initialPresent = {}) {\n  const initialPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(initialPresent);\n\n  const [state, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(useHistoryStateReducer, {\n    ...initialUseHistoryStateState,\n    present: initialPresentRef.current,\n  });\n\n  const canUndo = state.past.length !== 0;\n  const canRedo = state.future.length !== 0;\n\n  const undo = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    if (canUndo) {\n      dispatch({ type: \"UNDO\" });\n    }\n  }, [canUndo]);\n\n  const redo = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    if (canRedo) {\n      dispatch({ type: \"REDO\" });\n    }\n  }, [canRedo]);\n\n  const set = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (newPresent) => dispatch({ type: \"SET\", newPresent }),\n    []\n  );\n\n  const clear = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    () =>\n      dispatch({ type: \"CLEAR\", initialPresent: initialPresentRef.current }),\n    []\n  );\n\n  return { state: state.present, set, undo, redo, clear, canUndo, canRedo };\n}\n\nfunction useHover() {\n  const [hovering, setHovering] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const previousNode = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n\n  const handleMouseEnter = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    setHovering(true);\n  }, []);\n\n  const handleMouseLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    setHovering(false);\n  }, []);\n\n  const customRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (node) => {\n      if (previousNode.current?.nodeType === Node.ELEMENT_NODE) {\n        previousNode.current.removeEventListener(\n          \"mouseenter\",\n          handleMouseEnter\n        );\n        previousNode.current.removeEventListener(\n          \"mouseleave\",\n          handleMouseLeave\n        );\n      }\n\n      if (node?.nodeType === Node.ELEMENT_NODE) {\n        node.addEventListener(\"mouseenter\", handleMouseEnter);\n        node.addEventListener(\"mouseleave\", handleMouseLeave);\n      }\n\n      previousNode.current = node;\n    },\n    [handleMouseEnter, handleMouseLeave]\n  );\n\n  return [customRef, hovering];\n}\n\nfunction useIdle(ms = 1000 * 60) {\n  const [idle, setIdle] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    let timeoutId;\n\n    const handleTimeout = () => {\n      setIdle(true);\n    };\n\n    const handleEvent = throttle((e) => {\n      setIdle(false);\n\n      window.clearTimeout(timeoutId);\n      timeoutId = window.setTimeout(handleTimeout, ms);\n    }, 500);\n\n    const handleVisibilityChange = () => {\n      if (!document.hidden) {\n        handleEvent();\n      }\n    };\n\n    timeoutId = window.setTimeout(handleTimeout, ms);\n\n    window.addEventListener(\"mousemove\", handleEvent);\n    window.addEventListener(\"mousedown\", handleEvent);\n    window.addEventListener(\"resize\", handleEvent);\n    window.addEventListener(\"keydown\", handleEvent);\n    window.addEventListener(\"touchstart\", handleEvent);\n    window.addEventListener(\"wheel\", handleEvent);\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n\n    return () => {\n      window.removeEventListener(\"mousemove\", handleEvent);\n      window.removeEventListener(\"mousedown\", handleEvent);\n      window.removeEventListener(\"resize\", handleEvent);\n      window.removeEventListener(\"keydown\", handleEvent);\n      window.removeEventListener(\"touchstart\", handleEvent);\n      window.removeEventListener(\"wheel\", handleEvent);\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n      window.clearTimeout(timeoutId);\n    };\n  }, [ms]);\n\n  return idle;\n}\n\nfunction useIntersectionObserver(options = {}) {\n  const { threshold = 1, root = null, rootMargin = \"0px\" } = options;\n  const [entry, setEntry] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n\n  const previousObserver = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n\n  const customRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (node) => {\n      if (previousObserver.current) {\n        previousObserver.current.disconnect();\n        previousObserver.current = null;\n      }\n\n      if (node?.nodeType === Node.ELEMENT_NODE) {\n        const observer = new IntersectionObserver(\n          ([entry]) => {\n            setEntry(entry);\n          },\n          { threshold, root, rootMargin }\n        );\n\n        observer.observe(node);\n        previousObserver.current = observer;\n      }\n    },\n    [threshold, root, rootMargin]\n  );\n\n  return [customRef, entry];\n}\n\nfunction useIsClient() {\n  const [isClient, setIsClient] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  return isClient;\n}\n\nfunction useIsFirstRender() {\n  const renderRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n\n  if (renderRef.current === true) {\n    renderRef.current = false;\n    return true;\n  }\n\n  return renderRef.current;\n}\n\nfunction useList(defaultList = []) {\n  const [list, setList] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultList);\n\n  const set = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((l) => {\n    setList(l);\n  }, []);\n\n  const push = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((element) => {\n    setList((l) => [...l, element]);\n  }, []);\n\n  const removeAt = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((index) => {\n    setList((l) => [...l.slice(0, index), ...l.slice(index + 1)]);\n  }, []);\n\n  const insertAt = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((index, element) => {\n    setList((l) => [...l.slice(0, index), element, ...l.slice(index)]);\n  }, []);\n\n  const updateAt = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((index, element) => {\n    setList((l) => l.map((e, i) => (i === index ? element : e)));\n  }, []);\n\n  const clear = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => setList([]), []);\n\n  return [list, { set, push, removeAt, insertAt, updateAt, clear }];\n}\n\nconst setLocalStorageItem = (key, value) => {\n  const stringifiedValue = JSON.stringify(value);\n  window.localStorage.setItem(key, stringifiedValue);\n  dispatchStorageEvent(key, stringifiedValue);\n};\n\nconst removeLocalStorageItem = (key) => {\n  window.localStorage.removeItem(key);\n  dispatchStorageEvent(key, null);\n};\n\nconst getLocalStorageItem = (key) => {\n  return window.localStorage.getItem(key);\n};\n\nconst useLocalStorageSubscribe = (callback) => {\n  window.addEventListener(\"storage\", callback);\n  return () => window.removeEventListener(\"storage\", callback);\n};\n\nconst getLocalStorageServerSnapshot = () => {\n  throw Error(\"useLocalStorage is a client-only hook\");\n};\n\nfunction useLocalStorage(key, initialValue) {\n  const getSnapshot = () => getLocalStorageItem(key);\n\n  const store = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    useLocalStorageSubscribe,\n    getSnapshot,\n    getLocalStorageServerSnapshot\n  );\n\n  const setState = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (v) => {\n      try {\n        const nextState = typeof v === \"function\" ? v(JSON.parse(store)) : v;\n\n        if (nextState === undefined || nextState === null) {\n          removeLocalStorageItem(key);\n        } else {\n          setLocalStorageItem(key, nextState);\n        }\n      } catch (e) {\n        console.warn(e);\n      }\n    },\n    [key, store]\n  );\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (\n      getLocalStorageItem(key) === null &&\n      typeof initialValue !== \"undefined\"\n    ) {\n      setLocalStorageItem(key, initialValue);\n    }\n  }, [key, initialValue]);\n\n  return [store ? JSON.parse(store) : initialValue, setState];\n}\n\nfunction useLockBodyScroll() {\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    const originalStyle = window.getComputedStyle(document.body).overflow;\n    document.body.style.overflow = \"hidden\";\n    return () => {\n      document.body.style.overflow = originalStyle;\n    };\n  }, []);\n}\n\nfunction useLongPress(callback, options = {}) {\n  const { threshold = 400, onStart, onFinish, onCancel } = options;\n  const isLongPressActive = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const isPressed = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const timerId = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (typeof callback !== \"function\") {\n      return {};\n    }\n\n    const start = (event) => {\n      if (!isMouseEvent(event) && !isTouchEvent(event)) return;\n\n      if (onStart) {\n        onStart(event);\n      }\n\n      isPressed.current = true;\n      timerId.current = setTimeout(() => {\n        callback(event);\n        isLongPressActive.current = true;\n      }, threshold);\n    };\n\n    const cancel = (event) => {\n      if (!isMouseEvent(event) && !isTouchEvent(event)) return;\n\n      if (isLongPressActive.current) {\n        if (onFinish) {\n          onFinish(event);\n        }\n      } else if (isPressed.current) {\n        if (onCancel) {\n          onCancel(event);\n        }\n      }\n\n      isLongPressActive.current = false;\n      isPressed.current = false;\n\n      if (timerId.current) {\n        window.clearTimeout(timerId.current);\n      }\n    };\n\n    const mouseHandlers = {\n      onMouseDown: start,\n      onMouseUp: cancel,\n      onMouseLeave: cancel,\n    };\n\n    const touchHandlers = {\n      onTouchStart: start,\n      onTouchEnd: cancel,\n    };\n\n    return {\n      ...mouseHandlers,\n      ...touchHandlers,\n    };\n  }, [callback, threshold, onCancel, onFinish, onStart]);\n}\n\nfunction useMap(initialState) {\n  const mapRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(new Map(initialState));\n  const [, reRender] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer((x) => x + 1, 0);\n\n  mapRef.current.set = (...args) => {\n    Map.prototype.set.apply(mapRef.current, args);\n    reRender();\n    return mapRef.current;\n  };\n\n  mapRef.current.clear = (...args) => {\n    Map.prototype.clear.apply(mapRef.current, args);\n    reRender();\n  };\n\n  mapRef.current.delete = (...args) => {\n    const res = Map.prototype.delete.apply(mapRef.current, args);\n    reRender();\n\n    return res;\n  };\n\n  return mapRef.current;\n}\n\nfunction useMeasure() {\n  const [dimensions, setDimensions] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    width: null,\n    height: null,\n  });\n\n  const previousObserver = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n\n  const customRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node) => {\n    if (previousObserver.current) {\n      previousObserver.current.disconnect();\n      previousObserver.current = null;\n    }\n\n    if (node?.nodeType === Node.ELEMENT_NODE) {\n      const observer = new ResizeObserver(([entry]) => {\n        if (entry && entry.borderBoxSize) {\n          const { inlineSize: width, blockSize: height } =\n            entry.borderBoxSize[0];\n\n          setDimensions({ width, height });\n        }\n      });\n\n      observer.observe(node);\n      previousObserver.current = observer;\n    }\n  }, []);\n\n  return [customRef, dimensions];\n}\n\nfunction useMediaQuery(query) {\n  const subscribe = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (callback) => {\n      const matchMedia = window.matchMedia(query);\n\n      matchMedia.addEventListener(\"change\", callback);\n      return () => {\n        matchMedia.removeEventListener(\"change\", callback);\n      };\n    },\n    [query]\n  );\n\n  const getSnapshot = () => {\n    return window.matchMedia(query).matches;\n  };\n\n  const getServerSnapshot = () => {\n    throw Error(\"useMediaQuery is a client-only hook\");\n  };\n\n  return react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n}\n\nfunction useMouse() {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    x: 0,\n    y: 0,\n    elementX: 0,\n    elementY: 0,\n    elementPositionX: 0,\n    elementPositionY: 0,\n  });\n\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    const handleMouseMove = (event) => {\n      let newState = {\n        x: event.pageX,\n        y: event.pageY,\n      };\n\n      if (ref.current?.nodeType === Node.ELEMENT_NODE) {\n        const { left, top } = ref.current.getBoundingClientRect();\n        const elementPositionX = left + window.scrollX;\n        const elementPositionY = top + window.scrollY;\n        const elementX = event.pageX - elementPositionX;\n        const elementY = event.pageY - elementPositionY;\n\n        newState.elementX = elementX;\n        newState.elementY = elementY;\n        newState.elementPositionX = elementPositionX;\n        newState.elementPositionY = elementPositionY;\n      }\n\n      setState((s) => {\n        return {\n          ...s,\n          ...newState,\n        };\n      });\n    };\n\n    document.addEventListener(\"mousemove\", handleMouseMove);\n\n    return () => {\n      document.removeEventListener(\"mousemove\", handleMouseMove);\n    };\n  }, []);\n\n  return [state, ref];\n}\n\nconst getConnection = () => {\n  return (\n    navigator?.connection ||\n    navigator?.mozConnection ||\n    navigator?.webkitConnection\n  );\n};\n\nconst useNetworkStateSubscribe = (callback) => {\n  window.addEventListener(\"online\", callback, { passive: true });\n  window.addEventListener(\"offline\", callback, { passive: true });\n\n  const connection = getConnection();\n\n  if (connection) {\n    connection.addEventListener(\"change\", callback, { passive: true });\n  }\n\n  return () => {\n    window.removeEventListener(\"online\", callback);\n    window.removeEventListener(\"offline\", callback);\n\n    if (connection) {\n      connection.removeEventListener(\"change\", callback);\n    }\n  };\n};\n\nconst getNetworkStateServerSnapshot = () => {\n  throw Error(\"useNetworkState is a client-only hook\");\n};\n\nfunction useNetworkState() {\n  const cache = react__WEBPACK_IMPORTED_MODULE_0__.useRef({});\n\n  const getSnapshot = () => {\n    const online = navigator.onLine;\n    const connection = getConnection();\n\n    const nextState = {\n      online,\n      downlink: connection?.downlink,\n      downlinkMax: connection?.downlinkMax,\n      effectiveType: connection?.effectiveType,\n      rtt: connection?.rtt,\n      saveData: connection?.saveData,\n      type: connection?.type,\n    };\n\n    if (isShallowEqual(cache.current, nextState)) {\n      return cache.current;\n    } else {\n      cache.current = nextState;\n      return nextState;\n    }\n  };\n\n  return react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    useNetworkStateSubscribe,\n    getSnapshot,\n    getNetworkStateServerSnapshot\n  );\n}\n\nfunction useObjectState(initialValue) {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(initialValue);\n\n  const handleUpdate = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((arg) => {\n    if (typeof arg === \"function\") {\n      setState((s) => {\n        const newState = arg(s);\n\n        if (isPlainObject(newState)) {\n          return {\n            ...s,\n            ...newState,\n          };\n        }\n      });\n    }\n\n    if (isPlainObject(arg)) {\n      setState((s) => ({\n        ...s,\n        ...arg,\n      }));\n    }\n  }, []);\n\n  return [state, handleUpdate];\n}\n\nfunction useOrientation() {\n  const [orientation, setOrientation] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    angle: 0,\n    type: \"landscape-primary\",\n  });\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    const handleChange = () => {\n      const { angle, type } = window.screen.orientation;\n      setOrientation({\n        angle,\n        type,\n      });\n    };\n\n    const handle_orientationchange = () => {\n      setOrientation({\n        type: \"UNKNOWN\",\n        angle: window.orientation,\n      });\n    };\n\n    if (window.screen?.orientation) {\n      handleChange();\n      window.screen.orientation.addEventListener(\"change\", handleChange);\n    } else {\n      handle_orientationchange();\n      window.addEventListener(\"orientationchange\", handle_orientationchange);\n    }\n\n    return () => {\n      if (window.screen?.orientation) {\n        window.screen.orientation.removeEventListener(\"change\", handleChange);\n      } else {\n        window.removeEventListener(\n          \"orientationchange\",\n          handle_orientationchange\n        );\n      }\n    };\n  }, []);\n\n  return orientation;\n}\n\nconst usePreferredLanguageSubscribe = (cb) => {\n  window.addEventListener(\"languagechange\", cb);\n  return () => window.removeEventListener(\"languagechange\", cb);\n};\n\nconst getPreferredLanguageSnapshot = () => {\n  return navigator.language;\n};\n\nconst getPreferredLanguageServerSnapshot = () => {\n  throw Error(\"usePreferredLanguage is a client-only hook\");\n};\n\nfunction usePreferredLanguage() {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    usePreferredLanguageSubscribe,\n    getPreferredLanguageSnapshot,\n    getPreferredLanguageServerSnapshot\n  );\n}\n\nfunction usePrevious(value) {\n  const [current, setCurrent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(value);\n  const [previous, setPrevious] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n\n  if (value !== current) {\n    setPrevious(current);\n    setCurrent(value);\n  }\n\n  return previous;\n}\n\nfunction useQueue(initialValue = []) {\n  const [queue, setQueue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(initialValue);\n\n  const add = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((element) => {\n    setQueue((q) => [...q, element]);\n  }, []);\n\n  const remove = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    let removedElement;\n\n    setQueue(([first, ...q]) => {\n      removedElement = first;\n      return q;\n    });\n\n    return removedElement;\n  }, []);\n\n  const clear = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    setQueue([]);\n  }, []);\n\n  return {\n    add,\n    remove,\n    clear,\n    first: queue[0],\n    last: queue[queue.length - 1],\n    size: queue.length,\n    queue,\n  };\n}\n\nfunction useRenderCount() {\n  const count = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n\n  count.current++;\n\n  return count.current;\n}\n\nfunction useRenderInfo(name = \"Unknown\") {\n  const count = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n  const lastRender = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  const now = Date.now();\n\n  count.current++;\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    lastRender.current = Date.now();\n  });\n\n  const sinceLastRender = lastRender.current ? now - lastRender.current : 0;\n\n  if (true) {\n    const info = {\n      name,\n      renders: count.current,\n      sinceLastRender,\n      timestamp: now,\n    };\n\n    console.log(info);\n\n    return info;\n  }\n}\n\nfunction useScript(src, options = {}) {\n  const [status, setStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"loading\");\n  const optionsRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(options);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    let script = document.querySelector(`script[src=\"${src}\"]`);\n\n    const domStatus = script?.getAttribute(\"data-status\");\n    if (domStatus) {\n      setStatus(domStatus);\n      return;\n    }\n\n    if (script === null) {\n      script = document.createElement(\"script\");\n      script.src = src;\n      script.async = true;\n      script.setAttribute(\"data-status\", \"loading\");\n      document.body.appendChild(script);\n\n      const handleScriptLoad = () => {\n        script.setAttribute(\"data-status\", \"ready\");\n        setStatus(\"ready\");\n        removeEventListeners();\n      };\n\n      const handleScriptError = () => {\n        script.setAttribute(\"data-status\", \"error\");\n        setStatus(\"error\");\n        removeEventListeners();\n      };\n\n      const removeEventListeners = () => {\n        script.removeEventListener(\"load\", handleScriptLoad);\n        script.removeEventListener(\"error\", handleScriptError);\n      };\n\n      script.addEventListener(\"load\", handleScriptLoad);\n      script.addEventListener(\"error\", handleScriptError);\n\n      const removeOnUnmount = optionsRef.current.removeOnUnmount;\n\n      return () => {\n        if (removeOnUnmount === true) {\n          script.remove();\n          removeEventListeners();\n        }\n      };\n    } else {\n      setStatus(\"unknown\");\n    }\n  }, [src]);\n\n  return status;\n}\n\nconst setSessionStorageItem = (key, value) => {\n  const stringifiedValue = JSON.stringify(value);\n  window.sessionStorage.setItem(key, stringifiedValue);\n  dispatchStorageEvent(key, stringifiedValue);\n};\n\nconst removeSessionStorageItem = (key) => {\n  window.sessionStorage.removeItem(key);\n  dispatchStorageEvent(key, null);\n};\n\nconst getSessionStorageItem = (key) => {\n  return window.sessionStorage.getItem(key);\n};\n\nconst useSessionStorageSubscribe = (callback) => {\n  window.addEventListener(\"storage\", callback);\n  return () => window.removeEventListener(\"storage\", callback);\n};\n\nconst getSessionStorageServerSnapshot = () => {\n  throw Error(\"useSessionStorage is a client-only hook\");\n};\n\nfunction useSessionStorage(key, initialValue) {\n  const getSnapshot = () => getSessionStorageItem(key);\n\n  const store = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    useSessionStorageSubscribe,\n    getSnapshot,\n    getSessionStorageServerSnapshot\n  );\n\n  const setState = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (v) => {\n      try {\n        const nextState = typeof v === \"function\" ? v(JSON.parse(store)) : v;\n\n        if (nextState === undefined || nextState === null) {\n          removeSessionStorageItem(key);\n        } else {\n          setSessionStorageItem(key, nextState);\n        }\n      } catch (e) {\n        console.warn(e);\n      }\n    },\n    [key, store]\n  );\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (\n      getSessionStorageItem(key) === null &&\n      typeof initialValue !== \"undefined\"\n    ) {\n      setSessionStorageItem(key, initialValue);\n    }\n  }, [key, initialValue]);\n\n  return [store ? JSON.parse(store) : initialValue, setState];\n}\n\nfunction useSet(values) {\n  const setRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(new Set(values));\n  const [, reRender] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer((x) => x + 1, 0);\n\n  setRef.current.add = (...args) => {\n    const res = Set.prototype.add.apply(setRef.current, args);\n    reRender();\n\n    return res;\n  };\n\n  setRef.current.clear = (...args) => {\n    Set.prototype.clear.apply(setRef.current, args);\n    reRender();\n  };\n\n  setRef.current.delete = (...args) => {\n    const res = Set.prototype.delete.apply(setRef.current, args);\n    reRender();\n\n    return res;\n  };\n\n  return setRef.current;\n}\n\nfunction useThrottle(value, interval = 500) {\n  const [throttledValue, setThrottledValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(value);\n  const lastUpdated = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const now = Date.now();\n\n    if (lastUpdated.current && now >= lastUpdated.current + interval) {\n      lastUpdated.current = now;\n      setThrottledValue(value);\n    } else {\n      const id = window.setTimeout(() => {\n        lastUpdated.current = now;\n        setThrottledValue(value);\n      }, interval);\n\n      return () => window.clearTimeout(id);\n    }\n  }, [value, interval]);\n\n  return throttledValue;\n}\n\nfunction useToggle(initialValue) {\n  const [on, setOn] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => {\n    if (typeof initialValue === \"boolean\") {\n      return initialValue;\n    }\n\n    return Boolean(initialValue);\n  });\n\n  const handleToggle = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((value) => {\n    if (typeof value === \"boolean\") {\n      return setOn(value);\n    }\n\n    return setOn((v) => !v);\n  }, []);\n\n  return [on, handleToggle];\n}\n\nconst useVisibilityChangeSubscribe = (callback) => {\n  document.addEventListener(\"visibilitychange\", callback);\n\n  return () => {\n    document.removeEventListener(\"visibilitychange\", callback);\n  };\n};\n\nconst getVisibilityChangeSnapshot = () => {\n  return document.visibilityState;\n};\n\nconst getVisibilityChangeServerSnapshot = () => {\n  throw Error(\"useVisibilityChange is a client-only hook\");\n};\n\nfunction useVisibilityChange() {\n  const visibilityState = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    useVisibilityChangeSubscribe,\n    getVisibilityChangeSnapshot,\n    getVisibilityChangeServerSnapshot\n  );\n\n  return visibilityState === \"visible\";\n}\n\nfunction useWindowScroll() {\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    x: null,\n    y: null,\n  });\n\n  const scrollTo = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((...args) => {\n    if (typeof args[0] === \"object\") {\n      window.scrollTo(args[0]);\n    } else if (typeof args[0] === \"number\" && typeof args[1] === \"number\") {\n      window.scrollTo(args[0], args[1]);\n    } else {\n      throw new Error(\n        `Invalid arguments passed to scrollTo. See here for more info. https://developer.mozilla.org/en-US/docs/Web/API/Window/scrollTo`\n      );\n    }\n  }, []);\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    const handleScroll = () => {\n      setState({ x: window.scrollX, y: window.scrollY });\n    };\n\n    handleScroll();\n    window.addEventListener(\"scroll\", handleScroll);\n\n    return () => {\n      window.removeEventListener(\"scroll\", handleScroll);\n    };\n  }, []);\n\n  return [state, scrollTo];\n}\n\nfunction useWindowSize() {\n  const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    width: null,\n    height: null,\n  });\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    const handleResize = () => {\n      setSize({\n        width: window.innerWidth,\n        height: window.innerHeight,\n      });\n    };\n\n    handleResize();\n    window.addEventListener(\"resize\", handleResize);\n\n    return () => {\n      window.removeEventListener(\"resize\", handleResize);\n    };\n  }, []);\n\n  return size;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@uidotdev/usehooks/index.js\n");

/***/ })

};
;