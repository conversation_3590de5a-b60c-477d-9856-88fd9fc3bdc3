import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ReactNode, useMemo, useState } from "react";

import {
  TableBody,
  TableCell,
  TableHeader,
  TableHead,
  TableRow,
  Table,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { getCurrencyAbbreviations } from "@/constants/currencies";
import { Checkbox } from "../checkbox";
import { Popover, PopoverContent, PopoverTrigger } from "../popover";

type ColumnDefWithCustomMeta<TData, TValue = unknown> = ColumnDef<
  TData,
  TValue
> & {
  meta?: {
    padding?: boolean;
    headerClassName?: string;
  };
};

type DataTableProps<TData, TValue> = {
  columns: ColumnDefWithCustomMeta<TData>[];
  data: (TData & { tooltip?: ReactNode })[];
  isPreview?: boolean;
  height?: string;
  rowSelection?: Record<string, boolean>;
  setRowSelection?: React.Dispatch<
    React.SetStateAction<Record<string, boolean>>
  >;
  showHeader?: boolean;
  selectSize?: number;
  headerClassName?: string;
  size?: "m" | "l";
  variant?: "default" | "alt1";
  emptyMessage?: React.ReactNode;
  currency?: string | null;
};

export type DataTableMeta = {
  tooltip?: string | (() => React.ReactNode);
  isPreview?: boolean;
};

function TableTooltip({
  displayName,
  description,
  isPreview,
  currency,
}: {
  displayName: string;
  description: string;
  isPreview?: boolean;
  currency?: string | null;
}) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Popover open={isOpen}>
      <PopoverTrigger
        className="text-left"
        onMouseLeave={() => setIsOpen(false)}
        onMouseEnter={() => setIsOpen(true)}
      >
        {displayName.replace(
          "Currency Value",
          getCurrencyAbbreviations(currency)
        )}
      </PopoverTrigger>
      <PopoverContent
        side="bottom"
        align="start"
        className={cn(
          "w-fit flex-wrap whitespace-normal px-res-x-sm py-res-y-sm font-semibold",
          isPreview ? "w-fit max-w-[30vw] text-base" : "w-[40vw] text-2xl"
        )}
      >
        {description}
      </PopoverContent>
    </Popover>
  );
}

export function DataTable<TData, TValue>({
  columns,
  data,
  isPreview,
  height,
  rowSelection,
  setRowSelection,
  showHeader = true,
  selectSize = 50,
  headerClassName,
  size = "l",
  variant = "default",
  emptyMessage = "No data found",
  currency,
}: DataTableProps<TData, TValue>) {
  const table = useReactTable({
    data,
    columns: setRowSelection
      ? [
          {
            id: "select",
            size: selectSize,
            header: ({ table }) => (
              <Checkbox
                checked={table.getIsAllRowsSelected()}
                onCheckedChange={table.getToggleAllRowsSelectedHandler()}
              />
            ),
            cell: ({ row }) => (
              <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
                <Checkbox
                  className="border-2"
                  checked={row.getIsSelected()}
                  onCheckedChange={row.getToggleSelectedHandler()}
                />
              </div>
            ),
            enableSorting: false,
            enableColumnFilter: false,
          },
          ...columns,
        ]
      : columns,
    getCoreRowModel: getCoreRowModel(),
    state: rowSelection ? { rowSelection } : undefined,
    onRowSelectionChange: setRowSelection,
    enableRowSelection: !!setRowSelection,
    meta: {
      isPreview,
    },
  });

  const sizing = useMemo(() => {
    const sizeList = {
      m: {
        padding: "px-res-x-sm py-res-y-base",
        text: "text-base",
        header: "",
      },
      l: {
        padding: "px-res-x-sm py-res-y-base",
        text: "text-3xl",
        header: "h-[8vh]",
      },
    };

    return sizeList[size];
  }, [size]);

  const styling = useMemo(() => {
    const variantConfigList = {
      default: {
        header: "text-primary-500 bg-white border-none",
        headerCell: "bg-white text-[16px]",
        row: "even:bg-white odd:bg-white",
        body: "[&_tr:hover]:bg-white",
      },
      alt1: {
        header: "text-white rounded-lg",
        headerCell: "bg-gradient",
        row: "",
        body: "",
      },
    };

    return variantConfigList[variant];
  }, [variant]);

  return (
    <div
      className="w-full overflow-y-auto rounded-xl bg-white p-4"
      style={{ height }}
    >
      <Table className="h-[1px] w-full table-fixed overflow-hidden">
        <TableHeader
          className={cn(
            "w-full",
            styling.header,
            sizing.text,
            showHeader ? sizing.header : "h-0 text-[0px] leading-[0]"
          )}
        >
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                const meta = header.column.columnDef.meta as
                  | DataTableMeta
                  | undefined;

                return (
                  <TableHead
                    className={cn(
                      "h-full items-start border-neutral-100 text-left font-bold",
                      styling.headerCell,
                      showHeader && `${sizing.padding} border`,
                      headerClassName
                    )}
                    key={header.id}
                    style={{ width: `${header.getSize()}px` }}
                  >
                    {typeof meta?.tooltip === "string" ? (
                      <TableTooltip
                        currency={currency}
                        isPreview={isPreview}
                        displayName={
                          (header.column.columnDef.header as string) ?? ""
                        }
                        description={meta.tooltip}
                      />
                    ) : typeof meta?.tooltip === "function" ? (
                      <>{meta?.tooltip()}</>
                    ) : (
                      <>{header.column.columnDef.header}</>
                    )}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>

        <TableBody className={cn("flex-auto", styling.body)}>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected?.() ? "selected" : undefined}
                className={cn("h-fit", sizing.text, styling.row)}
              >
                {row.getVisibleCells().map((cell) => {
                  const meta = (
                    cell.column.columnDef as ColumnDefWithCustomMeta<TData>
                  ).meta;

                  return (
                    <TableCell
                      className={cn(
                        "relative border-4 border-neutral-100 align-top",
                        meta?.padding ? sizing.padding : "p-0"
                      )}
                      key={cell.id}
                    >
                      {flexRender(cell.column.columnDef.cell, {
                        ...cell.getContext(),
                      })}
                    </TableCell>
                  );
                })}
              </TableRow>
            ))
          ) : (
            <TableRow className="w-full hover:bg-white">
              <TableCell
                colSpan={columns.length + (!!setRowSelection ? 1 : 0)}
                className="h-24 w-full bg-white text-center text-3xl"
              >
                {emptyMessage}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}

export default DataTable;
