# frozen_string_literal: true

# Account Plan Table
class AccountPlanTableItems::ApStakeholderMappingItem < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  enum influence: string_enum('Low', 'Medium', 'High')
  # enum role: string_enum('Sponsor', 'Sponsor & Coach', 'Coach', 'Anti-sponsor', 'Stakeholder')
  enum perception: string_enum('Strategic Advisor', 'Trusted Partner', 'Solution Provider', 'Preferred Supplier', 'Vendor')
  enum advocacy: string_enum('Advocates Us', 'Prefers Us', 'No Preference', 'Unsure of Us', 'Prefers Alternative', 'Advocates Alternative')

  belongs_to :ap_table
end
