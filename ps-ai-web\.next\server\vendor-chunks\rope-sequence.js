"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rope-sequence";
exports.ids = ["vendor-chunks/rope-sequence"];
exports.modules = {

/***/ "(ssr)/./node_modules/rope-sequence/dist/index.js":
/*!**************************************************!*\
  !*** ./node_modules/rope-sequence/dist/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar GOOD_LEAF_SIZE = 200;\n\n// :: class<T> A rope sequence is a persistent sequence data structure\n// that supports appending, prepending, and slicing without doing a\n// full copy. It is represented as a mostly-balanced tree.\nvar RopeSequence = function RopeSequence () {};\n\nRopeSequence.prototype.append = function append (other) {\n  if (!other.length) { return this }\n  other = RopeSequence.from(other);\n\n  return (!this.length && other) ||\n    (other.length < GOOD_LEAF_SIZE && this.leafAppend(other)) ||\n    (this.length < GOOD_LEAF_SIZE && other.leafPrepend(this)) ||\n    this.appendInner(other)\n};\n\n// :: (union<[T], RopeSequence<T>>) → RopeSequence<T>\n// Prepend an array or other rope to this one, returning a new rope.\nRopeSequence.prototype.prepend = function prepend (other) {\n  if (!other.length) { return this }\n  return RopeSequence.from(other).append(this)\n};\n\nRopeSequence.prototype.appendInner = function appendInner (other) {\n  return new Append(this, other)\n};\n\n// :: (?number, ?number) → RopeSequence<T>\n// Create a rope repesenting a sub-sequence of this rope.\nRopeSequence.prototype.slice = function slice (from, to) {\n    if ( from === void 0 ) from = 0;\n    if ( to === void 0 ) to = this.length;\n\n  if (from >= to) { return RopeSequence.empty }\n  return this.sliceInner(Math.max(0, from), Math.min(this.length, to))\n};\n\n// :: (number) → T\n// Retrieve the element at the given position from this rope.\nRopeSequence.prototype.get = function get (i) {\n  if (i < 0 || i >= this.length) { return undefined }\n  return this.getInner(i)\n};\n\n// :: ((element: T, index: number) → ?bool, ?number, ?number)\n// Call the given function for each element between the given\n// indices. This tends to be more efficient than looping over the\n// indices and calling `get`, because it doesn't have to descend the\n// tree for every element.\nRopeSequence.prototype.forEach = function forEach (f, from, to) {\n    if ( from === void 0 ) from = 0;\n    if ( to === void 0 ) to = this.length;\n\n  if (from <= to)\n    { this.forEachInner(f, from, to, 0); }\n  else\n    { this.forEachInvertedInner(f, from, to, 0); }\n};\n\n// :: ((element: T, index: number) → U, ?number, ?number) → [U]\n// Map the given functions over the elements of the rope, producing\n// a flat array.\nRopeSequence.prototype.map = function map (f, from, to) {\n    if ( from === void 0 ) from = 0;\n    if ( to === void 0 ) to = this.length;\n\n  var result = [];\n  this.forEach(function (elt, i) { return result.push(f(elt, i)); }, from, to);\n  return result\n};\n\n// :: (?union<[T], RopeSequence<T>>) → RopeSequence<T>\n// Create a rope representing the given array, or return the rope\n// itself if a rope was given.\nRopeSequence.from = function from (values) {\n  if (values instanceof RopeSequence) { return values }\n  return values && values.length ? new Leaf(values) : RopeSequence.empty\n};\n\nvar Leaf = /*@__PURE__*/(function (RopeSequence) {\n  function Leaf(values) {\n    RopeSequence.call(this);\n    this.values = values;\n  }\n\n  if ( RopeSequence ) Leaf.__proto__ = RopeSequence;\n  Leaf.prototype = Object.create( RopeSequence && RopeSequence.prototype );\n  Leaf.prototype.constructor = Leaf;\n\n  var prototypeAccessors = { length: { configurable: true },depth: { configurable: true } };\n\n  Leaf.prototype.flatten = function flatten () {\n    return this.values\n  };\n\n  Leaf.prototype.sliceInner = function sliceInner (from, to) {\n    if (from == 0 && to == this.length) { return this }\n    return new Leaf(this.values.slice(from, to))\n  };\n\n  Leaf.prototype.getInner = function getInner (i) {\n    return this.values[i]\n  };\n\n  Leaf.prototype.forEachInner = function forEachInner (f, from, to, start) {\n    for (var i = from; i < to; i++)\n      { if (f(this.values[i], start + i) === false) { return false } }\n  };\n\n  Leaf.prototype.forEachInvertedInner = function forEachInvertedInner (f, from, to, start) {\n    for (var i = from - 1; i >= to; i--)\n      { if (f(this.values[i], start + i) === false) { return false } }\n  };\n\n  Leaf.prototype.leafAppend = function leafAppend (other) {\n    if (this.length + other.length <= GOOD_LEAF_SIZE)\n      { return new Leaf(this.values.concat(other.flatten())) }\n  };\n\n  Leaf.prototype.leafPrepend = function leafPrepend (other) {\n    if (this.length + other.length <= GOOD_LEAF_SIZE)\n      { return new Leaf(other.flatten().concat(this.values)) }\n  };\n\n  prototypeAccessors.length.get = function () { return this.values.length };\n\n  prototypeAccessors.depth.get = function () { return 0 };\n\n  Object.defineProperties( Leaf.prototype, prototypeAccessors );\n\n  return Leaf;\n}(RopeSequence));\n\n// :: RopeSequence\n// The empty rope sequence.\nRopeSequence.empty = new Leaf([]);\n\nvar Append = /*@__PURE__*/(function (RopeSequence) {\n  function Append(left, right) {\n    RopeSequence.call(this);\n    this.left = left;\n    this.right = right;\n    this.length = left.length + right.length;\n    this.depth = Math.max(left.depth, right.depth) + 1;\n  }\n\n  if ( RopeSequence ) Append.__proto__ = RopeSequence;\n  Append.prototype = Object.create( RopeSequence && RopeSequence.prototype );\n  Append.prototype.constructor = Append;\n\n  Append.prototype.flatten = function flatten () {\n    return this.left.flatten().concat(this.right.flatten())\n  };\n\n  Append.prototype.getInner = function getInner (i) {\n    return i < this.left.length ? this.left.get(i) : this.right.get(i - this.left.length)\n  };\n\n  Append.prototype.forEachInner = function forEachInner (f, from, to, start) {\n    var leftLen = this.left.length;\n    if (from < leftLen &&\n        this.left.forEachInner(f, from, Math.min(to, leftLen), start) === false)\n      { return false }\n    if (to > leftLen &&\n        this.right.forEachInner(f, Math.max(from - leftLen, 0), Math.min(this.length, to) - leftLen, start + leftLen) === false)\n      { return false }\n  };\n\n  Append.prototype.forEachInvertedInner = function forEachInvertedInner (f, from, to, start) {\n    var leftLen = this.left.length;\n    if (from > leftLen &&\n        this.right.forEachInvertedInner(f, from - leftLen, Math.max(to, leftLen) - leftLen, start + leftLen) === false)\n      { return false }\n    if (to < leftLen &&\n        this.left.forEachInvertedInner(f, Math.min(from, leftLen), to, start) === false)\n      { return false }\n  };\n\n  Append.prototype.sliceInner = function sliceInner (from, to) {\n    if (from == 0 && to == this.length) { return this }\n    var leftLen = this.left.length;\n    if (to <= leftLen) { return this.left.slice(from, to) }\n    if (from >= leftLen) { return this.right.slice(from - leftLen, to - leftLen) }\n    return this.left.slice(from, leftLen).append(this.right.slice(0, to - leftLen))\n  };\n\n  Append.prototype.leafAppend = function leafAppend (other) {\n    var inner = this.right.leafAppend(other);\n    if (inner) { return new Append(this.left, inner) }\n  };\n\n  Append.prototype.leafPrepend = function leafPrepend (other) {\n    var inner = this.left.leafPrepend(other);\n    if (inner) { return new Append(inner, this.right) }\n  };\n\n  Append.prototype.appendInner = function appendInner (other) {\n    if (this.left.depth >= Math.max(this.right.depth, other.depth) + 1)\n      { return new Append(this.left, new Append(this.right, other)) }\n    return new Append(this, other)\n  };\n\n  return Append;\n}(RopeSequence));\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RopeSequence);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rope-sequence/dist/index.js\n");

/***/ })

};
;