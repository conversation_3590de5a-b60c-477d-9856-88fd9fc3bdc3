# frozen_string_literal: true

module V1
  class AuthController < ApiController
    authorize_auth_token! :all, only: [:show]

    def authenticate
      email = auth_params[:email].to_s.downcase.strip

      authenticate_user = AuthenticateUser.new(email, auth_params[:password], auth_params[:organization_unique_id])
      auth_token = authenticate_user.call
      user = authenticate_user.auth_user
      organization_user = authenticate_user.organization_user
      organization = authenticate_user.organization
      user.update(last_login_at: Time.current)

      render_json auth_token,
                  ::V1::AuthOutput,
                  current_user: user,
                  organization_user: organization_user,
                  organization: organization
    end

    def show
      return render_error 'Not authorized', status: 401 unless current_user

    
      
      render_json auth_token,
                  ::V1::AuthOutput,
                  current_user: current_user,
                  organization_user: current_user_data[:organization_user],
                  organization: current_user_data[:organization]
    end

    private

    def auth_params
      params.permit(:email, :password, :organization_unique_id)
    end
  end
end
