import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";

export const deleteStakeholderMapping = ({
  accountId,
  id,
}: {
  id: number;
  accountId: number;
}): ApiResponse => {
  return api.delete(
    API_ROUTES.ACCOUNT_PLANS_STAKEHOLDER_MAPPING_DETAIL(accountId, id)
  );
};

type UseDeleteStakeholderMappingOptions = {
  mutationConfig?: MutationConfig<typeof deleteStakeholderMapping>;
};

export const useDeleteStakeholderMapping = ({
  mutationConfig,
}: UseDeleteStakeholderMappingOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      if (invalidate) {
        await queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS,
            args[1].accountId,
            QUERY_KEYS.ACCOUNT_PLANS_STAKEHOLDER_MAPPING,
          ],
        });
      }

      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_TARGETED_PERCEPTION_DEVELOPMENT,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteStakeholderMapping,
  });
};
