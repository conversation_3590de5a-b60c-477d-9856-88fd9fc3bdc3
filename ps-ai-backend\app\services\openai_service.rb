# frozen_string_literal: true
require 'tiktoken_ruby'

class OpenaiService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
    @client = OpenAI::Client.new(access_token: Rails.application.credentials.openai_key)
    @user_data = user_data
  end

  def generate_ap_table_response(params)    
    result = initialize_chat(params)
    stream_response(result)

    assistant_message = result.assistant_message_obj
    new_msg = reformat_json_output(assistant_message.content)

    assistant_message.update(content: new_msg)
    json_content = JSON.parse(new_msg)

    save_ap_table_generated_response(json_content, params[:ap_table_id])
  end
  
  def generate_considerations(params)
    result = initialize_chat(params)
    stream_response(result)

    assistant_message = result.assistant_message_obj
    new_msg = reformat_json_output(assistant_message.content)

    assistant_message.update(content: new_msg)
    json_content = JSON.parse(new_msg)

    OpenStruct.new(
      assistant_message: assistant_message,
      json_content: json_content
    )
  end

  def initialize_chat(params)
    chat_id = params[:chat_id]
    user_prompt = params[:query]
    message_type = 'general'

    if chat_id
      chat = ::Chat.find_by!(id: chat_id)
    else
      chat_service = ChatService.new(@user_data)
      chat = chat_service.create(params)
    end

    message_data = {
      chat_id: chat.id,
      sender: 'user',
      content: ''
    }
    user_messages = {}

    if message_type == 'image_and_file'
      message_file = create_file(file_url, 'assistants')
      attachments = [
        { file_id: message_file['id'], tools: [{ type: 'file_search' }] }
      ]

      user_messages = {
        role: 'user',
        content: [
          { type: 'text', text: user_prompt },
          { type: 'image_url', image_url: { url: image_url } }
        ],
        attachments: attachments
      }

      message_data[:content] = user_prompt
      message_data[:image_url] = image_url
      message_data[:file_url] = file_url
      message_data[:openai_file_id] = message_file['id']
    end

    if message_type == 'file_search'
      message_file = create_file(file_url, 'assistants')
      attachments = [
        { file_id: message_file['id'], tools: [{ type: 'file_search' }] }
      ]

      user_messages = { role: 'user', content: user_prompt, attachments: attachments }

      message_data[:content] = user_prompt
      message_data[:file_url] = file_url
      message_data[:openai_file_id] = message_file['id']
    end

    if message_type == 'image'
      user_messages = {
        role: 'user',
        content: [
          { type: 'text', text: user_prompt },
          { type: 'image_url', image_url: { url: image_url } }
        ]
      }

      message_data[:content] = user_prompt
      message_data[:image_url] = image_url
    end

    if message_type == 'general'
      user_messages = { role: 'user', content: user_prompt }
      message_data[:content] = user_prompt
    end

    openai_file_id = message_data.delete(:openai_file_id)
    user_message_obj = ::Message.create!(message_data)
    if openai_file_id.present?
      OpenaiFile.create!(
        object_id: user_message_obj.id,
        object_class: user_message_obj.class.name,
        object_class_column: 'file_url',
        openai_file_id: openai_file_id
      )
    end

    assistant_message_obj = ::Message.new(
      chat_id: chat.id,
      sender: 'assistant',
      content: ''
    )

    OpenStruct.new(
      chat: chat,
      model: chat.model,
      user_messages: user_messages,
      assistant_message_obj: assistant_message_obj,
      user_message_obj: user_message_obj
    )
  end

  def stream_response(params)
    chat = params.chat
    model = params.model
    user_messages = params.user_messages
    assistant_message_obj = params.assistant_message_obj
    user_message_obj = params.user_message_obj
    openai_assistant_id = model.openai_assistant_id
    openai_thread_id = chat.openai_thread_id

    if openai_thread_id
      thread_id = openai_thread_id

      message_response = @client.messages.create(
        thread_id: thread_id,
        parameters: user_messages
      )

      run_response = @client.runs.create(
        thread_id: thread_id,
        parameters: {
          assistant_id: openai_assistant_id,
          stream: proc do |chunk, _bytesize|
            if chunk["object"] == "thread.message.delta"
              print chunk.dig("delta", "content", 0, "text", "value")
              assistant_message_obj.content = assistant_message_obj.content + chunk.dig("delta", "content", 0, "text", "value").to_s
            end

            if chunk["object"] == "thread.run" && chunk["status"] == "completed"
              run_id = chunk["id"]
              usage_data = chunk["usage"]
              print("\n", usage_data, "\n")
              
              user_message_obj.tokens_used = usage_data["prompt_tokens"]
              assistant_message_obj.tokens_used = usage_data["completion_tokens"]
            end
          end
        }
      )

      user_message_obj.openai_thread_id = thread_id
      assistant_message_obj.openai_thread_id = thread_id

      user_message_obj.save!
      assistant_message_obj.save!
    else
      thread_id = @client.threads.create['id']

      user_message_obj.openai_thread_id = thread_id
      assistant_message_obj.openai_thread_id = thread_id

      message_response = @client.messages.create(
        thread_id: thread_id,
        parameters: user_messages
      )

      run_response = @client.runs.create(
        thread_id: thread_id,
        parameters: {
          assistant_id: openai_assistant_id,
          stream: proc do |chunk, _bytesize|
            if chunk["object"] == "thread.message.delta"
              print chunk.dig("delta", "content", 0, "text", "value")
              assistant_message_obj.content = assistant_message_obj.content + chunk.dig("delta", "content", 0, "text", "value").to_s
            end

            if chunk["object"] == "thread.run" && chunk["status"] == "completed"
              run_id = chunk["id"]
              usage_data = chunk["usage"]
              print("\n", usage_data, "\n")
              
              user_message_obj.tokens_used = usage_data["prompt_tokens"]
              assistant_message_obj.tokens_used = usage_data["completion_tokens"]
            end
          end
        }
      )


      user_message_obj.openai_thread_id = thread_id
      assistant_message_obj.openai_thread_id = thread_id
      chat.openai_thread_id = thread_id

      user_message_obj.save!
      assistant_message_obj.save!
      chat.save!
    end
  end

  """
  ================================================
  OpenAI Assistant general functions
  # TODO: Error Handling
  ================================================
  """
  def create_assistant(params, **options)
    tools = assistant_tools_builder(params)
    
    parameters = {
      model: params[:model],
      name: params[:name],
      temperature: params[:temperature],
      description: '',
      instructions: params[:instructions],
      response_format: {
        type: params[:response_format_type] || 'json_object'
      }
    }

    if options[:tool_resources].present?
      parameters[:tool_resources] = options[:tool_resources]
    end

    response = @client.assistants.create(
      parameters: parameters  
    )

    response
  end

  def modify_assistant(params, **options)
    tools = assistant_tools_builder(params)
    assistant_id = params[:openai_assistant_id]

    parameters = {
      model: params[:model],
      name: params[:name],
      temperature: params[:temperature],
      description: '',
      instructions: params[:instructions],
      response_format: {
        type: params[:response_format_type] || 'json_object'
      }
    }

    if options[:tool_resources].present?
      parameters[:tool_resources] = options[:tool_resources]
    end

    response = @client.assistants.modify(
      id: assistant_id,
      parameters: parameters
    )

    response
  end

  def retrieve_assistant(assistant_id)
    @client.assistants.retrieve(id: assistant_id)
  end

  def delete_assistant(assistant_id)
    @client.assistants.delete(id: assistant_id)
  end

  def create_assistant_files(vector_store_id, **options)
    mode = options[:mode]

    return unless vector_store_id.present? && ['openai_file_ids', 'file_urls'].include?(mode)

    # 
    openai_file_ids = []
    purpose = 'assistants'

    if mode == 'file_urls'
      file_urls = options[:file_urls]
      model = options[:model]
      file_urls.each do |url|
        response_file = create_file(url, purpose)
        openai_file_ids << response_file['id']

        OpenaiFile.create!(
          object_id: model.id,
          object_class: model.class.name,
          object_class_column: 'openai_assistant_id',
          openai_file_id: response_file['id']
        )
      end
    elsif mode == 'openai_file_ids'
      openai_file_ids = options[:openai_file_ids]
    end

    @client.vector_store_file_batches.create(
      vector_store_id: vector_store_id,
      parameters: {
        file_ids: openai_file_ids
      }
    )
  end

  """
  ================================================
  OpenAI file & vector store general functions
  ================================================
  """
  def create_vector_store(name)
    response = @client.vector_stores.create(
      parameters: {
        name: name
      }
    )

    response
  end

  def delete_vector_store(vector_store_id)
    response_vector_store_files = list_vector_store_files(vector_store_id)
    openai_file_ids = response_vector_store_files['data'].collect { |file| file['id'] }

    openai_file_ids.each do |f_id|
      delete_file(f_id)
    end

    @client.vector_stores.delete(id: vector_store_id)
  end

  def list_vector_store_files(vector_store_id)
    @client.vector_store_files.list(vector_store_id: vector_store_id)
  end

  def create_file(file_url, purpose)
    filename = File.basename(URI.parse(file_url).path)
    root_filepath = Rails.root.join('tmp', filename)
    File.open(root_filepath, 'wb') do |fo|
      fo.write(URI.open(file_url).read)
    end

    open_file = File.open(root_filepath)

    response = @client.files.upload(parameters: { file: open_file, purpose: purpose})

    File.delete(root_filepath)

    response
  end

  def delete_file(openai_file_id)
    @client.files.delete(id: openai_file_id)
  end

  private

  def assistant_tools_builder(params)
    tools = []

    if params[:file_search]
      tools << { type: "file_search" }
    end

    if params[:code_interpreter]
      tools << { type: "code_interpreter" }
    end

    tools
  end

  def save_ap_table_generated_response(json_content, ap_table_id)
    data = json_content['data']

    ap_table = ApTable.find_by(id: ap_table_id)
    table_type = ap_table.table_type
  
    if table_type == 'circumstantial_analysis'
      # ::AccountPlanTableItems::ApCircumstantialAnalysisItem.where(ap_table_id: ap_table_id).discard_all

      ::AccountPlanTableItems::ApCircumstantialAnalysisItem.item_types.values.each do |k|
        mod_k = "#{k}_analysis"
        if data.has_key?(mod_k)
          create_params = {
            ap_table_id: ap_table_id,
            item_type: k,
            description: data[mod_k]
          }

          ::AccountPlanTableItems::ApCircumstantialAnalysisItem.create!(create_params)
        end
      end
    elsif table_type == 'top_action'
      ::AccountPlanTableItems::ApTopActionItem.where(ap_table_id: ap_table_id).discard_all

      data.each do |d|
        create_params = {
          ap_table_id: ap_table_id,
          description: d['action'],
          order: d['priority'],
          action_target: d['action_target']
        }

        ::AccountPlanTableItems::ApTopActionItem.create!(create_params)
      end
    # elsif table_type == 'action_plan'
      # ::AccountPlanTableItems::ApActionPlanItem.where(ap_table_id: ap_table_id).discard_all

      # create_params = {
      #   ap_table_id: ap_table_id,
      #   description: data['action']
      # }

      # ::AccountPlanTableItems::ApActionPlanItem.create!(create_params)
    # elsif table_type == 'missing_information'
    #   ::AccountPlanTableItems::ApMissingInformationItem.where(ap_table_id: ap_table_id).discard_all

    #   create_params = {
    #     ap_table_id: ap_table_id,
    #     description: data['result']
    #   }

    #   ::AccountPlanTableItems::ApMissingInformationItem.create!(create_params)
    elsif table_type == 'targeted_perception_development'
      ::AccountPlanTableItems::ApTargetedPerceptionDevelopmentItem.where(ap_table_id: ap_table_id).discard_all

      data.each do |d|
        create_params = {
          ap_table_id: ap_table_id,
          ap_stakeholder_mapping_item_id: d['stakeholder_mapping_item_id'],
          action: d['action'],
          result: d['result'],
          leverage: d['leverage']
        }

        ::AccountPlanTableItems::ApTargetedPerceptionDevelopmentItem.create!(create_params)
      end
    elsif table_type == 'insight_and_perspective'
      ::AccountPlanTableItems::ApInsightAndPerspectiveItem.where(ap_table_id: ap_table_id).discard_all

      data.each do |d|
        create_params = {
          ap_table_id: ap_table_id,
          description: d['description'],
          target_name: d['target_name']
        }

        ::AccountPlanTableItems::ApInsightAndPerspectiveItem.create!(create_params)
      end
    end
    
  end
  
  def reformat_json_output(str)
    new_msg = str.gsub('```json', '')
    first_curl_bracket_index = new_msg.index('{')
    new_msg = new_msg[first_curl_bracket_index..]
    last_triple_notes = new_msg.index('```')

    if !last_triple_notes.nil?
      new_msg = new_msg[..(last_triple_notes - 1)]
    end

    new_msg
  end
end
