# frozen_string_literal: true

class AuthenticateUser
  attr_reader :auth_user, :organization_user, :organization

  def initialize(email, password, organization_unique_id, auth_user = nil, _referer = nil)
    @email = email
    @password = password
    @organization_unique_id = organization_unique_id
    @auth_user = auth_user
  end

  def call
    curr_user = user
    return unless curr_user

    payload = { user_id: curr_user.id, organization_id: @organization.id, organization_user_id: @organization_user.id }

    @auth_user = curr_user
    JsonWebToken.encode(payload, 10.years)
  end

  private

  attr_reader :email, :password, :organization_unique_id

  def user
    user = User.find_by(email:)
    @organization = Organization.find_by(unique_id: organization_unique_id.to_s)

    raise ExceptionHandler::AuthenticationError, ErrorMessage.invalid_credentials unless user && @organization

    @organization_user = OrganizationUser.find_by(user_id: user.id, organization_id: @organization.id, status: 'active')
    raise ExceptionHandler::AuthenticationError, ErrorMessage.invalid_credentials unless @organization_user

    if user.authenticate(password)
      return user
    end

    raise(ExceptionHandler::AuthenticationError, ErrorMessage.invalid_credentials)
  end
end
