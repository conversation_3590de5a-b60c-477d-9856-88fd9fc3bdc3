# frozen_string_literal: true

class ModelTemplateVariables < ::ApplicationRepository
  def default_scope
    ::ModelTemplateVariable.all
  end

  def filter_by_organization_id(organization_id)
    @scope.joins(:model_template)
          .where(model_templates: { organization_id: organization_id })
  end

  def filter_by_model_template_id(model_template_id)
    @scope.where(model_template_id: model_template_id)
  end

  def filter_by_search(search)
    @scope.where('model_template_variables.name ilike ?', "%#{search}%")
  end
end
