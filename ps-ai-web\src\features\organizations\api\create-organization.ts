import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { OrganizationData, OrganizationTier } from "../types";
import { toast } from "sonner";

export type OrganizationCreatePayload = {
  name: string;
  unique_id?: string;
  tier?: OrganizationTier;
  designated_owner_email?: string;
  organization_identifier_id?: string;
};

export const createOrganization = ({
  data,
}: {
  data?: OrganizationCreatePayload;
}): ApiResponse<OrganizationData> => {
  return api.post(`${API_ROUTES.ORGANIZATIONS}`, data);
};

type UseCreateOrganizationOptions = {
  mutationConfig?: MutationConfig<typeof createOrganization>;
};

export const useCreateOrganization = ({
  mutationConfig,
}: UseCreateOrganizationOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);

      toast("A new organization has been successfully created");

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ORGANIZATION],
      });
    },
    ...restConfig,
    mutationFn: createOrganization,
  });
};
