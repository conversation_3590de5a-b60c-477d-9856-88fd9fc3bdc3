"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tiptap-markdown";
exports.ids = ["vendor-chunks/tiptap-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/tiptap-markdown/dist/tiptap-markdown.es.js":
/*!*****************************************************************!*\
  !*** ./node_modules/tiptap-markdown/dist/tiptap-markdown.es.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Markdown: () => (/* binding */ Markdown)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! prosemirror-markdown */ \"(ssr)/./node_modules/prosemirror-markdown/dist/index.js\");\n/* harmony import */ var markdown_it__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! markdown-it */ \"(ssr)/./node_modules/markdown-it/index.mjs\");\n/* harmony import */ var _tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/pm/model */ \"(ssr)/./node_modules/@tiptap/pm/model/dist/index.js\");\n/* harmony import */ var markdown_it_task_lists__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! markdown-it-task-lists */ \"(ssr)/./node_modules/markdown-it-task-lists/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/@tiptap/pm/state/dist/index.js\");\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\n\n\n\n\n\n\nconst MarkdownTightLists = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Extension.create({\n  name: \"markdownTightLists\",\n  addOptions: () => ({\n    tight: true,\n    tightClass: \"tight\",\n    listTypes: [\"bulletList\", \"orderedList\"]\n  }),\n  addGlobalAttributes() {\n    return [{\n      types: this.options.listTypes,\n      attributes: {\n        tight: {\n          default: this.options.tight,\n          parseHTML: (element) => element.getAttribute(\"data-tight\") === \"true\" || !element.querySelector(\"p\"),\n          renderHTML: (attributes) => ({\n            class: attributes.tight ? this.options.tightClass : null,\n            \"data-tight\": attributes.tight ? \"true\" : null\n          })\n        }\n      }\n    }];\n  },\n  addCommands() {\n    var _this = this;\n    return {\n      toggleTight: function() {\n        let tight = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : null;\n        return (_ref) => {\n          let {\n            editor,\n            commands\n          } = _ref;\n          function toggleTight(name) {\n            if (!editor.isActive(name)) {\n              return false;\n            }\n            const attrs = editor.getAttributes(name);\n            return commands.updateAttributes(name, {\n              tight: tight !== null && tight !== void 0 ? tight : !(attrs !== null && attrs !== void 0 && attrs.tight)\n            });\n          }\n          return _this.options.listTypes.some((name) => toggleTight(name));\n        };\n      }\n    };\n  }\n});\nconst md = (0,markdown_it__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\nfunction scanDelims(text, pos) {\n  md.inline.State.prototype.scanDelims.call({\n    src: text,\n    posMax: text.length\n  });\n  const state = new md.inline.State(text, null, null, []);\n  return state.scanDelims(pos, true);\n}\nfunction shiftDelim(text, delim, start, offset) {\n  let res = text.substring(0, start) + text.substring(start + delim.length);\n  res = res.substring(0, start + offset) + delim + res.substring(start + offset);\n  return res;\n}\nfunction trimStart(text, delim, from, to) {\n  let pos = from, res = text;\n  while (pos < to) {\n    if (scanDelims(res, pos).can_open) {\n      break;\n    }\n    res = shiftDelim(res, delim, pos, 1);\n    pos++;\n  }\n  return {\n    text: res,\n    from: pos,\n    to\n  };\n}\nfunction trimEnd(text, delim, from, to) {\n  let pos = to, res = text;\n  while (pos > from) {\n    if (scanDelims(res, pos).can_close) {\n      break;\n    }\n    res = shiftDelim(res, delim, pos, -1);\n    pos--;\n  }\n  return {\n    text: res,\n    from,\n    to: pos\n  };\n}\nfunction trimInline(text, delim, from, to) {\n  let state = {\n    text,\n    from,\n    to\n  };\n  state = trimStart(state.text, delim, state.from, state.to);\n  state = trimEnd(state.text, delim, state.from, state.to);\n  if (state.to - state.from < delim.length + 1) {\n    state.text = state.text.substring(0, state.from) + state.text.substring(state.to + delim.length);\n  }\n  return state.text;\n}\nclass MarkdownSerializerState extends prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.MarkdownSerializerState {\n  constructor(nodes, marks, options) {\n    super(nodes, marks, options !== null && options !== void 0 ? options : {});\n    __publicField(this, \"inTable\", false);\n    this.inlines = [];\n  }\n  render(node, parent, index) {\n    super.render(node, parent, index);\n    const top = this.inlines[this.inlines.length - 1];\n    if (top !== null && top !== void 0 && top.start && top !== null && top !== void 0 && top.end) {\n      const {\n        delimiter,\n        start,\n        end\n      } = this.normalizeInline(top);\n      this.out = trimInline(this.out, delimiter, start, end);\n      this.inlines.pop();\n    }\n  }\n  markString(mark, open, parent, index) {\n    const info = this.marks[mark.type.name];\n    if (info.expelEnclosingWhitespace) {\n      if (open) {\n        this.inlines.push({\n          start: this.out.length,\n          delimiter: info.open\n        });\n      } else {\n        const top = this.inlines.pop();\n        this.inlines.push({\n          ...top,\n          end: this.out.length\n        });\n      }\n    }\n    return super.markString(mark, open, parent, index);\n  }\n  normalizeInline(inline) {\n    let {\n      start,\n      end\n    } = inline;\n    while (this.out.charAt(start).match(/\\s/)) {\n      start++;\n    }\n    return {\n      ...inline,\n      start\n    };\n  }\n}\nconst HTMLMark = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Mark.create({\n  name: \"markdownHTMLMark\",\n  /**\n   * @return {{markdown: MarkdownMarkSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: {\n          open(state, mark) {\n            var _getMarkTags$, _getMarkTags;\n            if (!this.editor.storage.markdown.options.html) {\n              console.warn(`Tiptap Markdown: \"${mark.type.name}\" mark is only available in html mode`);\n              return \"\";\n            }\n            return (_getMarkTags$ = (_getMarkTags = getMarkTags(mark)) === null || _getMarkTags === void 0 ? void 0 : _getMarkTags[0]) !== null && _getMarkTags$ !== void 0 ? _getMarkTags$ : \"\";\n          },\n          close(state, mark) {\n            var _getMarkTags$2, _getMarkTags2;\n            if (!this.editor.storage.markdown.options.html) {\n              return \"\";\n            }\n            return (_getMarkTags$2 = (_getMarkTags2 = getMarkTags(mark)) === null || _getMarkTags2 === void 0 ? void 0 : _getMarkTags2[1]) !== null && _getMarkTags$2 !== void 0 ? _getMarkTags$2 : \"\";\n          }\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nfunction getMarkTags(mark) {\n  const schema = mark.type.schema;\n  const node = schema.text(\" \", [mark]);\n  const html = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_4__.getHTMLFromFragment)(_tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(node), schema);\n  const match = html.match(/^(<.*?>) (<\\/.*?>)$/);\n  return match ? [match[1], match[2]] : null;\n}\nfunction elementFromString(value) {\n  const wrappedValue = `<body>${value}</body>`;\n  return new window.DOMParser().parseFromString(wrappedValue, \"text/html\").body;\n}\nfunction escapeHTML(value) {\n  return value === null || value === void 0 ? void 0 : value.replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n}\nfunction extractElement(node) {\n  const parent = node.parentElement;\n  const prepend = parent.cloneNode();\n  while (parent.firstChild && parent.firstChild !== node) {\n    prepend.appendChild(parent.firstChild);\n  }\n  if (prepend.childNodes.length > 0) {\n    parent.parentElement.insertBefore(prepend, parent);\n  }\n  parent.parentElement.insertBefore(node, parent);\n  if (parent.childNodes.length === 0) {\n    parent.remove();\n  }\n}\nfunction unwrapElement(node) {\n  const parent = node.parentNode;\n  while (node.firstChild)\n    parent.insertBefore(node.firstChild, node);\n  parent.removeChild(node);\n}\nconst HTMLNode = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"markdownHTMLNode\",\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node, parent) {\n          if (this.editor.storage.markdown.options.html) {\n            state.write(serializeHTML(node, parent));\n          } else {\n            console.warn(`Tiptap Markdown: \"${node.type.name}\" node is only available in html mode`);\n            state.write(`[${node.type.name}]`);\n          }\n          if (node.isBlock) {\n            state.closeBlock(node);\n          }\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nfunction serializeHTML(node, parent) {\n  const schema = node.type.schema;\n  const html = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_4__.getHTMLFromFragment)(_tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(node), schema);\n  if (node.isBlock && (parent instanceof _tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__.Fragment || parent.type.name === schema.topNodeType.name)) {\n    return formatBlock(html);\n  }\n  return html;\n}\nfunction formatBlock(html) {\n  const dom = elementFromString(html);\n  const element = dom.firstElementChild;\n  element.innerHTML = element.innerHTML.trim() ? `\n${element.innerHTML}\n` : `\n`;\n  return element.outerHTML;\n}\nconst Blockquote = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"blockquote\"\n});\nconst Blockquote$1 = Blockquote.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.nodes.blockquote,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst BulletList = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"bulletList\"\n});\nconst BulletList$1 = BulletList.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node) {\n          return state.renderList(node, \"  \", () => (this.editor.storage.markdown.options.bulletListMarker || \"-\") + \" \");\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst CodeBlock = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"codeBlock\"\n});\nconst CodeBlock$1 = CodeBlock.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node) {\n          state.write(\"```\" + (node.attrs.language || \"\") + \"\\n\");\n          state.text(node.textContent, false);\n          state.ensureNewLine();\n          state.write(\"```\");\n          state.closeBlock(node);\n        },\n        parse: {\n          setup(markdownit2) {\n            var _this$options$languag;\n            markdownit2.set({\n              langPrefix: (_this$options$languag = this.options.languageClassPrefix) !== null && _this$options$languag !== void 0 ? _this$options$languag : \"language-\"\n            });\n          },\n          updateDOM(element) {\n            element.innerHTML = element.innerHTML.replace(/\\n<\\/code><\\/pre>/g, \"</code></pre>\");\n          }\n        }\n      }\n    };\n  }\n});\nconst HardBreak = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"hardBreak\"\n});\nconst HardBreak$1 = HardBreak.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node, parent, index) {\n          for (let i = index + 1; i < parent.childCount; i++)\n            if (parent.child(i).type != node.type) {\n              state.write(state.inTable ? HTMLNode.storage.markdown.serialize.call(this, state, node, parent) : \"\\\\\\n\");\n              return;\n            }\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Heading = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"heading\"\n});\nconst Heading$1 = Heading.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.nodes.heading,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst HorizontalRule = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"horizontalRule\"\n});\nconst HorizontalRule$1 = HorizontalRule.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.nodes.horizontal_rule,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Image = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"image\"\n});\nconst Image$1 = Image.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.nodes.image,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst ListItem = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"listItem\"\n});\nconst ListItem$1 = ListItem.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.nodes.list_item,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst OrderedList = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"orderedList\"\n});\nfunction findIndexOfAdjacentNode(node, parent, index) {\n  let i = 0;\n  for (; index - i > 0; i++) {\n    if (parent.child(index - i - 1).type.name !== node.type.name) {\n      break;\n    }\n  }\n  return i;\n}\nconst OrderedList$1 = OrderedList.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node, parent, index) {\n          const start = node.attrs.start || 1;\n          const maxW = String(start + node.childCount - 1).length;\n          const space = state.repeat(\" \", maxW + 2);\n          const adjacentIndex = findIndexOfAdjacentNode(node, parent, index);\n          const separator = adjacentIndex % 2 ? \") \" : \". \";\n          state.renderList(node, space, (i) => {\n            const nStr = String(start + i);\n            return state.repeat(\" \", maxW - nStr.length) + nStr + separator;\n          });\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Paragraph = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"paragraph\"\n});\nconst Paragraph$1 = Paragraph.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.nodes.paragraph,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nfunction childNodes(node) {\n  var _node$content$content, _node$content;\n  return (_node$content$content = node === null || node === void 0 || (_node$content = node.content) === null || _node$content === void 0 ? void 0 : _node$content.content) !== null && _node$content$content !== void 0 ? _node$content$content : [];\n}\nconst Table = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"table\"\n});\nconst Table$1 = Table.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node, parent) {\n          if (!isMarkdownSerializable(node)) {\n            HTMLNode.storage.markdown.serialize.call(this, state, node, parent);\n            return;\n          }\n          state.inTable = true;\n          node.forEach((row, p, i) => {\n            state.write(\"| \");\n            row.forEach((col, p2, j) => {\n              if (j) {\n                state.write(\" | \");\n              }\n              const cellContent = col.firstChild;\n              if (cellContent.textContent.trim()) {\n                state.renderInline(cellContent);\n              }\n            });\n            state.write(\" |\");\n            state.ensureNewLine();\n            if (!i) {\n              const delimiterRow = Array.from({\n                length: row.childCount\n              }).map(() => \"---\").join(\" | \");\n              state.write(`| ${delimiterRow} |`);\n              state.ensureNewLine();\n            }\n          });\n          state.closeBlock(node);\n          state.inTable = false;\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nfunction hasSpan(node) {\n  return node.attrs.colspan > 1 || node.attrs.rowspan > 1;\n}\nfunction isMarkdownSerializable(node) {\n  const rows = childNodes(node);\n  const firstRow = rows[0];\n  const bodyRows = rows.slice(1);\n  if (childNodes(firstRow).some((cell) => cell.type.name !== \"tableHeader\" || hasSpan(cell) || cell.childCount > 1)) {\n    return false;\n  }\n  if (bodyRows.some((row) => childNodes(row).some((cell) => cell.type.name === \"tableHeader\" || hasSpan(cell) || cell.childCount > 1))) {\n    return false;\n  }\n  return true;\n}\nconst TaskItem = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"taskItem\"\n});\nconst TaskItem$1 = TaskItem.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node) {\n          const check = node.attrs.checked ? \"[x]\" : \"[ ]\";\n          state.write(`${check} `);\n          state.renderContent(node);\n        },\n        parse: {\n          updateDOM(element) {\n            [...element.querySelectorAll(\".task-list-item\")].forEach((item) => {\n              const input = item.querySelector(\"input\");\n              item.setAttribute(\"data-type\", \"taskItem\");\n              if (input) {\n                item.setAttribute(\"data-checked\", input.checked);\n                input.remove();\n              }\n            });\n          }\n        }\n      }\n    };\n  }\n});\nconst TaskList = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"taskList\"\n});\nconst TaskList$1 = TaskList.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: BulletList$1.storage.markdown.serialize,\n        parse: {\n          setup(markdownit2) {\n            markdownit2.use(markdown_it_task_lists__WEBPACK_IMPORTED_MODULE_2__);\n          },\n          updateDOM(element) {\n            [...element.querySelectorAll(\".contains-task-list\")].forEach((list) => {\n              list.setAttribute(\"data-type\", \"taskList\");\n            });\n          }\n        }\n      }\n    };\n  }\n});\nconst Text = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Node.create({\n  name: \"text\"\n});\nconst Text$1 = Text.extend({\n  /**\n   * @return {{markdown: MarkdownNodeSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize(state, node) {\n          state.text(escapeHTML(node.text));\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Bold = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Mark.create({\n  name: \"bold\"\n});\nconst Bold$1 = Bold.extend({\n  /**\n   * @return {{markdown: MarkdownMarkSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.marks.strong,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Code = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Mark.create({\n  name: \"code\"\n});\nconst Code$1 = Code.extend({\n  /**\n   * @return {{markdown: MarkdownMarkSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.marks.code,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Italic = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Mark.create({\n  name: \"italic\"\n});\nconst Italic$1 = Italic.extend({\n  /**\n   * @return {{markdown: MarkdownMarkSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.marks.em,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Link = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Mark.create({\n  name: \"link\"\n});\nconst Link$1 = Link.extend({\n  /**\n   * @return {{markdown: MarkdownMarkSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: prosemirror_markdown__WEBPACK_IMPORTED_MODULE_5__.defaultMarkdownSerializer.marks.link,\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst Strike = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Mark.create({\n  name: \"strike\"\n});\nconst Strike$1 = Strike.extend({\n  /**\n   * @return {{markdown: MarkdownMarkSpec}}\n   */\n  addStorage() {\n    return {\n      markdown: {\n        serialize: {\n          open: \"~~\",\n          close: \"~~\",\n          expelEnclosingWhitespace: true\n        },\n        parse: {\n          // handled by markdown-it\n        }\n      }\n    };\n  }\n});\nconst markdownExtensions = [Blockquote$1, BulletList$1, CodeBlock$1, HardBreak$1, Heading$1, HorizontalRule$1, HTMLNode, Image$1, ListItem$1, OrderedList$1, Paragraph$1, Table$1, TaskItem$1, TaskList$1, Text$1, Bold$1, Code$1, HTMLMark, Italic$1, Link$1, Strike$1];\nfunction getMarkdownSpec(extension) {\n  var _extension$storage, _markdownExtensions$f;\n  const markdownSpec = (_extension$storage = extension.storage) === null || _extension$storage === void 0 ? void 0 : _extension$storage.markdown;\n  const defaultMarkdownSpec = (_markdownExtensions$f = markdownExtensions.find((e) => e.name === extension.name)) === null || _markdownExtensions$f === void 0 ? void 0 : _markdownExtensions$f.storage.markdown;\n  if (markdownSpec || defaultMarkdownSpec) {\n    return {\n      ...defaultMarkdownSpec,\n      ...markdownSpec\n    };\n  }\n  return null;\n}\nclass MarkdownSerializer {\n  constructor(editor) {\n    /**\n     * @type {import('@tiptap/core').Editor}\n     */\n    __publicField(this, \"editor\", null);\n    this.editor = editor;\n  }\n  serialize(content) {\n    const state = new MarkdownSerializerState(this.nodes, this.marks, {\n      hardBreakNodeName: HardBreak$1.name\n    });\n    state.renderContent(content);\n    return state.out;\n  }\n  get nodes() {\n    var _this$editor$extensio;\n    return {\n      ...Object.fromEntries(Object.keys(this.editor.schema.nodes).map((name) => [name, this.serializeNode(HTMLNode)])),\n      ...Object.fromEntries((_this$editor$extensio = this.editor.extensionManager.extensions.filter((extension) => extension.type === \"node\" && this.serializeNode(extension)).map((extension) => [extension.name, this.serializeNode(extension)])) !== null && _this$editor$extensio !== void 0 ? _this$editor$extensio : [])\n    };\n  }\n  get marks() {\n    var _this$editor$extensio2;\n    return {\n      ...Object.fromEntries(Object.keys(this.editor.schema.marks).map((name) => [name, this.serializeMark(HTMLMark)])),\n      ...Object.fromEntries((_this$editor$extensio2 = this.editor.extensionManager.extensions.filter((extension) => extension.type === \"mark\" && this.serializeMark(extension)).map((extension) => [extension.name, this.serializeMark(extension)])) !== null && _this$editor$extensio2 !== void 0 ? _this$editor$extensio2 : [])\n    };\n  }\n  serializeNode(node) {\n    var _getMarkdownSpec;\n    return (_getMarkdownSpec = getMarkdownSpec(node)) === null || _getMarkdownSpec === void 0 || (_getMarkdownSpec = _getMarkdownSpec.serialize) === null || _getMarkdownSpec === void 0 ? void 0 : _getMarkdownSpec.bind({\n      editor: this.editor,\n      options: node.options\n    });\n  }\n  serializeMark(mark) {\n    var _getMarkdownSpec2;\n    const serialize = (_getMarkdownSpec2 = getMarkdownSpec(mark)) === null || _getMarkdownSpec2 === void 0 ? void 0 : _getMarkdownSpec2.serialize;\n    return serialize ? {\n      ...serialize,\n      open: typeof serialize.open === \"function\" ? serialize.open.bind({\n        editor: this.editor,\n        options: mark.options\n      }) : serialize.open,\n      close: typeof serialize.close === \"function\" ? serialize.close.bind({\n        editor: this.editor,\n        options: mark.options\n      }) : serialize.close\n    } : null;\n  }\n}\nclass MarkdownParser {\n  constructor(editor, _ref) {\n    /**\n     * @type {import('@tiptap/core').Editor}\n     */\n    __publicField(this, \"editor\", null);\n    /**\n     * @type {markdownit}\n     */\n    __publicField(this, \"md\", null);\n    let {\n      html,\n      linkify,\n      breaks\n    } = _ref;\n    this.editor = editor;\n    this.md = this.withPatchedRenderer((0,markdown_it__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      html,\n      linkify,\n      breaks\n    }));\n  }\n  parse(content) {\n    let {\n      inline\n    } = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    if (typeof content === \"string\") {\n      this.editor.extensionManager.extensions.forEach((extension) => {\n        var _getMarkdownSpec;\n        return (_getMarkdownSpec = getMarkdownSpec(extension)) === null || _getMarkdownSpec === void 0 || (_getMarkdownSpec = _getMarkdownSpec.parse) === null || _getMarkdownSpec === void 0 || (_getMarkdownSpec = _getMarkdownSpec.setup) === null || _getMarkdownSpec === void 0 ? void 0 : _getMarkdownSpec.call({\n          editor: this.editor,\n          options: extension.options\n        }, this.md);\n      });\n      const renderedHTML = this.md.render(content);\n      const element = elementFromString(renderedHTML);\n      this.editor.extensionManager.extensions.forEach((extension) => {\n        var _getMarkdownSpec2;\n        return (_getMarkdownSpec2 = getMarkdownSpec(extension)) === null || _getMarkdownSpec2 === void 0 || (_getMarkdownSpec2 = _getMarkdownSpec2.parse) === null || _getMarkdownSpec2 === void 0 || (_getMarkdownSpec2 = _getMarkdownSpec2.updateDOM) === null || _getMarkdownSpec2 === void 0 ? void 0 : _getMarkdownSpec2.call({\n          editor: this.editor,\n          options: extension.options\n        }, element);\n      });\n      this.normalizeDOM(element, {\n        inline,\n        content\n      });\n      return element.innerHTML;\n    }\n    return content;\n  }\n  normalizeDOM(node, _ref2) {\n    let {\n      inline,\n      content\n    } = _ref2;\n    this.normalizeBlocks(node);\n    node.querySelectorAll(\"*\").forEach((el) => {\n      var _el$nextSibling;\n      if (((_el$nextSibling = el.nextSibling) === null || _el$nextSibling === void 0 ? void 0 : _el$nextSibling.nodeType) === Node.TEXT_NODE && !el.closest(\"pre\")) {\n        el.nextSibling.textContent = el.nextSibling.textContent.replace(/^\\n/, \"\");\n      }\n    });\n    if (inline) {\n      this.normalizeInline(node, content);\n    }\n    return node;\n  }\n  normalizeBlocks(node) {\n    const blocks = Object.values(this.editor.schema.nodes).filter((node2) => node2.isBlock);\n    const selector = blocks.map((block) => {\n      var _block$spec$parseDOM;\n      return (_block$spec$parseDOM = block.spec.parseDOM) === null || _block$spec$parseDOM === void 0 ? void 0 : _block$spec$parseDOM.map((spec) => spec.tag);\n    }).flat().filter(Boolean).join(\",\");\n    if (!selector) {\n      return;\n    }\n    [...node.querySelectorAll(selector)].forEach((el) => {\n      if (el.parentElement.matches(\"p\")) {\n        extractElement(el);\n      }\n    });\n  }\n  normalizeInline(node, content) {\n    var _node$firstElementChi;\n    if ((_node$firstElementChi = node.firstElementChild) !== null && _node$firstElementChi !== void 0 && _node$firstElementChi.matches(\"p\")) {\n      var _content$match$, _content$match, _content$match$2, _content$match2;\n      const firstParagraph = node.firstElementChild;\n      const {\n        nextElementSibling\n      } = firstParagraph;\n      const startSpaces = (_content$match$ = (_content$match = content.match(/^\\s+/)) === null || _content$match === void 0 ? void 0 : _content$match[0]) !== null && _content$match$ !== void 0 ? _content$match$ : \"\";\n      const endSpaces = !nextElementSibling ? (_content$match$2 = (_content$match2 = content.match(/\\s+$/)) === null || _content$match2 === void 0 ? void 0 : _content$match2[0]) !== null && _content$match$2 !== void 0 ? _content$match$2 : \"\" : \"\";\n      if (content.match(/^\\n\\n/)) {\n        firstParagraph.innerHTML = `${firstParagraph.innerHTML}${endSpaces}`;\n        return;\n      }\n      unwrapElement(firstParagraph);\n      node.innerHTML = `${startSpaces}${node.innerHTML}${endSpaces}`;\n    }\n  }\n  /**\n   * @param {markdownit} md\n   */\n  withPatchedRenderer(md2) {\n    const withoutNewLine = (renderer) => function() {\n      const rendered = renderer(...arguments);\n      if (rendered === \"\\n\") {\n        return rendered;\n      }\n      if (rendered[rendered.length - 1] === \"\\n\") {\n        return rendered.slice(0, -1);\n      }\n      return rendered;\n    };\n    md2.renderer.rules.hardbreak = withoutNewLine(md2.renderer.rules.hardbreak);\n    md2.renderer.rules.softbreak = withoutNewLine(md2.renderer.rules.softbreak);\n    md2.renderer.rules.fence = withoutNewLine(md2.renderer.rules.fence);\n    md2.renderer.rules.code_block = withoutNewLine(md2.renderer.rules.code_block);\n    md2.renderer.renderToken = withoutNewLine(md2.renderer.renderToken.bind(md2.renderer));\n    return md2;\n  }\n}\nconst MarkdownClipboard = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Extension.create({\n  name: \"markdownClipboard\",\n  addOptions() {\n    return {\n      transformPastedText: false,\n      transformCopiedText: false\n    };\n  },\n  addProseMirrorPlugins() {\n    return [new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_3__.Plugin({\n      key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_3__.PluginKey(\"markdownClipboard\"),\n      props: {\n        clipboardTextParser: (text, context, plainText) => {\n          if (plainText || !this.options.transformPastedText) {\n            return null;\n          }\n          const parsed = this.editor.storage.markdown.parser.parse(text, {\n            inline: true\n          });\n          return _tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__.DOMParser.fromSchema(this.editor.schema).parseSlice(elementFromString(parsed), {\n            preserveWhitespace: true,\n            context\n          });\n        },\n        /**\n         * @param {import('prosemirror-model').Slice} slice\n         */\n        clipboardTextSerializer: (slice) => {\n          if (!this.options.transformCopiedText) {\n            return null;\n          }\n          return this.editor.storage.markdown.serializer.serialize(slice.content);\n        }\n      }\n    })];\n  }\n});\nconst Markdown = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.Extension.create({\n  name: \"markdown\",\n  priority: 50,\n  addOptions() {\n    return {\n      html: true,\n      tightLists: true,\n      tightListClass: \"tight\",\n      bulletListMarker: \"-\",\n      linkify: false,\n      breaks: false,\n      transformPastedText: false,\n      transformCopiedText: false\n    };\n  },\n  addCommands() {\n    const commands = _tiptap_core__WEBPACK_IMPORTED_MODULE_4__.extensions.Commands.config.addCommands();\n    return {\n      setContent: (content, emitUpdate, parseOptions) => (props) => {\n        return commands.setContent(props.editor.storage.markdown.parser.parse(content), emitUpdate, parseOptions)(props);\n      },\n      insertContentAt: (range, content, options) => (props) => {\n        return commands.insertContentAt(range, props.editor.storage.markdown.parser.parse(content, {\n          inline: true\n        }), options)(props);\n      }\n    };\n  },\n  onBeforeCreate() {\n    this.editor.storage.markdown = {\n      options: {\n        ...this.options\n      },\n      parser: new MarkdownParser(this.editor, this.options),\n      serializer: new MarkdownSerializer(this.editor),\n      getMarkdown: () => {\n        return this.editor.storage.markdown.serializer.serialize(this.editor.state.doc);\n      }\n    };\n    this.editor.options.initialContent = this.editor.options.content;\n    this.editor.options.content = this.editor.storage.markdown.parser.parse(this.editor.options.content);\n  },\n  onCreate() {\n    this.editor.options.content = this.editor.options.initialContent;\n    delete this.editor.options.initialContent;\n  },\n  addStorage() {\n    return {\n      /// storage will be defined in onBeforeCreate() to prevent initial object overriding\n    };\n  },\n  addExtensions() {\n    return [MarkdownTightLists.configure({\n      tight: this.options.tightLists,\n      tightClass: this.options.tightListClass\n    }), MarkdownClipboard.configure({\n      transformPastedText: this.options.transformPastedText,\n      transformCopiedText: this.options.transformCopiedText\n    })];\n  }\n});\n\n//# sourceMappingURL=tiptap-markdown.es.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tiptap-markdown/dist/tiptap-markdown.es.js\n");

/***/ })

};
;