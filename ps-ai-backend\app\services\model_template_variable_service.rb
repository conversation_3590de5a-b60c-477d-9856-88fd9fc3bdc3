# frozen_string_literal: true

class ModelTemplateVariableService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
    @openai_service = OpenaiService.new(user_data)
  end

  def create(params)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    model_template_id = params[:model_template_id]
    model_template = ModelTemplate.find(model_template_id)
    authorize! template_ownership(organization_user, model_template, @organization)

    model = Model.find_by!(model_template_id: model_template_id)

    template_variable = ModelTemplateVariable.new

    ActiveRecord::Base.transaction do
      template_variable = ModelTemplateVariable.create!(params)

      # TODO move to background job
      # Upload file to OpenAI if file present
      file_url = template_variable.variable_reference_url
      if file_url.present?
        assistant_response = @openai_service.retrieve_assistant(model.openai_assistant_id)

        if assistant_response['tool_resources'].blank? || assistant_response['tool_resources']['file_search']['vector_store_ids'].blank?
          vs_response = @openai_service.create_vector_store("Vector Store Template: #{model_template.name}-#{model_template.id}")
          vector_store_id = vs_response['id']

          tool_resources = {
            file_search: {
              vector_store_ids: [vs_response['id']]
            }
          }

          assistant_params = {
            model: model.model,
            name: "#{model_template.name} #{model.model}",
            temperature: model_template.temperature,
            instructions: model.structured_prompt,
            response_format_type: model.response_format_type
          }

          @openai_service.modify_assistant(assistant_params, tool_resources: tool_resources)
        end

        response_file = @openai_service.create_file(file_url, 'assistants')
        
        OpenaiFile.create!(
          object_id: template_variable.id,
          object_class: template_variable.class.name,
          object_class_column: 'variable_reference_url',
          openai_file_id: response_file['id']
        )

        @openai_service.create_assistant_files(
          vector_store_id,
          mode: 'openai_file_ids',
          openai_file_ids: [response_file['id']]
        )
      end
    end

    template_variable
  end

  def update(id, params)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    params.delete(:model_template_id)
    template_variable = ModelTemplateVariable.find(id)

    model_template = template_variable.model_template
    authorize! template_ownership(organization_user, model_template, @organization)

    model = Model.find_by!(model_template_id: template_variable.model_template_id)

    ActiveRecord::Base.transaction do
      current_file_url = template_variable.variable_reference_url

      template_variable.update!(params)
      latest_file_url = template_variable.variable_reference_url

      # TODO move to background job
      # Update OpenAI file if variable reference is changed
      if current_file_url != latest_file_url
        openai_file = OpenaiFile.find_by(
          object_id: template_variable.id,
          object_class: template_variable.class.name,
          object_class_column: 'variable_reference_url'
        )
        if openai_file.present?
          begin
            response_file = @openai_service.delete_file(openai_file.openai_file_id)
            if response_file['deleted']
              openai_file.discard!
            end
          rescue Faraday::ResourceNotFound
            response_file = {}
            openai_file.discard!
          end
        end

        # TODO error handling
        if latest_file_url.present?
          assistant_response = @openai_service.retrieve_assistant(model.openai_assistant_id)
          if assistant_response['tool_resources'].blank? || assistant_response['tool_resources']['file_search']['vector_store_ids'].blank?
            vs_response = @openai_service.create_vector_store("Vector Store Template: #{model_template.name}-#{model_template.id}")
            vector_store_id = vs_response['id']

            tool_resources = {
              file_search: {
                vector_store_ids: [vs_response['id']]
              }
            }

            assistant_params = {
              model: model.model,
              name: "#{model_template.name} #{model.model}",
              temperature: model_template.temperature,
              instructions: model.structured_prompt,
              response_format_type: model.response_format_type
            }

            @openai_service.modify_assistant(assistant_params, tool_resources: tool_resources)
          end

          response_file = @openai_service.create_file(latest_file_url, 'assistants')

          OpenaiFile.create!(
            object_id: template_variable.id,
            object_class: template_variable.class.name,
            object_class_column: 'variable_reference_url',
            openai_file_id: response_file['id']
          )

          @openai_service.create_assistant_files(
            vector_store_id,
            mode: 'openai_file_ids',
            openai_file_ids: [response_file['id']]
          )
        end
      end
    end

    template_variable
  end

  def index(query_params)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    template_variables = ::ModelTemplateVariables.new

    filter = query_params.slice(:model_template_id, :search, :disable_pagination, :page, :per_page)
    if @organization.tier == 'superuser'
      # get global template if superuser tier
      filter = filter.merge(
        organization_id: nil
      )
    else
      filter = filter.merge(
        organization_id: organization_user.organization_id
      )
    end

    template_variables.filter(filter)
  end

  def destroy(id)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    template_variable = ModelTemplateVariable.find(id)

    model_template = template_variable.model_template
    authorize! template_ownership(organization_user, model_template, @organization)

    ActiveRecord::Base.transaction do
      # TODO move to background job
      openai_file = OpenaiFile.find_by(
        object_id: template_variable.id,
        object_class: template_variable.class.name,
        object_class_column: 'variable_reference_url'
      )
      if openai_file.present?
        begin
          response_file = @openai_service.delete_file(openai_file.openai_file_id)
        rescue Faraday::ResourceNotFound
          response_file = {}
        end
  
        openai_file.discard!
      end

      template_variable.discard!
    end
  end

  private

  def template_ownership(organization_user, template, organization)
    if organization.tier == 'superuser'
      template.organization_id.nil? || organization_user.organization_id == template.organization_id
    else
      organization_user.organization_id == template.organization_id
    end
  end
end
