import { useMutation } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";

import { toast } from "sonner";

export const deleteAccountPlan = ({
  accountId,
}: {
  accountId: number;
}): ApiResponse => {
  return api.delete(API_ROUTES.ACCOUNT_PLANS_DETAIL(accountId));
};

type UseDeleteAccountPlanOptions = {
  mutationConfig?: MutationConfig<typeof deleteAccountPlan>;
};

export const useDeleteAccountPlan = ({
  mutationConfig,
}: UseDeleteAccountPlanOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      toast("The selected account plan has been successfully removed");

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteAccountPlan,
  });
};
