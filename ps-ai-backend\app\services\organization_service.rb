# frozen_string_literal: true

class OrganizationService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
    @user_data = user_data
  end

  def create(params)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    params = params.with_indifferent_access

    params[:name] = params[:name].to_s
    params[:unique_id] = params[:unique_id] || SecureRandom.alphanumeric(10)
    params[:organization_code] = params[:name].first.upcase

    # if params.has_key?(:tier)
    #   assert! ['free', 'premium'].include?(params[:tier]), on_error: 'Tier invalid!'
    # end
    params[:tier] = 'free'

    designated_owner_email = params.delete(:designated_owner_email)
    organization_identifier_id = params.delete(:organization_identifier_id)

    new_org = Organization.new
    ActiveRecord::Base.transaction do
      begin
        new_org = Organization.create!(params)
      rescue ActiveRecord::RecordNotUnique
        raise Invalid, 'unique ID already exists, please try again'
      end

      if designated_owner_email.present?
        user_mgmt_service = UserManagementService.new(@user_data)

        invitation_params = {
          role: 'super_admin',
          email: designated_owner_email,
          organization_id: new_org.id,
          organization_identifier_id: organization_identifier_id
        }
        user_mgmt_service.create_invitation(invitation_params)
      end
    end

    new_org
  end

  def update(id, params)
    # verify_organization_tier(['superuser', 'premium'], @organization)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    # verify_roles(['owner', 'super_admin'], organization_user)
    verify_roles(['owner'], organization_user)

    params = params.with_indifferent_access

    to_be_updated_organization = Organization.find_by!(id: id)

    if organization_user.role&.name == 'owner' && @organization.tier == 'superuser'
      if to_be_updated_organization.tier == 'superuser'
        params.delete(:tier)
      end
    else
      params.delete(:tier)
      params.delete(:unique_id)
      authorize! to_be_updated_organization.id == @organization.id, on_error: 'Not owned organization'
    end

    if organization_user.role&.name == 'super_admin'
      params = params.slice(:image_url, :logo_url, :name)
    else
      Organization.color_fields.each do |f|
        if params.has_key?(f)
          assert! valid_color_hex?(params[f]), on_error: "Invalid color hex in field #{f.to_s}"
        end
      end
    end

    ActiveRecord::Base.transaction do
      to_be_updated_organization.update!(params)
    end

    to_be_updated_organization
  end

  def show(id)
    if id.to_i.zero?
      return Organization.find_or_create_by(id: 0)
    end
    authorize! @user&.id.present?
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    current_organization = organization_user.organization

    unless (organization_user.role&.name == 'owner' && current_organization.tier == 'superuser')
      authorize! id == organization_user.organization_id, on_error: 'Not owned organzation!'
    end

    organization = Organization.includes(:organization_crms).find(id)
    organization
  end

  def index(query_params)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    # verify_roles(['owner'], organization_user)

    organizations = ::Organizations.new

    filter = query_params.slice(
      :unique_id, :search, :disable_pagination, :page, :per_page
    )

    organizations.filter(filter)
  end

  def destroy(id)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    organization = Organization.find(id)

    assert! ['free', 'premium'].include?(organization.tier), on_error: 'Cannot delete non-client organization!'

    ActiveRecord::Base.transaction do
      organization.discard!
    end
  end

  def add_crm_to_organization(organization_id, crm_param)
    organization = Organization.find(organization_id)
    
    crm = organization.organization_crms.create!(crm_param)
    
    crm
  end

  def update_organization_crm(organization_id, crm_id, attrs)
    organization = Organization.find(organization_id)

    crm = organization.organization_crms.find(crm_id)

    crm.update!(attrs)

    crm
  end

  private

  def valid_color_hex?(color)
    valid_hex_regex = /^#?([a-fA-F0-9]{3}|[a-fA-F0-9]{6})$/
    (color =~ valid_hex_regex).present?
  end
end
