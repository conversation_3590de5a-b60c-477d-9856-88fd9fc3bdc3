# frozen_string_literal: true

class UserManagementService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
  end

  def create_invitation(params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner', 'super_admin'], organization_user)

    email = params[:email]&.strip&.downcase
    role = params[:role]
    organization_identifier_id = params[:organization_identifier_id]

    authorize! (Organization.role_hierarchy[role] || Organization.min_role_subs) >= (Organization.role_hierarchy[organization_user.role&.name] || Organization.max_role_subs),
               on_error: 'Cannot assign with higher role than your role'

    organization_id = if (organization_user.role&.name == 'owner' && organization_user.organization.tier == 'superuser')
      if role == 'owner'
        organization_user.organization_id
      else
        assert! params[:organization_id].present?, on_error: 'Invalid invitation!'
        params[:organization_id]
      end
    else
      organization_user.organization_id
    end

    organization = Organization.find_by(id: organization_id)
    assert! organization.present?, on_error: 'Organization not found'

    user = User.find_by(email: email)
    invited_org_user = OrganizationUser.find_by(organization_id: organization_id, user_id: user&.id)
    assert! invited_org_user.blank?, on_error: 'User already registered'

    role_obj = Role.find_by(organization_id: organization_id, name: role)
    assert! role_obj.present?, on_error: 'Role not found'

    current_time = Time.current
    last_invite = UserInvitation.where(
      organization_id: organization_id,
      email: email,
      invitation_expiry_date: current_time..,
      invitation_status: 'invited'
    ).first
    assert! !last_invite.present?, on_error: 'User already invited'

    if organization_identifier_id.present?
      same_identifier = UserInvitation.where(
        organization_id: organization_id,
        invitation_status: ['invited', 'confirmed'],
        organization_identifier_id: organization_identifier_id
      ).first
      assert! !same_identifier.present?, on_error: 'Employee ID already used'
    end

    invitation_code = SecureRandom.hex(64)
    invite_with_code = UserInvitation.where(
      invitation_code: invitation_code
    ).first
    assert! !invite_with_code.present?, on_error: 'Invite failed, please try again'

    user_invite = UserInvitation.create(
      email: email,
      invitation_status: 'invited',
      invitation_code: invitation_code,
      invitation_expiry_date: current_time + 1.days,
      organization_id: organization_id,
      invited_by_organization_user_id: organization_user.id,
      assigned_role_id: role_obj.id,
      organization_identifier_id: organization_identifier_id
    )

    Mailer::UserInvitationMailerJob.perform_later(user_invite)

    user_invite
  end

  def accept_invitation(params)
    invitation_code = params.delete(:invite_code)
    password = params.delete(:password)
    name = params.delete(:name) || ''
    last_name = params.delete(:last_name)

    ui = UserInvitation.find_by(invitation_code: invitation_code)
    authorize! ui.present?, on_error: 'Invitation Not Found'
    authorize! ui.invitation_status != 'confirmed', on_error: 'Invitation Already Used'

    current_time = Time.current
    expired_invitation = current_time >= ui.invitation_expiry_date
    if expired_invitation
      ui.update(invitation_status: 'expired')
      assert! !expired_invitation, on_error: 'Invitation Expired'
    end

    user = User.new
    organization_user = OrganizationUser.new

    ActiveRecord::Base.transaction do
      user = User.unscoped.find_by(
        email: ui.email
      )

      if user.blank?
        user = User.create!(
          email: ui.email,
          name: name,
          password: password,
          last_name:last_name
        )
      else
        user.update(
          discarded_at: nil,
          name: name,
          password: password,
          last_name:last_name
        )
      end

      organization_user = OrganizationUser.unscoped.find_by(
        user_id: user.id,
        organization_id: ui.organization_id
      )

      if organization_user.blank?
        organization_user = OrganizationUser.create!(
          user_id: user.id,
          organization_id: ui.organization_id,
          role_id: ui.assigned_role_id,
          status: 'active',
          organization_identifier_id: ui.organization_identifier_id
        )
      else
        organization_user.update(
          discarded_at: nil,
          role_id: ui.assigned_role_id,
          status: 'active',
          organization_identifier_id: ui.organization_identifier_id
        )
      end

      ui.update(invitation_status: 'confirmed')
    end

    organization_user
  end

  def detail_invitation(params)
    invitation_code = params[:invite_code]

    ui = UserInvitation.find_by(invitation_code: invitation_code)
    exist! ui.present?

    ui
  end

  def change_password_request(params)
    email = params[:email].downcase

    user = User.find_by(email: email)
    assert! user.present?, on_error: 'Email Invalid'

    current_time = Time.current
    last_request = TokenRequest.where(
      email: email,
      request_expiry_date: current_time..,
    ).where.not(
      request_status: 'confirmed'
    ).first
    assert! !last_request.present?, on_error: 'Already requested change password. please try again later'

    request_code = SecureRandom.hex(64)
    request_with_code = TokenRequest.where(
      request_code: request_code,
      request_expiry_date: current_time..
    ).first
    assert! !request_with_code.present?, on_error: 'Request failed, please try again'

    change_password_request = TokenRequest.create(
      email: email,
      request_status: 'requested',
      request_code: request_code,
      request_expiry_date: current_time + 1.days,
      requested_at: current_time,
      user_id: user.id,
      purpose: 'change_password'
    )

    Mailer::PasswordChangeRequestMailerJob.perform_later(change_password_request) 

    change_password_request
  end

  def change_password(params)
    request_code = params.delete(:request_code)
    password = params.delete(:password)
    password_confirmation = params.delete(:password_confirmation)

    cpr = TokenRequest.find_by(request_code: request_code)
    authorize! cpr.present?, on_error: 'Request Not Found'
    authorize! cpr.request_status != 'confirmed' && cpr.purpose == 'change_password', on_error: 'Request Invalid'

    current_time = Time.current
    expired_request = current_time >= cpr.request_expiry_date
    if expired_request
      cpr.update(request_status: 'expired')
      assert! !expired_request, on_error: 'Request Expired'
    end

    assert! password == password_confirmation, on_error: 'Wrong password confirmation'

    user = cpr.user

    ActiveRecord::Base.transaction do
      user.update(password: password)

      cpr.update(request_status: 'confirmed')
    end
  end

  def index(query_params)
    verify_organization_tier(['superuser', 'free'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner', 'super_admin'], organization_user)

    org_users = ::OrganizationUsers.new

    filter_org_id = query_params.delete(:organization_id)

    filter = query_params.slice(:search, :status, :disable_pagination, :page, :per_page)
    filter = filter.merge(
      organization_id: organization_user.organization_id
    )

    if @organization.tier == 'superuser' && filter_org_id.present?
      filter[:organization_id] = filter_org_id
    end

    filter[:status] = filter[:status] || 'active'

    filtered = org_users.include(:users, :team_leader_users, :organizations).filter(filter)

    OpenStruct.new(
      organization_users: filtered
    )
  end

  def update_user_permissions(user_id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    to_be_updated_organization_user = OrganizationUser.find_by(user_id: user_id, organization_id: organization_user.organization_id)
    exist! to_be_updated_organization_user.present?, on_error: 'User not found'

    permissions_input = params.delete(:permissions)
    
    if !permissions_input.is_a?(Hash)
      begin
        permissions_input = JSON.parse(permissions_input)
      rescue JSON::ParserError
        raise Invalid, 'Permission Input Format Invalid'
      end
    end

    ActiveRecord::Base.transaction do
      prompt_tuning_permissions = permissions_input['prompt_tuning_permissions']

      if [true, false].include?(prompt_tuning_permissions)
        to_be_updated_organization_user.update(prompt_tuning_permissions: prompt_tuning_permissions)
      end

      # TODO update other permission in RolePermission
    end

    to_be_updated_organization_user
  end

  def get_user_permissions(user_id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)

    get_user = User.find_by(id: user_id)
    exist! get_user.present?, on_error: 'User not found'

    get_organization_user = OrganizationUser.find_by(user_id: get_user.id, organization_id: organization_user.organization_id)
    exist! get_organization_user.present?, on_error: 'User not found'

    get_role = get_organization_user.role

    roles = { prompt_tuning_permissions: get_organization_user.prompt_tuning_permissions }

    if get_role.present?
      # TODO add from role permissions
    end

    OpenStruct.new(
      user_id: get_user.id,
      roles: roles
    )
  end
end
