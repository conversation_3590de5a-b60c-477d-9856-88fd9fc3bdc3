# frozen_string_literal: true

require_relative 'basic'

module Repositories
  class Hashes < Repositories::Basic
    def load(scope, limit = nil)
      new_scope = super
      sql = new_scope.klass.send(:sanitize_sql, new_scope.arel)
      binds = new_scope.arel.bind_values + new_scope.bind_values
      action = "#{new_scope.name} Load"

      result_set = new_scope.connection.select_all(sql, action, binds)
      column_types = result_set.column_types.dup

      result_set.map do |record|
        hash = {}
        record.each do |key, val|
          hash[key.to_sym] = column_types[key].send(:type_cast, val)
        end
        hash
      end
    end
  end
end
