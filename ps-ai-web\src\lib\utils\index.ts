import { type ClassValue, clsx } from "clsx";
import _ from "lodash";
import { twMerge } from "tailwind-merge";
import { default as dayjs } from "dayjs";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export const formatDate = (
  date?: string | Date | null,
  format: string = "MMMM D, YYYY"
) => {
  if (!date) return "-";

  return dayjs(date).format(format);
};

export function capitalize(str: string | undefined = "") {
  return _.startCase(_.camelCase(str));
}

export function getFullName(
  firstName?: string | null,
  lastName?: string | null
) {
  return firstName || lastName
    ? `${capitalize(firstName ?? "")} ${capitalize(lastName ?? "")}`
    : "-";
}

export function getUTCOffset(): string {
  const offsetMinutes = dayjs().utcOffset();
  const offsetHours = offsetMinutes / 60;
  const sign = offsetHours >= 0 ? "+" : "-";

  return `${sign}${Math.abs(offsetHours)}`;
}
