import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APPotentialOpportunity,
  APPotentialOpportunityBaseData,
} from "@/features/account-plan/types/revenue-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createPotentialOpportunity = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APPotentialOpportunityBaseData;
}): ApiResponse<APPotentialOpportunity> => {
  return api.post(
    API_ROUTES.ACCOUNT_PLANS_POTENTIAL_OPPORTUNITY(accountId),
    data
  );
};

type UseCreatePotentialOpportunityOptions = {
  mutationConfig?: MutationConfig<typeof createPotentialOpportunity>;
};

export const useCreatePotentialOpportunity = ({
  mutationConfig,
}: UseCreatePotentialOpportunityOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_POTENTIAL_OPPORTUNITY,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createPotentialOpportunity,
  });
};
