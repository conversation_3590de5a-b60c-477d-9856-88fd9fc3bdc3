import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { UserManagement } from "../types";
import { useMemo } from "react";
import { UserRole, UserStatus } from "@/features/auth/types/user";

type UserManagementListParams = BaseParams & {
  status?: UserStatus;
  organization_id?: number;
};

export const getUserManagementList = ({
  params,
}: {
  params?: UserManagementListParams;
}): ApiResponse<UserManagement[]> => {
  return api.get(API_ROUTES.USER_MANAGEMENTS, { params });
};

export const getUserManagementListQueryOptions = (
  params?: UserManagementListParams
) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.USER_MANAGEMENTS, params],
    queryFn: () => getUserManagementList({ params }),
  });
};

type UseUserManagementOptions = {
  params?: UserManagementListParams;
  queryConfig?: QueryConfig<typeof getUserManagementList>;
};

export const useUserManagementList = ({
  queryConfig,
  params,
}: UseUserManagementOptions) => {
  const userManagementListQuery = useQuery({
    ...getUserManagementListQueryOptions(params),
    ...queryConfig,
  });

  const userManagementList = useMemo(() => {
    return userManagementListQuery.data?.data || [];
  }, [userManagementListQuery.data?.data]);

  return {
    ...userManagementListQuery,
    userManagementList,
    isLastRemainingSuperAdmin:
      userManagementList.filter((v) => v.role?.name === UserRole.SUPER_ADMIN)
        .length === 1,
  };
};
