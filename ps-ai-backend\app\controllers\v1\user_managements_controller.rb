# frozen_string_literal: true

module V1
  class UserManagementsController < ApiController
    authorize_auth_token! :all, only: [
      :index, :create_invitation, :update_user_permissions, :get_user_permissions
    ]
    authorize_auth_token! :public, only: [:accept_invitation, :detail_invitation, :change_password_request, :change_password]

    def create_invitation
      input = ::V1::UserManagementInvitationInput.new(request_body)
      validate! input, capture_failure: true

      invitation = service.create_invitation(input.output)

      render_json invitation, use: :create_invite_format, status: :created
    end

    def accept_invitation
      input = ::V1::UserManagementAcceptInviteInput.new(request_body)
      validate! input, capture_failure: true

      user = service.accept_invitation(input.output)

      render_json user, status: :created
    end

    def detail_invitation
      invitation = service.detail_invitation(query_params)

      render_json_array invitation,
                        use: :invitation_format
    end

    def change_password_request
      input = ::V1::UserManagementChangePasswordRequestInput.new(request_body)
      validate! input, capture_failure: true

      request = service.change_password_request(input.output)

      render_json request, use: :create_change_password_request_format, status: :created
    end

    def change_password
      input = ::V1::UserManagementChangePasswordInput.new(request_body)
      validate! input, capture_failure: true

      request = service.change_password(input.output)

      render_empty_json({}, status: :ok)
    end

    def index
      result = service.index(query_params)

      render_json_array result.organization_users,
                        use: :format
    end

    def update_user_permissions
      input = ::V1::UserManagementPermissionUpdateInput.new(request_body)
      validate! input, capture_failure: true

      org_user = service.update_user_permissions(params[:user_id], input.output)

      render_json org_user, use: :format, status: :ok
    end

    def get_user_permissions
      roles = service.get_user_permissions(params[:user_id])

      render_json roles, use: :roles_format, status: :ok
    end

    private

    def default_output
      ::V1::UserManagementOutput
    end

    def service
      @service ||= ::UserManagementService.new(current_user_data)
    end
  end
end
