# frozen_string_literal: true

module V1
    class OrganizationsController < ApiController
      authorize_auth_token! :all, except: [:show]
      authorize_auth_token! :public, only: [:show]

      def create
        input = ::V1::OrganizationCreateInput.new(request_body)
        validate! input, capture_failure: true
  
        organization = service.create(input.output)
  
        render_json organization, use: :format, status: :ok
      end

      def update
        input = ::V1::OrganizationUpdateInput.new(request_body)
        validate! input, capture_failure: true
  
        organization = service.update(params[:id], input.output)
  
        render_json organization, use: :format, status: :ok
      end
  
      def show
        organization = service.show(params[:id])
  
        render_json organization,
                    use: :format,
                    status: :ok
      end

      def index
        organizations = service.index(query_params)
  
        render_json_array organizations,
                          use: :format,
                          status: :ok
      end

      def destroy
        service.destroy(params[:id])
  
        render_empty_json({}, status: :ok)
      end

      def create_crm
        input = ::V1::OrganizationCrmCreateInput.new(request_body)
        validate! input, capture_failure: true

        crm = service.add_crm_to_organization(params[:organization_id], input.output)

        render_empty_json({}, status: :ok)
      end

      def update_crm
        input = ::V1::OrganizationCrmUpdateInput.new(request_body)
        validate! input, capture_failure: true

        crm = service.update_organization_crm(params[:organization_id], params[:id], input.output)

        render_empty_json({}, status: :ok)
      end

      private
  
      def default_output
        ::V1::OrganizationOutput
      end

      def service
        @service ||= ::OrganizationService.new(current_user_data)
      end
    end
  end
  