import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";

export const deletePotentialOpportunity = ({
  accountId,
  id,
}: {
  id: number;
  accountId: number;
}): ApiResponse => {
  return api.delete(
    API_ROUTES.ACCOUNT_PLANS_POTENTIAL_OPPORTUNITY_DETAIL(accountId, id)
  );
};

type UseDeletePotentialOpportunityOptions = {
  mutationConfig?: MutationConfig<typeof deletePotentialOpportunity>;
};

export const useDeletePotentialOpportunity = ({
  mutationConfig,
}: UseDeletePotentialOpportunityOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_POTENTIAL_OPPORTUNITY,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deletePotentialOpportunity,
  });
};
