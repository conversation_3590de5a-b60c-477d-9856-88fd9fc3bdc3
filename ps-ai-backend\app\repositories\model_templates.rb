# frozen_string_literal: true

class ModelTemplates < ::ApplicationRepository
  def default_scope
    ::ModelTemplate.all
  end

  def filter_by_id(id)
    @scope.where(id: id)
  end

  def filter_by_organization_id(organization_id)
    @scope.where(organization_id: organization_id)
  end

  def filter_by_template_category_id(template_category_id)
    @scope.where(template_category_id: template_category_id)
  end

  def filter_by_search(search)
    @scope.where('name ilike ?', "%#{search}%")
  end

  def filter_by_list_template_ap_category(filter)
    tc_str = TemplateCategory.ap_table_map_to_categories[filter]
    if tc_str
      template_category = TemplateCategory.find_by(name: tc_str)
      @scope.where(template_category_id: template_category&.id)
    else
      @scope.where(status: 'active')
    end
  end

  def filter_by_status(status)
    @scope.where(status: status)
  end
end
