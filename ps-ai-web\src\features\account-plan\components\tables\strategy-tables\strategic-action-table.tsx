import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import _ from "lodash";
import { toast } from "sonner";

import { AccountPlanTableType } from "@/features/account-plan/types";
import DataTable from "@/components/ui/data-table";
import {
  DatePickerCell,
  TiptapCell,
} from "@/components/ui/data-table/data-table-components";

import { Button } from "@/components/ui/button";
import { APTopAction } from "@/features/account-plan/types/strategy-types";

import { useCreateTopAction } from "@/features/account-plan/api/strategy-apis/top-action/create-top-action";
import { useUpdateTopAction } from "@/features/account-plan/api/strategy-apis/top-action/update-top-action";
import { useDeleteTopAction } from "@/features/account-plan/api/strategy-apis/top-action/delete-top-action";
import { useTopActionList } from "@/features/account-plan/api/strategy-apis/top-action/get-top-action-list";
import { AccountTable, AccountTableTitle } from "../base-table";
import { getAccountPlanTableName } from "@/features/account-plan/constants";

export const StrategicActionTable = () => {
  const [tableData, setTableData] = useState<APTopAction[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { topActionList } = useTopActionList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const createTopAction = useCreateTopAction({});
  const updateTopAction = useUpdateTopAction({});
  const deleteTopAction = useDeleteTopAction({});

  useEffect(() => {
    if (!topActionList) return;

    const newTableData = topActionList?.map((v, idx) => ({
      idx,
      ...v,
    }));

    setTableData(newTableData);
  }, [topActionList]);

  const selectedRows = Object.keys(rowSelection)
    .filter((rowId) => rowSelection[rowId])
    .map((idx) => tableData[parseInt(idx)]);

  const onAddRow = async () => {
    try {
      const res = await createTopAction.mutateAsync({
        accountId,
      });

      setTableData((prev) => [...prev, res.data]);
    } catch (_) {
      toast("An unexpected error occured when adding data");
    }
  };

  const onDeleteRows = async () => {
    try {
      const promises = [];

      setTableData(
        tableData.filter((row) => !selectedRows.find((v) => v.id === row.id))
      );

      setRowSelection({});

      promises.push(
        selectedRows.map(async (row) => {
          if (!!row?.id) {
            return deleteTopAction.mutateAsync({
              id: row.id,
              accountId,
            });
          }
        })
      );
      await Promise.all(promises);
    } catch (_) {
      toast("An unexpected error occured when deleting rows");
    }
  };

  const onChangeData = useCallback(
    async (data: Partial<APTopAction>, id: number) => {
      try {
        setTableData((prev) =>
          prev.map((v) => (v.id === id ? { ...v, ...data } : v))
        );

        await updateTopAction.mutateAsync({
          accountId,
          id,
          data,
        });
      } catch (_) {
        toast("An unexpected error occured when modifying data");
      }
    },
    [accountId, updateTopAction]
  );

  const columns: ColumnDef<APTopAction>[] = useMemo(
    () => [
      {
        accessorKey: "description",
        header: "What will you do?",
        size: 325,
        cell: ({ row }) => {
          const rowData = row.original;

          return (
            <TiptapCell
              value={rowData.description}
              onChange={(description) =>
                onChangeData({ description }, rowData.id)
              }
              className="text-lg"
            />
          );
        },
        meta: {
          tooltip: "A clear and actionable explanation of the task.",
        },
      },
      {
        accessorKey: "how",
        header: "How will you do it?",
        size: 325,
        cell: ({ row }) => {
          const rowData = row.original;

          return (
            <TiptapCell
              value={rowData.how}
              onChange={(how) => onChangeData({ how }, rowData.id)}
              className="text-lg"
            />
          );
        },
        meta: {
          tooltip: "Describe methods, steps or processes for execution.",
        },
      },
      {
        accessorKey: "action_target",
        header: "Owner",
        size: 175,
        cell: ({ row }) => {
          const rowData = row.original;

          return (
            <TiptapCell
              value={rowData.action_target}
              onChange={(action_target) =>
                onChangeData({ action_target }, rowData.id)
              }
              className="text-lg"
            />
          );
        },
        meta: {
          tooltip: "Team member responsible for this strategic action.",
        },
      },
      {
        accessorKey: "action_date",
        header: "Due Date",
        size: 175,
        cell: ({ row }) => {
          const rowData = row.original;

          return (
            <DatePickerCell
              className="text-lg"
              date={rowData.action_date}
              onSelect={(action_date) => {
                onChangeData(
                  { action_date: action_date as unknown as string },
                  rowData.id
                );
              }}
            />
          );
        },
        meta: {
          tooltip:
            "The timeline or deadline by which the action should be completed to stay on track with the overall strategy.",
        },
      },
    ],
    [onChangeData]
  );

  return (
    <AccountTable
      type={AccountPlanTableType.TOP_ACTION}
      footer={
        <>
          <Button onClick={onAddRow} isLoading={createTopAction.isPending}>
            Add row
          </Button>
          <Button
            variant="destructive"
            disabled={selectedRows.length === 0}
            onClick={onDeleteRows}
          >
            Delete Row
          </Button>
        </>
      }
      heightRatio={0.2}
    >
      <DataTable
        columns={columns}
        data={tableData}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
      />
    </AccountTable>
  );
};
