{"name": "ps-ai-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint --fix", "cloc": "cloc . --exclude-dir=node_modules,.next,.history, --not-match-f='.*.(md|mjs|svg)$'"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-pdf/renderer": "^4.1.6", "@tabler/icons-react": "^3.17.0", "@tanstack/query-sync-storage-persister": "^5.59.0", "@tanstack/react-query": "^5.59.0", "@tanstack/react-query-persist-client": "^5.59.0", "@tanstack/react-table": "^8.21.2", "@tiptap/extension-bullet-list": "^2.11.5", "@tiptap/extension-list-item": "^2.11.5", "@tiptap/extension-list-keymap": "^2.11.5", "@tiptap/extension-mention": "^2.7.3", "@tiptap/extension-ordered-list": "^2.11.5", "@tiptap/extension-placeholder": "^2.7.3", "@tiptap/react": "^2.7.3", "@tiptap/starter-kit": "^2.7.3", "@tiptap/suggestion": "^2.7.3", "@types/chroma-js": "^2.4.5", "@uidotdev/usehooks": "^2.4.1", "ag-grid-community": "^32.2.1", "ag-grid-react": "^32.2.1", "axios": "^1.7.7", "bytes": "^3.1.2", "chroma-js": "^3.1.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.0.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.3.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "immer": "^10.1.1", "input-otp": "^1.2.4", "lodash": "^4.17.21", "lucide-react": "^0.441.0", "merge-refs": "^1.3.0", "next": "^14.2.30", "next-themes": "^0.3.0", "next-usequerystate": "^1.20.0", "react": "^18", "react-colorful": "^5.6.1", "react-day-picker": "8.10.1", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "rehype-raw": "^7.0.0", "rehype-react": "^8.0.0", "remark-gfm": "^4.0.0", "sharp": "^0.34.3", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tippy.js": "^6.3.7", "tiptap-markdown": "^0.8.10", "unified": "^11.0.5", "unist": "^0.0.1", "unist-util-visit": "^5.0.0", "use-deep-compare": "^1.3.0", "use-immer": "^0.10.0", "vaul": "^0.9.3", "zod": "^3.23.8", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query-devtools": "^5.59.0", "@types/bytes": "^3.1.4", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.9", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "eslint": "^8", "eslint-config-next": "14.2.11", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.30.0", "eslint-plugin-prettier": "5.2.6", "postcss": "^8", "prettier-plugin-tailwind-styled-components": "^0.0.4", "prettier-plugin-tailwindcss": "0.6.11", "sass": "^1.79.5", "tailwind-scrollbar": "^3.1.0", "tailwind-styled-components": "^2.2.0", "tailwindcss": "^3.4.1", "type-fest": "^4.26.1", "typescript": "^5"}}