"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input/";
import { PATH } from "@/constants/path";
import { AccountPlanStatus } from "@/features/account-plan/types";
import { useCreateAccountPlan } from "@/features/account-plan/api/create-account-plan";
import { useCreateStakeholderMapping } from "@/features/account-plan/api/position-apis/stakeholder-mapping/create-stakeholder-mapping";
import { useCreateWalletShare } from "@/features/account-plan/api/position-apis/wallet-share/create-wallet-share";
import { useCreateCircumstantialAnalysis } from "@/features/account-plan/api/position-apis/circumstantial-analysis/create-circumstantial-analysis";
import { useCreateSvot } from "@/features/account-plan/api/position-apis/svot/create-svot";
import { useCreateRevenueForecast } from "@/features/account-plan/api/revenue-apis/revenue-forecast/create-revenue-forecast";
import { useCreateHistoricRevenue } from "@/features/account-plan/api/revenue-apis/historic-revenue/create-historic-revenue";
import { useCreateCurrentRevenue } from "@/features/account-plan/api/revenue-apis/current-revenue/create-current-revenue";
import { useCreateCurrentOpportunity } from "@/features/account-plan/api/revenue-apis/current-opportunity/create-current-opportunity";
import { useCreatePotentialOpportunity } from "@/features/account-plan/api/revenue-apis/potential-opportunity/create-potential-opportunity";
import {
  APCircumstantialAnalysisType,
  APSvotType,
  APWalletShareType,
} from "@/features/account-plan/types/position-types";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useCreateAccountPlanGroup } from "@/features/account-plan/api/account-plan-group/create-account-plan-group";
import { isRequestError } from "@/lib/api-client";
import { QUERY_KEYS } from "@/constants/query-keys";
import { useCreateTargetedPerceptionDevelopment } from "@/features/account-plan/api/strategy-apis/targeted-perception-development/create-targeted-perception-development";
import { useCreateTopAction } from "@/features/account-plan/api/strategy-apis/top-action/create-top-action";
import { useCreateClientMeetingSchedule } from "@/features/account-plan/api/strategy-apis/client-meeting-schedule/create-client-meeting-schedule";
import { AutoComplete } from "@/components/ui/autocomplete";
import { currencies } from "@/constants/currencies";
import { useIndustryList } from "../../api/get-industry-list";
import { useUpdateAccountPlan } from "../../api/update-account-plan";

const createAccountPlanSchema = z.object({
  company: z.string().min(1, { message: "Please fill out the company name" }),
  account_addressable_area: z
    .string()
    .min(1, { message: "Please fill out the account addressable area" }),
  location: z.string().min(1, { message: "Please fill out the location" }),
  industry: z.object(
    {
      value: z.string(),
      label: z.string(),
    },
    { message: "Please choose an industry" }
  ),
  currency: z.object(
    {
      value: z.string(),
      label: z.string(),
      name: z.string(),
    },
    { message: "Please choose a currency" }
  ),
});

type CreateAccountPlanForm = z.infer<typeof createAccountPlanSchema>;

export const useCreateNewAccountPlanTables = () => {
  const [isLoading, setIsLoading] = useState(false);

  const queryClient = useQueryClient();
  const createAccountPlan = useCreateAccountPlan({});
  const updateAccountPlan = useUpdateAccountPlan({});

  const createStakeholderMapping = useCreateStakeholderMapping({});
  const createWalletShare = useCreateWalletShare({});
  const createCircumstantialAnalysis = useCreateCircumstantialAnalysis({});
  const createSvot = useCreateSvot({});

  const createHistoricRevenue = useCreateHistoricRevenue({});
  const createCurrentRevenue = useCreateCurrentRevenue({});
  const createCurrentOpportunity = useCreateCurrentOpportunity({});
  const createPotentialOpportunity = useCreatePotentialOpportunity({});
  const createRevenueForecast = useCreateRevenueForecast({});

  const createTargetedPerceptionDevelopment =
    useCreateTargetedPerceptionDevelopment({});
  const createTopAction = useCreateTopAction({});
  const createClientMeetingSchedule = useCreateClientMeetingSchedule({});

  const onCreateNewAccountPlanTables = async (accountId: number) => {
    setIsLoading(true);

    try {
      const promises: Promise<{}>[] = [];

      promises.push(
        ...Array.from({ length: 1 }).map(() =>
          updateAccountPlan.mutateAsync({
            accountId,
            data: {
              review_date: new Date(),
            },
          })
        )
      );

      promises.push(
        ...Array.from({ length: 1 }).map(() =>
          createStakeholderMapping.mutateAsync({
            accountId,
          })
        )
      );

      promises.push(
        ...[
          APWalletShareType.ADDRESSABLE,
          APWalletShareType.OURS,
          APWalletShareType.COMPETITION,
          APWalletShareType.AVAILABLE,
        ].map((item_type) =>
          createWalletShare.mutateAsync({
            accountId,
            data: { item_type },
          })
        )
      );

      promises.push(
        ...[
          APCircumstantialAnalysisType.MACRO,
          APCircumstantialAnalysisType.INDUSTRY,
          APCircumstantialAnalysisType.BUSINESS,
        ].map((item_type) =>
          createCircumstantialAnalysis.mutateAsync({
            accountId,
            data: { item_type },
          })
        )
      );

      promises.push(
        ...[
          APSvotType.STRENGTH,
          APSvotType.VULNERABILITY,
          APSvotType.OPPORTUNITY,
          APSvotType.THREAT,
        ].map((item_type) =>
          createSvot.mutateAsync({
            accountId,
            data: { item_type },
          })
        )
      );

      promises.push(
        ...Array.from({ length: 1 }).map(() =>
          createHistoricRevenue.mutateAsync({
            accountId,
          })
        )
      );

      promises.push(
        ...Array.from({ length: 1 }).map(() =>
          createCurrentRevenue.mutateAsync({
            accountId,
          })
        )
      );

      promises.push(
        ...Array.from({ length: 1 }).map(() =>
          createCurrentOpportunity.mutateAsync({
            accountId,
          })
        )
      );

      promises.push(
        ...Array.from({ length: 1 }).map(() =>
          createPotentialOpportunity.mutateAsync({
            accountId,
          })
        )
      );

      promises.push(
        ...["12", "18", "24"].map((timespan) =>
          createRevenueForecast.mutateAsync({
            accountId,
            data: {
              timespan,
            },
          })
        )
      );

      promises.push(
        ...Array.from({ length: 2 }).map(() =>
          createTargetedPerceptionDevelopment.mutateAsync({
            accountId,
          })
        )
      );

      promises.push(
        ...Array.from({ length: 2 }).map((_, idx) =>
          createTopAction.mutateAsync({
            accountId,
            data: {
              order: idx + 1,
            },
          })
        )
      );

      promises.push(
        ...Array.from({ length: 2 }).map(() =>
          createClientMeetingSchedule.mutateAsync({
            accountId,
          })
        )
      );

      await Promise.all(promises);
    } catch (_) {
    } finally {
      setIsLoading(false);
    }
  };

  const onCreateNewAccountPlan = async (account_plan_group_id: number) => {
    setIsLoading(true);

    try {
      const { data: newAccountPlan } = await createAccountPlan.mutateAsync({
        data: {
          account_plan_group_id,
          status: AccountPlanStatus.ACTIVE,
          review_date: new Date(),
        },
      });
      const accountId = newAccountPlan.id;

      await onCreateNewAccountPlanTables(accountId);
    } catch (_) {
    } finally {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ACCOUNT_PLANS],
      });

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ACCOUNT_PLANS_GROUPS, account_plan_group_id],
      });

      setIsLoading(false);
    }
  };

  return { onCreateNewAccountPlan, onCreateNewAccountPlanTables, isLoading };
};

export default function CreateAccountPlanButton() {
  const router = useRouter();
  const [isLoadingCreateAccountPlan, setIsLoadingCreateAccountPlan] =
    useState(false);

  const form = useForm<CreateAccountPlanForm>({
    resolver: zodResolver(createAccountPlanSchema),
    reValidateMode: "onSubmit",
    defaultValues: {
      company: "",
      account_addressable_area: "",
      location: "",
    },
  });

  const { onCreateNewAccountPlanTables } = useCreateNewAccountPlanTables();
  const createAccountPlanGroup = useCreateAccountPlanGroup({});
  const { industryOptions } = useIndustryList({});

  const onCreateNewAccountPlanGroup = async (values: CreateAccountPlanForm) => {
    const { industry, currency, ...data } = values;

    try {
      setIsLoadingCreateAccountPlan(true);

      const { data: newAccountPlanGroup } =
        await createAccountPlanGroup.mutateAsync({
          data: {
            ...data,
            currency: currency.name,
            industry_id: industryOptions.find((v) => v.label === industry.label)
              ?.id,
          },
        });
      const accountId = newAccountPlanGroup.account_plans[0].id;

      await onCreateNewAccountPlanTables(accountId);

      if (accountId) {
        router.push(PATH.DASHBOARD_ACCOUNT_PLAN_EDIT(accountId));
        toast("Successfully created a new account plan");
      }
    } catch (e) {
      if (isRequestError(e)) {
        toast.error(e.response?.data.errors[0].message);
      } else {
        toast.error("An error occured while creating new account plan");
      }
    } finally {
      setIsLoadingCreateAccountPlan(false);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="rounded-xl bg-gradient">New Account Plan</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogTitle>Create new account plan</DialogTitle>
        <DialogDescription>
          Enter the details of new account plan
        </DialogDescription>

        <Form {...form}>
          <form className="mt-res-y-base flex flex-col gap-res-y-base">
            <FormField
              control={form.control}
              name="company"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">Company</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter the company name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="account_addressable_area"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    Account Adressable Area
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter the account addressable area"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">Location</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter the account location"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="industry"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">Industry</FormLabel>
                  <FormControl>
                    <AutoComplete
                      placeholder="Search Industry Name"
                      options={industryOptions}
                      value={field.value}
                      onValueChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="currency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">Currency</FormLabel>
                  <FormControl>
                    <AutoComplete
                      placeholder="Search Currency"
                      options={currencies.map((v) => ({
                        ...v,
                        label: `${v.code} ${v.symbolNative} - ${v.name}`,
                        value: `${v.code} ${v.symbolNative} - ${v.name}`,
                      }))}
                      value={field.value}
                      onValueChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              className="mt-res-y-base"
              type="submit"
              onClick={form.handleSubmit(onCreateNewAccountPlanGroup)}
              isLoading={isLoadingCreateAccountPlan}
            >
              Create account plan
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
