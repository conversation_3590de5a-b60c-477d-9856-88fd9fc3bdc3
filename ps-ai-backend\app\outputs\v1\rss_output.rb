# frozen_string_literal: true

module V1
  class RssOutput < ApiOutput
    def format
      {
        id: @object.id,
        rss_id: @object.rss_id,
        source_url: @object.source_url,
        rss_feed_url: @object.rss_feed_url,
        title: @object.title,
        description: @object.description,
        rss_feeds: rss_feeds_output,
        updated_at: @object.updated_at,
        industry: industry_output
      }
    end

    def rss_feeds_output
      return if rss_feeds.blank?

      curr_feeds = rss_feeds.select { |rf| rf.rss_id == @object.id }

      curr_feeds.map do |rf|
        ::V1::RssFeedOutput.new(rf).format
      end
    end

    def industry_output
      return if industries.blank?

      curr_industry = industries.find { |i| i.id == @object.industry_id }

      return if curr_industry.blank?

      ::V1::IndustryOutput.new(curr_industry).format
    end

    def rss_feeds
      @options[:rss_feeds]
    end

    def industries
      @options[:industries]
    end
  end
end
