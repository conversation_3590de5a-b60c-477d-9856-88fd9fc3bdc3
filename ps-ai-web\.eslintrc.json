{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:prettier/recommended", "plugin:import/recommended"], "parserOptions": {"project": "./tsconfig.json", "tsconfigRootDir": "./", "parser": "@typescript-eslint/parser"}, "settings": {"import/parsers": {"@typescript-eslint/parser": [".ts", ".tsx"]}, "import/resolver": {"typescript": {"alwaysTryTypes": true, "project": "./tsconfig.json"}}}, "rules": {"react-hooks/exhaustive-deps": ["warn", {"additionalHooks": "(useDeepCompareEffect|useDeepCompareCallback|useDeepCompareMemo|useDeepCompareImperativeHandle|useDeepCompareLayoutEffect)"}], "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "@typescript-eslint/no-empty-object-type": "off", "@typescript-eslint/ban-ts-comment": "off", "react/prop-types": "off", "react/react-in-jsx-scope": "off", "react/no-unescaped-entities": "off", "import/named": "off", "import/no-named-as-default": "off", "import/no-named-as-default-member": "off"}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "react-hooks"]}