# frozen_string_literal: true

class OrganizationUser < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  enum status: string_enum('active', 'archived')

  belongs_to :user
  belongs_to :organization
  belongs_to :role, optional: true

  belongs_to :team_leader_organization_user,  
             optional: true,
             class_name: 'OrganizationUser',
             foreign_key: 'team_leader_organization_user_id'

  # before_create :set_organization_identifier_id
  # after_create :increase_organization_identifier_id

  def set_organization_identifier_id
    organization = self.organization

    self.organization_identifier_id = organization.last_used_identifier_id + 1
  end

  def increase_organization_identifier_id
    organization = self.organization

    organization.update!(last_used_identifier_id: self.organization_identifier_id)
  end
end
