# frozen_string_literal: true

class ExceptionOutput < ErrorOutput
  def initialize(e, options = {})
    super(e, options)
    render_exception
  end

  def as_json(*_)
    super.merge(backtrace: backtrace).compact
  end

  def error_message
    @message
  end

  private

  def backtrace
    @options[:debug] &&
      @object.backtrace &&
      ::Rails.backtrace_cleaner.clean(@object.backtrace)
  end

  def render_exception
    render = @object.class.name.underscore.parameterize.underscore
    render = :exception unless respond_to?(render, true)
    send(render, @object)
  end

  def faraday_bad_request_error(e)
    @options[:status] = 400
    @message = e.message
  end

  def faraday_unauthorized_error(e)
    @options[:status] = 401
    @message = e.message
  end

  def faraday_forbidden_error(e)
    @options[:status] = 403
    @message = e.message
  end

  def faraday_resource_not_found(e)
    @options[:status] = 404
    @message = e.message
  end

  def active_record_record_invalid(e)
    @options[:status] = 422
    @message = e.record.errors.to_a.first
  end

  def active_model_strict_validation_failed(e)
    @options[:status] = 422
    @message = e.message
  end

  def active_model_unknown_attribute_error(e)
    @options[:status] = 422
    @message = e.message
  end

  def active_record_unknown_attribute_error(e)
    @options[:status] = 422
    @message = e.message
  end

  def active_record_record_not_found(e)
    _, _, klass = e.message.match(/\ACouldn't find (all )?([^\s]+)/).to_a
    klass ||= 'Object'
    i18n_key = klass.underscore
    name =
      begin
        scope = [:activerecord, :models]
        default = klass.underscore.parameterize.titleize.capitalize
        I18n.translate(i18n_key, scope: scope, default: default)
      end
    @options[:status] = 404
    @message =
      begin
        scope = [:errors, *namespace.split('/'), i18n_key]
        options = { klass: name, scope: scope, cascade: true }
        I18n.translate(:not_found, **options)
      end
  end

  def action_controller_parameter_missing(e)
    @options[:status] = 422
    @message = e.message
  end

  def action_controller_routing_error(e)
    @options[:status] = 404
    @message = e.message
  end

  def exception_handler_missing_token(e)
    @options[:status] = 422
    @message = e.message
  end

  def exception_handler_authentication_error(e)
    @options[:status] = 401
    @message = e.message
  end

  def exception_handler_unauthorized(e)
    @options[:status] = 403
    @message = e.message
  end

  def exception_handler_not_found(e)
    @options[:status] = 404
    @message = e.message
  end

  def app_service_invalid(e)
    @options[:status] = 422
    @message = e.message
  end

  def exception(e)
    Honeybadger.notify(e)
    Sentry.capture_exception(e)
    @options[:status] = 500
    @message = if @options[:debug]
                 e.message
               else
                 "Sorry, there's an error on our side. We're working on it!"
               end
  end

  def namespace
    @options[:namespace]
  end

  alias exception_handler_invalid_token exception_handler_missing_token
end
