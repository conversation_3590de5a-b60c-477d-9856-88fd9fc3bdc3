import _ from "lodash";

import {
  PdfTable,
  PdfTableCell,
  PdfTableHeader,
  PdfTableRow,
  PdfTableTitle,
} from "..";
import { AccountPlanAnalysisPDFProps } from "../../download-analysis";
import { formatDecimalValue } from "@/lib/utils/table-utils";
import { formatDate } from "@/lib/utils";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { AccountPlanTableType } from "@/features/account-plan/types";

export const PotentialOpportunityPDFTable = ({
  data,
  currency,
}: {
  data?: AccountPlanAnalysisPDFProps["potentialOpportunityList"];
  currency?: string;
}) => {
  return (
    <PdfTable>
      <PdfTableTitle>
        {" "}
        {getAccountPlanTableName(AccountPlanTableType.POTENTIAL_OPPORTUNITY)}
      </PdfTableTitle>

      <PdfTableRow>
        <PdfTableHeader style={{ flex: 3 }}>
          Service and Product Description
        </PdfTableHeader>
        <PdfTableHeader>{currency}</PdfTableHeader>
        <PdfTableHeader>Close Date</PdfTableHeader>
      </PdfTableRow>

      {data?.map((row, idx) => (
        <PdfTableRow key={idx}>
          <PdfTableCell style={{ flex: 3 }}>
            {row.product_service_name}
          </PdfTableCell>
          <PdfTableCell>
            {formatDecimalValue({ value: row.value })}
          </PdfTableCell>
          <PdfTableCell>
            {formatDate(row.close_date, "DD/MM/YYYY")}
          </PdfTableCell>
        </PdfTableRow>
      ))}
    </PdfTable>
  );
};
