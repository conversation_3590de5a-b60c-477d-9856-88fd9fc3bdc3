class CreateOrganizationUsers < ActiveRecord::Migration[7.0]
  def change
    create_table :organization_users do |t|
      t.references :user, :null => false
      t.references :organization, :null => false
      t.integer :organization_identifier_id, :null => false
      t.string :role
      t.boolean :prompt_tuning_permissions
      t.datetime :discarded_at, index: true
      t.integer :team_leader_organization_user_id, :default => nil, :null => true
      t.string :country_code

      t.timestamps
    end

    add_index :organization_users,
              [:organization_id, :organization_identifier_id], 
              :unique => true,
              :name => 'organization_users_organization_identifier_index'

    add_index :organization_users, :team_leader_organization_user_id
  end
end
