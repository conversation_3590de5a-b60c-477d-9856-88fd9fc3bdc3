RSpec.describe ::V1::TemplateCategoriesController, type: :request do
  let(:user) do
    User.create(
      name: 'Test',
      email: '<EMAIL>',
      password: '12345678'
    )
  end
  let(:organization) do
    Organization.create(name: 'Test')
  end
  let(:organization_user) do
    OrganizationUser.create(
      organization_id: organization.id,
      user_id: user.id
    )
  end
  let(:organization2) do
    Organization.create(name: 'Test2')
  end

  let(:params) do
    {
      name: 'New Cat'
    }
  end

  def create_template_category(params, user)
    user_token = JsonWebToken.encode({ user_id: user.id })

    headers = {
      "CONTENT_TYPE" => "application/json",
      "Authorization" => "bearer #{user_token}"
    }

    post "/v1/template_categories", headers: headers, params: params.to_json
  end

  def list_template_categories(params, user)
    user_token = JsonWebToken.encode({ user_id: user.id })

    headers = {
      "CONTENT_TYPE" => "application/json",
      "Authorization" => "bearer #{user_token}"
    }

    get "/v1/template_categories", headers: headers, params: params
  end

  describe "GET list template categories" do
    before do
      # init data
      user
      organization
      organization_user
      organization2

      create_template_category(params, user)

      params[:name] = 'New Cat 2'
      create_template_category(params, user)

      TemplateCategory.create(
        name: 'New Cat 3',
        organization_id: organization2.id
      )
    end

    it "return list template categories from user organization" do
      expect(TemplateCategory.all.count).to eq 3

      list_template_categories({}, user)
      expect(response.status).to eq 200

      response_body = JSON.parse(response.body).with_indifferent_access
      response_data = response_body[:data]
      expect(response_data.size).to eq 2
    end

    it "return filtered list template categories with 'search' params" do
      list_template_categories({ search: '2' }, user)
      expect(response.status).to eq 200

      response_body = JSON.parse(response.body).with_indifferent_access
      response_data = response_body[:data]
      expect(response_data.size).to eq 1

      expect(response_data.first[:name]).to eq 'New Cat 2'
    end

    it "return list template categories from user organization even if 'organization_id' params" do
      list_template_categories({ organization_id: organization2.id }, user)
      expect(response.status).to eq 200

      response_body = JSON.parse(response.body).with_indifferent_access
      response_data = response_body[:data]
      expect(response_data.size).to eq 2
    end
  end
end