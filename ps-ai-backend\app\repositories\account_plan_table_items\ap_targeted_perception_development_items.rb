# frozen_string_literal: true

class AccountPlanTableItems::ApTargetedPerceptionDevelopmentItems < ::ApplicationRepository
  def default_scope
    ::AccountPlanTableItems::ApTargetedPerceptionDevelopmentItem
  end

  def filter_by_ap_table_id(ap_table_id)
    @scope.where(ap_table_id: ap_table_id)
  end

  def filter_by_ap_stakeholder_mapping_item_id(stakeholder_mapping_item_id)
    @scope.where(ap_stakeholder_mapping_item_id: stakeholder_mapping_item_id)
  end

  def filter_by_exist_stakeholder(bool)
    if bool
      @scope.select(
        "ap_targeted_perception_development_items.*",
        "CASE WHEN sm.discarded_at is not null THEN null ELSE ap_targeted_perception_development_items.ap_stakeholder_mapping_item_id END as new_sm_id"
      ).joins("left join ap_stakeholder_mapping_items as sm ON sm.id = ap_targeted_perception_development_items.ap_stakeholder_mapping_item_id")
    else
      @scope
    end
  end
end
