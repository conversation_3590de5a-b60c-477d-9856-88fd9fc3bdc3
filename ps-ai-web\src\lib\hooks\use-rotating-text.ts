import { useState, useEffect } from "react";

interface UseRotatingTextOptions {
  intervalTime?: number;
  shouldStart?: boolean;
}

const useRotatingText = (
  texts: string[],
  { intervalTime = 3000, shouldStart = true }: UseRotatingTextOptions = {}
) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [isFadingOut, setIsFadingOut] = useState(false);

  useEffect(() => {
    if (!shouldStart) return;

    const interval = setInterval(() => {
      setIsFadingOut(true);

      setTimeout(() => {
        setCurrentTextIndex((prevIndex) => (prevIndex + 1) % texts.length);
        setIsFadingOut(false);
      }, 500);
    }, intervalTime);

    return () => clearInterval(interval);
  }, [texts.length, intervalTime, shouldStart]);

  return { currentRotatingText: texts[currentTextIndex], isFadingOut };
};

export default useRotatingText;
