# frozen_string_literal: true

class AuthorizeApiRequest
  def initialize(headers = {})
    @headers = headers
  end

  def call
    { user_data: }
  end

  private

  attr_reader :headers

  def user_data
    if decoded_auth
      @user ||= User.find_by!(id: decoded_auth[:user_id] || decoded[:sub])
      @organization_user ||= OrganizationUser.find_by!(id: decoded_auth[:organization_user_id], status: 'active')
      @organization ||= Organization.find_by!(id: decoded_auth[:organization_id])

      {
        user: @user,
        organization_user: @organization_user,
        organization: @organization
      }
    end
  rescue ActiveRecord::RecordNotFound => e
    raise(
      ExceptionHandler::InvalidToken,
      ("#{ErrorMessage.invalid_token} #{e.message}")
    )
  end

  def decoded_auth_token
    decoded = JsonWebToken.decode(http_auth_header)

    @decoded_auth_token ||= decoded
  end

  def decoded_auth
    decoded_auth_token
  end

  def http_auth_header
    return headers['Authorization'].split(' ').last if headers['Authorization'].present?

    raise(ExceptionHandler::MissingToken, ErrorMessage.missing_token)
  end

  def auth_mechanism
    headers['Authorization'].to_s.split(' ').first&.downcase
  end
end
