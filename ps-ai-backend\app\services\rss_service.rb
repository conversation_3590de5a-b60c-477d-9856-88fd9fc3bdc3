# frozen_string_literal: true

class RssService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
  end

  def index(query_params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    
    rsses = ::Rsses.new

    ap_active_filter = query_params.delete(:ap_active)
    filter = query_params.slice(
      :topic, :latest, :industry_id,
      :page, :per_page
    )

    if ap_active_filter
      # get by @organization_user.organization_id just to double make sure its from current login user's organization 
      apg_active_ids = AccountPlan.where(organization_id: @organization_user.organization_id, status: 'active').pluck(:account_plan_group_id).compact.uniq
      industry_ids = AccountPlanGroup.where(id: apg_active_ids).pluck(:industry_id).compact.uniq
      filter[:industry_id] = industry_ids
    end

    filtered = rsses.filter(filter)

    rss_feeds = []
    if filter[:latest]
      rss_feeds = RssFeed.select('DISTINCT ON (rss_id) *')
                         .where(rss_id: filtered.pluck(:id).compact.uniq)
                         .order('rss_id ASC, created_at DESC')
    else
      rss_feeds = RssFeed.where(rss_id: filtered.pluck(:id).compact.uniq, status: 'new_feed')
    end

    industries = Industry.where(id: filtered.pluck(:industry_id).compact.uniq)

    OpenStruct.new(
      rsses: filtered,
      rss_feeds: rss_feeds,
      industries: industries
    )
  end

  def handle_webhook(access_key, params)
    authorize! ENV['WEBHOOK_ACCESS_KEY'] == access_key

    # Get RSS obj
    params = params.with_indifferent_access
    rss_params = params[:feed]
    rss_id = rss_params.delete(:id)

    ActiveRecord::Base.transaction do
      rss = Rss.find_or_create_by(rss_id: rss_id)

      # Get topic industry based on keyword / topic id
      rss_source_url = rss_params[:source_url]
      rss_source_url_query = URI.parse(rss_source_url).query
      if rss_source_url_query
        rss_source_url_params = CGI.parse(rss_source_url_query).with_indifferent_access
        topic = rss_source_url_params[:keyword] + rss_source_url_params[:topicId]

        if topic
          topic = topic.first
          industry = Industry.where("name  ilike ?", "%#{topic}%").order(:id).first

          if !industry.present?
            industry = Industry.create(name: apa_titleize(topic))
          end
        end
      end

      db_rss_params = {
        source_url: rss_params[:source_url],
        rss_feed_url: rss_params[:rss_feed_url],
        title: rss_params[:title],
        description: rss_params[:description],
        industry_id: industry&.id
      }

      # get feed params
      db_feed_params = []
      begin
        feed_params = params[:data][:items_new]
        feed_params.each do |fp|
          db_feed_params << {
            url: fp[:url],
            thumbnail: fp[:thumbnail],
            title: fp[:title],
            description: fp[:description_text],
            date_published: fp[:date_published],
            authors: fp[:authors],
            status: 'new_feed',
            rss_id: rss.id
          }
        end
      rescue NoMethodError
        # Do Nothing
      end

      rss.update(db_rss_params)

      if !db_feed_params.blank?
        RssFeed.where(rss_id: rss.id).update_all(status: 'outdated_feed')
        RssFeed.insert_all(db_feed_params)
      end
    end
  end
end
