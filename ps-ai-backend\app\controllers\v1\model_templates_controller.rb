# frozen_string_literal: true

module V1
  class ModelTemplatesController < ApiController
    authorize_auth_token! :all

    def index
      result = service.index(query_params)

      render_json_array result.templates,
                        ::V1::ModelTemplateOutput,
                        use: :format,
                        current_user: current_user,
                        # variables: result.variables,
                        template_categories: result.template_categories,
                        organization_users: result.organization_users,
                        account_plan_groups: result.account_plan_groups
    end

    def show
      result = service.show(params[:id])

      render_json result.template,
                  ::V1::ModelTemplateOutput,
                  use: :format,
                  current_user: current_user,
                  variables: result.variables,
                  template_categories: result.template_categories
    end

    def create
      input = ::V1::ModelTemplateCreationInput.new(request_body)
      validate! input, capture_failure: true

      result = service.create(input.output)

      render_json result.template,
                  ::V1::ModelTemplateOutput,
                  use: :format, status: :created,
                  current_user: current_user,
                  variables: result.variables,
                  template_categories: result.template_categories
    end

    def update
      input = ::V1::ModelTemplateUpdateInput.new(request_body)
      validate! input, capture_failure: true

      result = service.update(params[:id], input.output)

      render_json result.template,
                  ::V1::ModelTemplateOutput,
                  use: :format, status: :ok,
                  current_user: current_user,
                  variables: result.variables,
                  template_categories: result.template_categories
    end

    def destroy
      service.destroy(params[:id])

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::ModelTemplateOutput
    end

    def service
      @service ||= ::ModelTemplateService.new(current_user_data)
    end
  end
end
