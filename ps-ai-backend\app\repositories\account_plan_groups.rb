# frozen_string_literal: true

class AccountPlanGroups < ::ApplicationRepository
  def default_scope
    ::AccountPlanGroup.all
  end

  def filter_by_organization_id(organization_id)
    @scope.where(organization_id: organization_id)
  end

  def filter_by_search(search)
    @scope.where('account_plan_unique_id ilike ?', "%#{search}%")
  end

  def filter_by_account_plan_unique_id(account_plan_unique_id)
    @scope.where('account_plan_unique_id ilike ?', "%#{account_plan_unique_id}%")
  end

  # def filter_by_status(status)
  #   @scope.where('account_plan_groups.status = ?', "%#{status}%")
  # end

  def filter_by_currency(currency)
    @scope.where('account_plan_groups.currency ilike ?', "%#{currency}%")
  end

  def filter_by_company(company)
    @scope.where('account_plan_groups.company ilike ?', "%#{company}%")
  end

  def filter_by_industry_id(industry_id)
    @scope.where(industry_id: industry_id)
  end

  def filter_by_ap_name(name)
    @scope.where("(COALESCE(ap.version, '') || ' ' || COALESCE(ap.name, '')) ilike ?", "%#{name}%")
  end

  def filter_by_ap_owner(organization_user_id)
    @scope.where('ap.owner_organization_user_id = ?', organization_user_id)
  end

  def filter_by_sort(sort)
    column = sort[:sort_column]
    direction = sort[:sort_direction]

    if column == 'review_date'
      select_sql = Arel.sql("account_plan_groups.*, array_agg(COALESCE(ap.review_date, TIMESTAMP '1970-01-01 00:00:00') ORDER BY COALESCE(ap.review_date, TIMESTAMP '1970-01-01 00:00:00') DESC) as agg")
      order_sql = Arel.sql("(array_agg(COALESCE(ap.review_date, TIMESTAMP '1970-01-01 00:00:00') ORDER BY COALESCE(ap.review_date, TIMESTAMP '1970-01-01 00:00:00') DESC))[1] #{direction}")

      @scope.select(select_sql).order(order_sql)
    elsif column == 'next_review'
      select_sql = Arel.sql("account_plan_groups.*, array_agg(COALESCE(ap.review_date, TIMESTAMP '1970-01-01 00:00:00') + (COALESCE(ap.next_review_date, 0)::integer || ' month')::interval ORDER BY COALESCE(ap.review_date, TIMESTAMP '1970-01-01 00:00:00') + (COALESCE(ap.next_review_date, 0)::integer || ' month')::interval) as agg")
      order_sql = Arel.sql("(array_agg(COALESCE(ap.review_date, TIMESTAMP '1970-01-01 00:00:00') + (COALESCE(ap.next_review_date, 0)::integer || ' month')::interval ORDER BY COALESCE(ap.review_date, TIMESTAMP '1970-01-01 00:00:00') + (COALESCE(ap.next_review_date, 0)::integer || ' month')::interval DESC))[1] #{direction}")
      
      @scope.select(select_sql).order(order_sql)
    elsif column == 'industry'
      order_sql = Arel.sql("i.name #{direction}")
      group_sql = Arel.sql("i.name")
      @scope.joins('LEFT JOIN industries i ON i.id = account_plan_groups.industry_id')
            .order(order_sql).group(group_sql)
    elsif column == 'currency'
      order_sql = Arel.sql("account_plan_groups.currency #{direction}")
      @scope.order(order_sql)
    elsif column == 'company'
      order_sql = Arel.sql("UPPER(account_plan_groups.company) #{direction}")
      @scope.order(order_sql)
    elsif column == 'account_plan_unique_id'
      order_str = "COALESCE(NULLIF(split_part(account_plan_groups.account_plan_unique_id, '-', 1), ''), NULL)  #{direction}, COALESCE(NULLIF(split_part(account_plan_groups.account_plan_unique_id, '-', 2), '')::integer, NULL) #{direction}"
      order_sql = Arel.sql(order_str)
      @scope.order(order_sql)
    else
      @scope
    end
  end

  def include_account_plan_filter
    @scope.joins('LEFT JOIN account_plans ap ON ap.account_plan_group_id = account_plan_groups.id AND ap.discarded_at IS NULL').group('account_plan_groups.id')
  end
end
