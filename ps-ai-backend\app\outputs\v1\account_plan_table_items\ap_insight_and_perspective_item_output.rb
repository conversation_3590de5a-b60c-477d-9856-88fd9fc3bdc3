# frozen_string_literal: true

module V1
  class AccountPlanTableItems::ApInsightAndPerspectiveItemOutput < ApiOutput
    def format
      {
        id: @object.id,
        target_name: @object.target_name,
        description: @object.description,
        ap_table_id: @object.ap_table_id,
        ap_stakeholder_mapping_item: stakeholder_mapping_item_output
      }
    end

    def stakeholder_mapping_item_output
      begin
        if @object.new_sm_id.nil?
          return
        end
      rescue NoMethodError
        if @object.ap_stakeholder_mapping_item.nil?
          return
        end
      end

      AccountPlanTableItems::ApStakeholderMappingItemOutput.new(@object.ap_stakeholder_mapping_item)
                                                           .format
    end
  end
end
