# frozen_string_literal: true

module V1
  class AccountPlanOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        version: @object.version,
        status: @object.status,
        priority: @object.priority,
        review_date: @object.review_date,
        next_review_date: @object.next_review_date,
        currency: @object.currency,
        company: @object.company,
        location: @object.location,
        industry: @object.industry,
        function: @object.function,
        account_addressable_area: @object.account_addressable_area,
        organization_id: @object.organization_id,
        updated_at: @object.updated_at,
        generated_analysis: @object.generated_analysis,
        last_generated_analysis_at: @object.last_generated_analysis_at,
        last_updated_by: last_updated_by_output,
        owner_user: owner_user_output,
        model_template: model_template_output,
        account_plan_group: account_plan_group_output
      }
    end

    def active_top_action_format
      {
        id: @object.id,
        account_plan_unique_id: @object.unique_id,
        company_name: @object.company_name,
        top_actions: active_top_action_output
      }
    end

    def last_updated_by_output
      return if @object.last_updated_by_organization_user_id.nil? || organization_users.blank?

      org_user = organization_users.find do |ou|
        ou.id == @object.last_updated_by_organization_user_id
      end

      return if org_user.nil?
      
      ::V1::UserOutput.new(org_user.user).name_format
    end

    def owner_user_output
      return if @object.owner_organization_user_id.nil? || organization_users.blank?

      org_user = organization_users.find do |ou|
        ou.id == @object.owner_organization_user_id
      end

      return if org_user.nil?
      
      ::V1::UserOutput.new(org_user.user).name_format
    end

    def model_template_output
      {
        id: @object.model_template&.id,
        name: @object.model_template&.name
      }
    end

    def account_plan_group_output
      return if account_plan_groups.blank?

      curr_group = account_plan_groups.find { |g| g.id == @object.account_plan_group_id }

      return if curr_group.blank?

      {
        id: curr_group.id,
        account_plan_unique_id: curr_group.account_plan_unique_id,
        currency: curr_group.currency
      }
    end

    def active_top_action_output
      curr_ap_table = ap_tables.find { |apt| apt.account_plan_id == @object.id }

      return [] if curr_ap_table.blank?

      curr_top_actions = top_actions.select { |ta| ta.ap_table_id == curr_ap_table.id }

      return [] if curr_top_actions.blank?

      curr_top_actions.map do |cta|
        output = ::V1::AccountPlanTableItems::ApTopActionItemOutput.new(cta).format
        output['account_plan_id'] = @object.id
        output
      end
    end

    def current_user
      @options[:current_user]
    end

    def organization_users
      @options[:organization_users]
    end

    def account_plan_groups
      @options[:account_plan_groups]
    end

    def ap_tables
      @options[:ap_tables]
    end

    def top_actions
      @options[:top_actions]
    end
  end
end
