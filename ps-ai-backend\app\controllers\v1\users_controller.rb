# frozen_string_literal: true
# THIS SHOULD BE ORGANIZATION USER

module V1
  class UsersController < ApiController
    authorize_auth_token! :all

    def show
      result = service.show(params[:id], query_params)

      render_json result.user,
                  use: :format,
                  organization_user: result.organization_user,
                  organization: result.organization
    end

    def update
      input = ::V1::UserUpdateInput.new(request_body)
      validate! input, capture_failure: true

      result = service.update(params[:id], input.output)

      render_json result.user,
                  use: :format,
                  organization_user: result.organization_user,
                  organization: result.organization
    end

    def destroy
      service.destroy(params[:id], query_params)
  
      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::UserOutput
    end

    def service
      @service ||= ::UserService.new(current_user_data)
    end
  end
end
