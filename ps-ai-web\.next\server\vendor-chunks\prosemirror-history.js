"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-history";
exports.ids = ["vendor-chunks/prosemirror-history"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-history/dist/index.js":
/*!********************************************************!*\
  !*** ./node_modules/prosemirror-history/dist/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeHistory: () => (/* binding */ closeHistory),\n/* harmony export */   history: () => (/* binding */ history),\n/* harmony export */   redo: () => (/* binding */ redo),\n/* harmony export */   redoDepth: () => (/* binding */ redoDepth),\n/* harmony export */   redoNoScroll: () => (/* binding */ redoNoScroll),\n/* harmony export */   undo: () => (/* binding */ undo),\n/* harmony export */   undoDepth: () => (/* binding */ undoDepth),\n/* harmony export */   undoNoScroll: () => (/* binding */ undoNoScroll)\n/* harmony export */ });\n/* harmony import */ var rope_sequence__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rope-sequence */ \"(ssr)/./node_modules/rope-sequence/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/prosemirror-transform/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n\n\n\n\n// ProseMirror's history isn't simply a way to roll back to a previous\n// state, because ProseMirror supports applying changes without adding\n// them to the history (for example during collaboration).\n//\n// To this end, each 'Branch' (one for the undo history and one for\n// the redo history) keeps an array of 'Items', which can optionally\n// hold a step (an actual undoable change), and always hold a position\n// map (which is needed to move changes below them to apply to the\n// current document).\n//\n// An item that has both a step and a selection bookmark is the start\n// of an 'event' — a group of changes that will be undone or redone at\n// once. (It stores only the bookmark, since that way we don't have to\n// provide a document until the selection is actually applied, which\n// is useful when compressing.)\n// Used to schedule history compression\nconst max_empty_items = 500;\nclass Branch {\n    constructor(items, eventCount) {\n        this.items = items;\n        this.eventCount = eventCount;\n    }\n    // Pop the latest event off the branch's history and apply it\n    // to a document transform.\n    popEvent(state, preserveItems) {\n        if (this.eventCount == 0)\n            return null;\n        let end = this.items.length;\n        for (;; end--) {\n            let next = this.items.get(end - 1);\n            if (next.selection) {\n                --end;\n                break;\n            }\n        }\n        let remap, mapFrom;\n        if (preserveItems) {\n            remap = this.remapping(end, this.items.length);\n            mapFrom = remap.maps.length;\n        }\n        let transform = state.tr;\n        let selection, remaining;\n        let addAfter = [], addBefore = [];\n        this.items.forEach((item, i) => {\n            if (!item.step) {\n                if (!remap) {\n                    remap = this.remapping(end, i + 1);\n                    mapFrom = remap.maps.length;\n                }\n                mapFrom--;\n                addBefore.push(item);\n                return;\n            }\n            if (remap) {\n                addBefore.push(new Item(item.map));\n                let step = item.step.map(remap.slice(mapFrom)), map;\n                if (step && transform.maybeStep(step).doc) {\n                    map = transform.mapping.maps[transform.mapping.maps.length - 1];\n                    addAfter.push(new Item(map, undefined, undefined, addAfter.length + addBefore.length));\n                }\n                mapFrom--;\n                if (map)\n                    remap.appendMap(map, mapFrom);\n            }\n            else {\n                transform.maybeStep(item.step);\n            }\n            if (item.selection) {\n                selection = remap ? item.selection.map(remap.slice(mapFrom)) : item.selection;\n                remaining = new Branch(this.items.slice(0, end).append(addBefore.reverse().concat(addAfter)), this.eventCount - 1);\n                return false;\n            }\n        }, this.items.length, 0);\n        return { remaining: remaining, transform, selection: selection };\n    }\n    // Create a new branch with the given transform added.\n    addTransform(transform, selection, histOptions, preserveItems) {\n        let newItems = [], eventCount = this.eventCount;\n        let oldItems = this.items, lastItem = !preserveItems && oldItems.length ? oldItems.get(oldItems.length - 1) : null;\n        for (let i = 0; i < transform.steps.length; i++) {\n            let step = transform.steps[i].invert(transform.docs[i]);\n            let item = new Item(transform.mapping.maps[i], step, selection), merged;\n            if (merged = lastItem && lastItem.merge(item)) {\n                item = merged;\n                if (i)\n                    newItems.pop();\n                else\n                    oldItems = oldItems.slice(0, oldItems.length - 1);\n            }\n            newItems.push(item);\n            if (selection) {\n                eventCount++;\n                selection = undefined;\n            }\n            if (!preserveItems)\n                lastItem = item;\n        }\n        let overflow = eventCount - histOptions.depth;\n        if (overflow > DEPTH_OVERFLOW) {\n            oldItems = cutOffEvents(oldItems, overflow);\n            eventCount -= overflow;\n        }\n        return new Branch(oldItems.append(newItems), eventCount);\n    }\n    remapping(from, to) {\n        let maps = new prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.Mapping;\n        this.items.forEach((item, i) => {\n            let mirrorPos = item.mirrorOffset != null && i - item.mirrorOffset >= from\n                ? maps.maps.length - item.mirrorOffset : undefined;\n            maps.appendMap(item.map, mirrorPos);\n        }, from, to);\n        return maps;\n    }\n    addMaps(array) {\n        if (this.eventCount == 0)\n            return this;\n        return new Branch(this.items.append(array.map(map => new Item(map))), this.eventCount);\n    }\n    // When the collab module receives remote changes, the history has\n    // to know about those, so that it can adjust the steps that were\n    // rebased on top of the remote changes, and include the position\n    // maps for the remote changes in its array of items.\n    rebased(rebasedTransform, rebasedCount) {\n        if (!this.eventCount)\n            return this;\n        let rebasedItems = [], start = Math.max(0, this.items.length - rebasedCount);\n        let mapping = rebasedTransform.mapping;\n        let newUntil = rebasedTransform.steps.length;\n        let eventCount = this.eventCount;\n        this.items.forEach(item => { if (item.selection)\n            eventCount--; }, start);\n        let iRebased = rebasedCount;\n        this.items.forEach(item => {\n            let pos = mapping.getMirror(--iRebased);\n            if (pos == null)\n                return;\n            newUntil = Math.min(newUntil, pos);\n            let map = mapping.maps[pos];\n            if (item.step) {\n                let step = rebasedTransform.steps[pos].invert(rebasedTransform.docs[pos]);\n                let selection = item.selection && item.selection.map(mapping.slice(iRebased + 1, pos));\n                if (selection)\n                    eventCount++;\n                rebasedItems.push(new Item(map, step, selection));\n            }\n            else {\n                rebasedItems.push(new Item(map));\n            }\n        }, start);\n        let newMaps = [];\n        for (let i = rebasedCount; i < newUntil; i++)\n            newMaps.push(new Item(mapping.maps[i]));\n        let items = this.items.slice(0, start).append(newMaps).append(rebasedItems);\n        let branch = new Branch(items, eventCount);\n        if (branch.emptyItemCount() > max_empty_items)\n            branch = branch.compress(this.items.length - rebasedItems.length);\n        return branch;\n    }\n    emptyItemCount() {\n        let count = 0;\n        this.items.forEach(item => { if (!item.step)\n            count++; });\n        return count;\n    }\n    // Compressing a branch means rewriting it to push the air (map-only\n    // items) out. During collaboration, these naturally accumulate\n    // because each remote change adds one. The `upto` argument is used\n    // to ensure that only the items below a given level are compressed,\n    // because `rebased` relies on a clean, untouched set of items in\n    // order to associate old items with rebased steps.\n    compress(upto = this.items.length) {\n        let remap = this.remapping(0, upto), mapFrom = remap.maps.length;\n        let items = [], events = 0;\n        this.items.forEach((item, i) => {\n            if (i >= upto) {\n                items.push(item);\n                if (item.selection)\n                    events++;\n            }\n            else if (item.step) {\n                let step = item.step.map(remap.slice(mapFrom)), map = step && step.getMap();\n                mapFrom--;\n                if (map)\n                    remap.appendMap(map, mapFrom);\n                if (step) {\n                    let selection = item.selection && item.selection.map(remap.slice(mapFrom));\n                    if (selection)\n                        events++;\n                    let newItem = new Item(map.invert(), step, selection), merged, last = items.length - 1;\n                    if (merged = items.length && items[last].merge(newItem))\n                        items[last] = merged;\n                    else\n                        items.push(newItem);\n                }\n            }\n            else if (item.map) {\n                mapFrom--;\n            }\n        }, this.items.length, 0);\n        return new Branch(rope_sequence__WEBPACK_IMPORTED_MODULE_0__[\"default\"].from(items.reverse()), events);\n    }\n}\nBranch.empty = new Branch(rope_sequence__WEBPACK_IMPORTED_MODULE_0__[\"default\"].empty, 0);\nfunction cutOffEvents(items, n) {\n    let cutPoint;\n    items.forEach((item, i) => {\n        if (item.selection && (n-- == 0)) {\n            cutPoint = i;\n            return false;\n        }\n    });\n    return items.slice(cutPoint);\n}\nclass Item {\n    constructor(\n    // The (forward) step map for this item.\n    map, \n    // The inverted step\n    step, \n    // If this is non-null, this item is the start of a group, and\n    // this selection is the starting selection for the group (the one\n    // that was active before the first step was applied)\n    selection, \n    // If this item is the inverse of a previous mapping on the stack,\n    // this points at the inverse's offset\n    mirrorOffset) {\n        this.map = map;\n        this.step = step;\n        this.selection = selection;\n        this.mirrorOffset = mirrorOffset;\n    }\n    merge(other) {\n        if (this.step && other.step && !other.selection) {\n            let step = other.step.merge(this.step);\n            if (step)\n                return new Item(step.getMap().invert(), step, this.selection);\n        }\n    }\n}\n// The value of the state field that tracks undo/redo history for that\n// state. Will be stored in the plugin state when the history plugin\n// is active.\nclass HistoryState {\n    constructor(done, undone, prevRanges, prevTime, prevComposition) {\n        this.done = done;\n        this.undone = undone;\n        this.prevRanges = prevRanges;\n        this.prevTime = prevTime;\n        this.prevComposition = prevComposition;\n    }\n}\nconst DEPTH_OVERFLOW = 20;\n// Record a transformation in undo history.\nfunction applyTransaction(history, state, tr, options) {\n    let historyTr = tr.getMeta(historyKey), rebased;\n    if (historyTr)\n        return historyTr.historyState;\n    if (tr.getMeta(closeHistoryKey))\n        history = new HistoryState(history.done, history.undone, null, 0, -1);\n    let appended = tr.getMeta(\"appendedTransaction\");\n    if (tr.steps.length == 0) {\n        return history;\n    }\n    else if (appended && appended.getMeta(historyKey)) {\n        if (appended.getMeta(historyKey).redo)\n            return new HistoryState(history.done.addTransform(tr, undefined, options, mustPreserveItems(state)), history.undone, rangesFor(tr.mapping.maps), history.prevTime, history.prevComposition);\n        else\n            return new HistoryState(history.done, history.undone.addTransform(tr, undefined, options, mustPreserveItems(state)), null, history.prevTime, history.prevComposition);\n    }\n    else if (tr.getMeta(\"addToHistory\") !== false && !(appended && appended.getMeta(\"addToHistory\") === false)) {\n        // Group transforms that occur in quick succession into one event.\n        let composition = tr.getMeta(\"composition\");\n        let newGroup = history.prevTime == 0 ||\n            (!appended && history.prevComposition != composition &&\n                (history.prevTime < (tr.time || 0) - options.newGroupDelay || !isAdjacentTo(tr, history.prevRanges)));\n        let prevRanges = appended ? mapRanges(history.prevRanges, tr.mapping) : rangesFor(tr.mapping.maps);\n        return new HistoryState(history.done.addTransform(tr, newGroup ? state.selection.getBookmark() : undefined, options, mustPreserveItems(state)), Branch.empty, prevRanges, tr.time, composition == null ? history.prevComposition : composition);\n    }\n    else if (rebased = tr.getMeta(\"rebased\")) {\n        // Used by the collab module to tell the history that some of its\n        // content has been rebased.\n        return new HistoryState(history.done.rebased(tr, rebased), history.undone.rebased(tr, rebased), mapRanges(history.prevRanges, tr.mapping), history.prevTime, history.prevComposition);\n    }\n    else {\n        return new HistoryState(history.done.addMaps(tr.mapping.maps), history.undone.addMaps(tr.mapping.maps), mapRanges(history.prevRanges, tr.mapping), history.prevTime, history.prevComposition);\n    }\n}\nfunction isAdjacentTo(transform, prevRanges) {\n    if (!prevRanges)\n        return false;\n    if (!transform.docChanged)\n        return true;\n    let adjacent = false;\n    transform.mapping.maps[0].forEach((start, end) => {\n        for (let i = 0; i < prevRanges.length; i += 2)\n            if (start <= prevRanges[i + 1] && end >= prevRanges[i])\n                adjacent = true;\n    });\n    return adjacent;\n}\nfunction rangesFor(maps) {\n    let result = [];\n    for (let i = maps.length - 1; i >= 0 && result.length == 0; i--)\n        maps[i].forEach((_from, _to, from, to) => result.push(from, to));\n    return result;\n}\nfunction mapRanges(ranges, mapping) {\n    if (!ranges)\n        return null;\n    let result = [];\n    for (let i = 0; i < ranges.length; i += 2) {\n        let from = mapping.map(ranges[i], 1), to = mapping.map(ranges[i + 1], -1);\n        if (from <= to)\n            result.push(from, to);\n    }\n    return result;\n}\n// Apply the latest event from one branch to the document and shift the event\n// onto the other branch.\nfunction histTransaction(history, state, redo) {\n    let preserveItems = mustPreserveItems(state);\n    let histOptions = historyKey.get(state).spec.config;\n    let pop = (redo ? history.undone : history.done).popEvent(state, preserveItems);\n    if (!pop)\n        return null;\n    let selection = pop.selection.resolve(pop.transform.doc);\n    let added = (redo ? history.done : history.undone).addTransform(pop.transform, state.selection.getBookmark(), histOptions, preserveItems);\n    let newHist = new HistoryState(redo ? added : pop.remaining, redo ? pop.remaining : added, null, 0, -1);\n    return pop.transform.setSelection(selection).setMeta(historyKey, { redo, historyState: newHist });\n}\nlet cachedPreserveItems = false, cachedPreserveItemsPlugins = null;\n// Check whether any plugin in the given state has a\n// `historyPreserveItems` property in its spec, in which case we must\n// preserve steps exactly as they came in, so that they can be\n// rebased.\nfunction mustPreserveItems(state) {\n    let plugins = state.plugins;\n    if (cachedPreserveItemsPlugins != plugins) {\n        cachedPreserveItems = false;\n        cachedPreserveItemsPlugins = plugins;\n        for (let i = 0; i < plugins.length; i++)\n            if (plugins[i].spec.historyPreserveItems) {\n                cachedPreserveItems = true;\n                break;\n            }\n    }\n    return cachedPreserveItems;\n}\n/**\nSet a flag on the given transaction that will prevent further steps\nfrom being appended to an existing history event (so that they\nrequire a separate undo command to undo).\n*/\nfunction closeHistory(tr) {\n    return tr.setMeta(closeHistoryKey, true);\n}\nconst historyKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_2__.PluginKey(\"history\");\nconst closeHistoryKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_2__.PluginKey(\"closeHistory\");\n/**\nReturns a plugin that enables the undo history for an editor. The\nplugin will track undo and redo stacks, which can be used with the\n[`undo`](https://prosemirror.net/docs/ref/#history.undo) and [`redo`](https://prosemirror.net/docs/ref/#history.redo) commands.\n\nYou can set an `\"addToHistory\"` [metadata\nproperty](https://prosemirror.net/docs/ref/#state.Transaction.setMeta) of `false` on a transaction\nto prevent it from being rolled back by undo.\n*/\nfunction history(config = {}) {\n    config = { depth: config.depth || 100,\n        newGroupDelay: config.newGroupDelay || 500 };\n    return new prosemirror_state__WEBPACK_IMPORTED_MODULE_2__.Plugin({\n        key: historyKey,\n        state: {\n            init() {\n                return new HistoryState(Branch.empty, Branch.empty, null, 0, -1);\n            },\n            apply(tr, hist, state) {\n                return applyTransaction(hist, state, tr, config);\n            }\n        },\n        config,\n        props: {\n            handleDOMEvents: {\n                beforeinput(view, e) {\n                    let inputType = e.inputType;\n                    let command = inputType == \"historyUndo\" ? undo : inputType == \"historyRedo\" ? redo : null;\n                    if (!command)\n                        return false;\n                    e.preventDefault();\n                    return command(view.state, view.dispatch);\n                }\n            }\n        }\n    });\n}\nfunction buildCommand(redo, scroll) {\n    return (state, dispatch) => {\n        let hist = historyKey.getState(state);\n        if (!hist || (redo ? hist.undone : hist.done).eventCount == 0)\n            return false;\n        if (dispatch) {\n            let tr = histTransaction(hist, state, redo);\n            if (tr)\n                dispatch(scroll ? tr.scrollIntoView() : tr);\n        }\n        return true;\n    };\n}\n/**\nA command function that undoes the last change, if any.\n*/\nconst undo = buildCommand(false, true);\n/**\nA command function that redoes the last undone change, if any.\n*/\nconst redo = buildCommand(true, true);\n/**\nA command function that undoes the last change. Don't scroll the\nselection into view.\n*/\nconst undoNoScroll = buildCommand(false, false);\n/**\nA command function that redoes the last undone change. Don't\nscroll the selection into view.\n*/\nconst redoNoScroll = buildCommand(true, false);\n/**\nThe amount of undoable events available in a given state.\n*/\nfunction undoDepth(state) {\n    let hist = historyKey.getState(state);\n    return hist ? hist.done.eventCount : 0;\n}\n/**\nThe amount of redoable events available in a given editor state.\n*/\nfunction redoDepth(state) {\n    let hist = historyKey.getState(state);\n    return hist ? hist.undone.eventCount : 0;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-history/dist/index.js\n");

/***/ })

};
;