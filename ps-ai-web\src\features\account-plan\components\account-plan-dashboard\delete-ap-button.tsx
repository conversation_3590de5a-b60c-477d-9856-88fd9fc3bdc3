"use client";

import { GridDeleteButton } from "@/components/ui/grid/grid-actions";
import { useDeleteAccountPlanGroups } from "../../api/account-plan-group/delete-account-plan-group";

export default function DeleteAccountPlanButton({
  accountGroupId,
}: {
  accountGroupId: number;
}) {
  const deleteAccountPlanGroup = useDeleteAccountPlanGroups({});

  const onDeleteAccountPlanGroup = async () => {
    await deleteAccountPlanGroup.mutateAsync({
      accountGroupId,
    });
  };

  return (
    <GridDeleteButton
      onDelete={onDeleteAccountPlanGroup}
      isLoading={deleteAccountPlanGroup.isPending}
      itemName="account plan"
    />
  );
}
