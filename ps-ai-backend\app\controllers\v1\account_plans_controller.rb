# frozen_string_literal: true

module V1
  class AccountPlansController < ApiController
    authorize_auth_token! :all

    rescue_from ActiveRecord::ConnectionNotEstablished, with: :handle_db_connection_error
    rescue_from ActiveRecord::StatementInvalid, with: :handle_db_connection_error
    rescue_from PG::ConnectionBad, with: :handle_db_connection_error

    def index
      result = service.index(query_params)

      render_json_array result.account_plans,
                        use: :format,
                        organization_users: result.organization_users,
                        account_plan_groups: result.account_plan_groups
    end

    def show
      result = service.show(params[:id])

      render_json result.account_plan,
                  use: :format,
                  organization_users: result.organization_users,
                  account_plan_groups: result.account_plan_groups
    end

    def create
      input = ::V1::AccountPlanCreationInput.new(request_body)
      validate! input, capture_failure: true

      result = service.create(input.output)

      render_json result.account_plan,
                  use: :format,
                  status: :created,
                  organization_users: result.organization_users
    end

    def update
      input = ::V1::AccountPlanUpdateInput.new(request_body)
      validate! input, capture_failure: true

      result = service.update(params[:id], input.output)

      render_json result.account_plan,
                  use: :format,
                  status: :ok,
                  organization_users: result.organization_users,
                  account_plan_groups: result.account_plan_groups
    end

    def destroy
      service.destroy(params[:id])

      render_empty_json({}, status: :ok)
    end

    def active_top_actions
      result = service.active_top_actions

      render_json_array result.active_account_plans,
                        use: :active_top_action_format,
                        ap_tables: result.ap_tables,
                        top_actions: result.top_actions
    end

    private

    def default_output
      ::V1::AccountPlanOutput
    end

    def service
      @service ||= ::AccountPlanService.new(current_user_data)
    end

    def handle_db_connection_error(error)
      Rails.logger.error("[DB CONNECTION ERROR] #{error.class} - #{error.message}")
      render json: {
        error: "The service is temporarily unavailable. Please try again shortly."
      }, status: :service_unavailable
    end
  end
end
