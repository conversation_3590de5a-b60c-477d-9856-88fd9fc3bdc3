class CreateCurrencyExchangeRates < ActiveRecord::Migration[7.0]
  def change
    create_table :currency_exchange_rates do |t|
      t.string :base_currency, :index => true
      t.string :exchange_currency, :index => true
      t.integer :rates
      t.integer :rates_decimal
      t.datetime :discarded_at, :index => true

      t.timestamps
    end

    add_index :currency_exchange_rates, [:base_currency, :exchange_currency],
              :name => "base_exhange_currency_unique_index",
              :unique => true
  end
end
