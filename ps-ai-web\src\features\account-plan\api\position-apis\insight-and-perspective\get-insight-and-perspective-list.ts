import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APInsightAndPerspective } from "@/features/account-plan/types/position-types";

type InsightAndPerspectiveListParams = BaseParams;

export const getInsightAndPerspectiveList = ({
  accountId,
  params,
}: {
  accountId: number;
  params?: InsightAndPerspectiveListParams;
}): ApiResponse<APInsightAndPerspective[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE(accountId), {
    params,
  });
};

export const getInsightAndPerspectiveListQueryOptions = (
  accountId: number,
  params?: InsightAndPerspectiveListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE,
    ],
    queryFn: () => getInsightAndPerspectiveList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseInsightAndPerspectiveListOptions = {
  params?: InsightAndPerspectiveListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getInsightAndPerspectiveList>;
  options?: Partial<
    ReturnType<typeof getInsightAndPerspectiveListQueryOptions>
  >;
};

export const useInsightAndPerspectiveList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UseInsightAndPerspectiveListOptions) => {
  const insightAndPerspectiveListQuery = useQuery({
    ...getInsightAndPerspectiveListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...insightAndPerspectiveListQuery,
    insightAndPerspectiveList: insightAndPerspectiveListQuery.data?.data,
  };
};
