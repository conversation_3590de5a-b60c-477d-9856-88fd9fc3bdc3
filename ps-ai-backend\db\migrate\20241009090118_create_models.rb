class CreateModels < ActiveRecord::Migration[7.0]
  def change
    create_table :models do |t|
      t.references :model_template
      t.string :model
      t.string :response_format_type
      t.string :response_format_json_schema
      t.string :openai_assistant_id
      t.text :structured_prompt
      t.string :openai_assistant_vector_store_id
      t.datetime :discarded_at, :index => true

      t.timestamps
    end
  end
end
