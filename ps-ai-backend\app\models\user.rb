# frozen_string_literal: true

class User < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }
  has_secure_password

  enum status: string_enum('active', 'archived')

  has_many :organization_users
  has_many :organizations, through: :organization_users
  has_many :roles, through: :organization_users

  validates_presence_of :name, :email, :password_digest

  def authenticate(unencrypted_password)
    BCrypt::Password.new(password_digest).is_password?(unencrypted_password) && self
  end
end
