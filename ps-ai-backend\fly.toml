# fly.toml app configuration file generated for psai-api-wispy-resonance-3660 on 2025-04-21T22:47:46+07:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'psai-api-wispy-resonance-3660'
primary_region = 'sin'
console_command = '/rails/bin/rails console'

[build]

[processes]
  app = './bin/rails server'
  worker = 'bundle exec sidekiq'

[[mounts]]
  source = 'psai1'
  destination = '/data'

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = 'off'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  size = 'shared-cpu-1x'
  memory = '2gb'
  cpu_kind = 'shared'
  cpus = 1

[[statics]]
  guest_path = '/rails/public'
  url_prefix = '/'
