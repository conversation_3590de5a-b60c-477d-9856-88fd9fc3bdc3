# frozen_string_literal: true

module Repositories
  class Basic
    include Enumerable
    def initialize(scope = default_scope, options = nil, includes = nil)
      if scope.is_a?(Hash) && !includes
        includes = options
        options = scope
        scope = default_scope
      end

      @options = Hash(default_options.merge(options.to_h)).with_indifferent_access
      @includes = Array(includes)
      @scope = scope
      apply_filters(@options)
      apply_includes(@includes)
    end

    def initialize_copy(repo)
      super
      @scope = repo.scope
      @options = repo.options
      @includes = repo.includes
    end

    def scope
      @scope.clone
    end

    def options
      @options.clone
    end

    def includes
      @includes.clone
    end

    def filter(options)
      new_repo = clone
      new_repo.instance_variable_set('@options', @options.merge(options))
      new_repo.send(:apply_filters, options)
      new_repo
    end

    def include(*associations)
      new_repo = clone
      new_repo.instance_variable_set('@includes', @includes | associations)
      new_repo.send(:apply_includes, associations)
      new_repo
    end

    def first
      load(@scope.reverse_order.reverse_order, 1)[0]
    end

    def last
      load(@scope.reverse_order, 1)[0]
    end

    def exists?
      @scope.exists?
    end

    def count(*args)
      @scope.count(*args)
    end

    def each(&block)
      load(@scope).each(&block)
    end

    def inspect
      "#<#{self.class} @options=#{@options}>"
    end

    private

    def update_scope(scope)
      new_repo = clone
      new_repo.instance_variable_set('@scope', scope)
      new_repo
    end

    def load(scope, limit = nil)
      return scope.limit(limit) if limit

      scope
    end

    def default_scope
      raise 'default_scope not implemented'
    end

    def default_options
      {}
    end

    def apply_filters(options)
      options.each do |key, value|
        method = "filter_by_#{key}"

        @scope = send(method, value) || @scope if respond_to?(method, true)
      end
    end

    def apply_includes(associations)
      associations.each do |value|
        method = "include_#{value}"
        @scope = send(method) || @scope
      end
    end
  end
end
