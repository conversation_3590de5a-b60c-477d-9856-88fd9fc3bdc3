# frozen_string_literal: true
# TODO for tiered organization

class ModelTemplateService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
    @openai_service = OpenaiService.new(user_data)
  end

  def index(query_params)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    templates = ::ModelTemplates.new

    filter = query_params.slice(
      :list_template_ap_category, :id, :template_category_id, :status,
      :search, :disable_pagination, :page, :per_page
    )

    if @organization.tier == 'superuser'
      # get global template if superuser tier
      filter = filter.merge(
        organization_id: nil
      )
    else
      filter = filter.merge(
        organization_id: organization_user.organization_id
      )
    end

    filtered_result_templates = templates.filter(filter)

    OpenStruct.new(
      templates: filtered_result_templates,
      variables: ModelTemplateVariable.where(model_template_id: filtered_result_templates.pluck(:id)),
      template_categories: TemplateCategory.where(id: filtered_result_templates.pluck(:template_category_id).compact.uniq),
      organization_users: OrganizationUser.where(id: filtered_result_templates.pluck(:last_updated_by_organization_user_id).compact.uniq),
      account_plan_groups: AccountPlanGroup.where(id: filtered_result_templates.pluck(:account_plan_group_id).compact.uniq)
    )
  end

  def show(id)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    # get global template if superuser tier
    organization_id = if @organization.tier == 'superuser'
                        nil
                      else
                        organization_user.organization_id
                      end
    template = ModelTemplate.find(id)
    authorize! template_ownership(organization_user, template, @organization)

    OpenStruct.new(
      template: template,
      variables: ModelTemplateVariable.where(model_template_id: template.id),
      template_categories: TemplateCategory.where(id: template.template_category_id),
      # input_sets: ModelTemplateInputSet.where(model_template_id: template.id)
    )
  end

  def create(params)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    # Create global template if superuser tier
    organization_id = if @organization.tier == 'superuser'
      nil
    else
      organization_user.organization_id
    end

    params[:organization_id] = organization_id
    params[:creator_organization_user_id] = organization_user.id
    params[:last_updated_by_organization_user_id] = organization_user.id

    authorize! params.has_key?(:template_category_id), on_error: 'No template category provided'
    template_category = TemplateCategory.find_by!(id: params[:template_category_id], organization_id: nil)

    if params.has_key?(:inputs)
      assert! params[:inputs].is_a?(Array) && params[:inputs].size > 0,
              on_error: 'Input(s) can not be empty'

      assert! params[:inputs].all? { |i| ModelTemplate.valid_inputs.include?(i) },
              on_error: 'Input(s) invalid'
    else
      raise Invalid, 'Input(s) can not be empty'
    end

    if params.has_key?(:name)
      assert! params[:name].present?, on_error: 'Version Template Name can not be empty'

      assert! !ModelTemplate.where(organization_id: organization_id, name: params[:name], template_category_id: template_category.id).any?,
              on_error: 'This version name already exists. Please choose a different name.'
    else
      raise Invalid, 'Version Template Name can not be empty'
    end

    template = ModelTemplate.new

    ActiveRecord::Base.transaction do
      if params.has_key?(:status)
        if params[:status] == 'active'
          ModelTemplate.where(template_category_id: template_category.id, organization_id: params[:organization_id])
                       .update_all(status: 'inactive')
        else
          params[:status] = 'inactive'
        end
      else
        params[:status] = 'inactive'
      end

      template = ModelTemplate.create!(params)

      result_rules = build_rules_from_template(template)

      # TODO remove vector store completely, just upload directly when thread created to save cost
      # TODO delegate to job
      # create vector store
      vs_response = @openai_service.create_vector_store("Vector Store Template: #{template.name}-#{template.id}")
      tool_resources = {
        file_search: {
          vector_store_ids: [vs_response['id']]
        }
      }

      # Upload file to OpenAI assistant if file present
      file_url = template.reference_output_url
      if file_url.present?
        response_file = @openai_service.create_file(file_url, 'assistants')
        
        OpenaiFile.create!(
          object_id: template.id,
          object_class: template.class.name,
          object_class_column: 'reference_output_url',
          openai_file_id: response_file['id']
        )

        @openai_service.create_assistant_files(
          vs_response['id'],
          mode: 'openai_file_ids',
          openai_file_ids: [response_file['id']]
        )
      end

      # create model with assistants
      list_models = Model.models.values
      list_models.each do |m|
        model = Model.create!(
          model_template_id: template.id,
          model: m,
          response_format_type: 'text',
          response_format_json_schema: nil,
          structured_prompt: result_rules[:structured_prompt],
          openai_assistant_vector_store_id: nil# vs_response['id']
        )

        # create assistant
        assistant_params = {
          model: model.model,
          name: "#{template.name} #{model.model}",
          temperature: template.temperature,
          instructions: model.structured_prompt,
          response_format_type: model.response_format_type
        }

        assistant_response = @openai_service.create_assistant(assistant_params)
        model.update!(openai_assistant_id: assistant_response['id'])
      end
    end

    OpenStruct.new(
      template: template,
      variables: ModelTemplateVariable.where(model_template_id: template.id),
      template_categories: TemplateCategory.where(id: template.template_category_id)
    )
  end

  def update(id, params, bypass_verification: false)
    template = ModelTemplate.find(id)
    template_category = template.template_category

    organization_id = if @organization.tier == 'superuser'
      nil
    else
      @organization.id
    end

    unless bypass_verification
      verify_organization_tier(['superuser'], @organization)
      organization_user = verify_user_organization(@user, @organization_user, @organization)
      verify_roles(['owner'], organization_user)

      params[:last_updated_by_organization_user_id] = organization_user.id

      if params.has_key?(:inputs)
        assert! params[:inputs].is_a?(Array) && params[:inputs].size > 0,
                on_error: 'Input(s) can not be empty'

        assert! params[:inputs].all? { |i| ModelTemplate.valid_inputs.include?(i) },
                on_error: 'Input(s) invalid'
      end

      if params.has_key?(:name)
        assert! !ModelTemplate.where(organization_id: organization_id, name: params[:name], template_category_id: template_category.id)
                            .where.not(id: template.id)
                            .any?,
                on_error: 'This version name already exists. Please choose a different name.'
      end

      authorize! template_ownership(organization_user, template, @organization)
    end

    params.delete(:organization_id)
    params.delete(:creator_organization_user_id)
    params.delete(:template_category_id)

    ActiveRecord::Base.transaction do
      curr_status = template.status
      if params.has_key?(:status) && curr_status != params[:status]
        other_category_templates = ModelTemplate.where(
                                                  template_category_id: template_category.id,
                                                  organization_id: organization_id
                                                )
                                                .where.not(id: template.id)
                                                .order(updated_at: :desc)
        if params[:status] == 'active'
          other_category_templates.update_all(status: 'inactive')
        elsif params[:status] == 'inactive'
          other_template = other_category_templates.first
          assert! other_template.present?, on_error: "Cannot deactivate. Need at least one active #{template_category.name} template!"
          other_template.update(status: 'active')
        end
      end

      current_file_url = template.reference_output_url

      template.update!(params)

      result_rules = build_rules_from_template(template)

      # TODO remove vector store completely, just upload directly when thread created to save cost
      # TODO delegate to job
      # update model & assistant
      list_models = Model.models.values
      to_be_create_assistant_model = []
      tool_resources = {
        file_search: {
          vector_store_ids: []
        }
      }

      models = Model.where(model_template_id: template.id)
      models.each do |model|
        model.update(
          structured_prompt: result_rules[:structured_prompt]
        )

        assistant_params = {
          model: model.model,
          name: "#{template.name} #{model.model}",
          temperature: template.temperature,
          instructions: model.structured_prompt,
          response_format_type: model.response_format_type
        }

        begin
          assistant_response = @openai_service.retrieve_assistant(model.openai_assistant_id)
          assistant_params[:openai_assistant_id] = assistant_response['id']
          tool_resources = assistant_response['tool_resources'].with_indifferent_access

          @openai_service.modify_assistant(assistant_params)
        rescue Faraday::ResourceNotFound
          assistant_params[:model_id] = model.id
          to_be_create_assistant_model << assistant_params
        rescue TypeError
          assistant_params[:model_id] = model.id
          to_be_create_assistant_model << assistant_params
        end
      end

      if tool_resources.blank? || tool_resources[:file_search][:vector_store_ids].empty?
        vs_response = @openai_service.create_vector_store("Vector Store Template: #{template.name}-#{template.id}")
        tool_resources = {
          file_search: {
            vector_store_ids: [vs_response['id']]
          }
        }
      end

      # Create assistant model if model does not have assistant
      to_be_create_assistant_model.each do |assistant_params|
        assistant_response = @openai_service.create_assistant(assistant_params)

        Model.find(assistant_params[:model_id])
             .update!(openai_assistant_id: assistant_response['id'])
      end

      not_created_models = list_models - models.pluck(:model).uniq.compact
      if !not_created_models.empty?
        not_created_models.each do |m|
          model = Model.create!(
            model_template_id: template.id,
            model: m,
            response_format_type: 'text',
            response_format_json_schema: nil,
            structured_prompt: result_rules[:structured_prompt],
            openai_assistant_vector_store_id: nil # vs_response['id']
          )

          # create assistant
          assistant_params = {
            model: model.model,
            name: "#{template.name} #{model.model}",
            temperature: template.temperature,
            instructions: model.structured_prompt,
            response_format_type: model.response_format_type,
            response_format_json_schema: model.response_format_json_schema
          }

          assistant_response = @openai_service.create_assistant(assistant_params)
          model.update!(openai_assistant_id: assistant_response['id'])
        end
      end

      latest_file_url = template.reference_output_url
      # Update OpenAI file in vector store if variable reference is changed
      if current_file_url != latest_file_url
        openai_file = OpenaiFile.find_by(
          object_id: template.id,
          object_class: template.class.name,
          object_class_column: 'reference_output_url'
        )
        if openai_file.present?
          begin
            response_file = @openai_service.delete_file(openai_file.openai_file_id)
            if response_file['deleted']
              openai_file.discard!
            end
          rescue Faraday::ResourceNotFound
            response_file = {}
            openai_file.discard!
          end
        end

        # TODO error handling
        if latest_file_url.present?
          response_file = @openai_service.create_file(latest_file_url, 'assistants')

          OpenaiFile.create!(
            object_id: template.id,
            object_class: template.class.name,
            object_class_column: 'reference_output_url',
            openai_file_id: response_file['id']
          )

          begin
            vector_store_id = tool_resources['file_search']['vector_store_ids'].first
          rescue NoMethodError
            vector_store_id = nil
          end
          

          @openai_service.create_assistant_files(
            vector_store_id,
            mode: 'openai_file_ids',
            openai_file_ids: [response_file['id']]
          )
        end
      end
    end

    OpenStruct.new(
      template: template,
      variables: ModelTemplateVariable.where(model_template_id: template.id),
      template_categories: TemplateCategory.where(id: template.template_category_id)
    )
  end

  def destroy(id)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    organization_id = if @organization.tier == 'superuser'
      nil
    else
      @organization.id
    end

    template = ModelTemplate.find(id)
    authorize! template_ownership(organization_user, template, @organization)

    time_current = Time.current
    models = Model.where(model_template_id: template.id)

    template_category = template.template_category

    ActiveRecord::Base.transaction do
      if template.status == 'active'
        other_template = ModelTemplate.where(
                                        template_category_id: template_category.id,
                                        organization_id: organization_id
                                      )
                                      .where.not(id: template.id)
                                      .order(updated_at: :desc)
                                      .first
        
        assert! !template_category.general_category || other_template.present?, on_error: "Cannot delete. Need at least one active #{template_category.name} template!"
        other_template&.update(status: 'active')
      end

      # TODO remove vector store completely, just upload directly when thread created to save cost
      # TODO delegate to job
      # Delete model, assistant, vector store, all files
      models.each do |model|
        assistant_id = model.openai_assistant_id

        if assistant_id.present?
          begin
            response_assistant = @openai_service.retrieve_assistant(assistant_id)
          rescue Faraday::ResourceNotFound
            response_assistant = {}
          end

          begin
            vector_store_id = response_assistant['tool_resources']['file_search']['vector_store_ids'].first
          rescue NoMethodError
            vector_store_id = nil
          rescue TypeError
            vector_store_id = nil
          end

          if vector_store_id
            response_vector_store_files = @openai_service.list_vector_store_files(vector_store_id)
            openai_file_ids = response_vector_store_files['data'].collect { |file| file['id'] }

            @openai_service.delete_vector_store(vector_store_id)
            OpenaiFile.where(openai_file_id: openai_file_ids).update_all(discarded_at: time_current)
          end

          begin
            @openai_service.delete_assistant(assistant_id)
            assistant_deleted = true
          rescue Faraday::ResourceNotFound
            assistant_deleted = true
          end
        end

        model.update(discarded_at: time_current, openai_assistant_id: nil)
      end

      # Delete reference output file
      openai_file = OpenaiFile.find_by(
        object_id: template.id,
        object_class: template.class.name,
        object_class_column: 'reference_output_url'
      )
      if openai_file.present?
        begin
          response_file = @openai_service.delete_file(openai_file.openai_file_id)
        rescue Faraday::ResourceNotFound
          response_file = {}
        end

        openai_file.discard!
      end

      template_variables = ModelTemplateVariable.where(model_template_id: template.id)
      OpenaiFile.where(
        object_id: template_variables.ids,
        object_class: ModelTemplateVariable.name,
        object_class_column: 'variable_reference_url'
      ).update_all(discarded_at: time_current)
      template_variables.update_all(discarded_at: time_current)

      template.update(
        last_updated_by_organization_user_id: organization_user.id,
        discarded_at: time_current
      )
    end
  end

  def build_rules_from_template(model_template)
    rules = model_template.rules
    reference_output = model_template.reference_output
    reference_output_url = model_template.reference_output_url
    reference_output_filename = reference_output_url.present? ? url_to_filename(reference_output_url) : ''

    given_data_text = "and given data:\n"
    rules_text = "Now, based on the above reference and the following instructions:\n"
    reference_output_text = "Example Reference Output:\n"
    final_output_text = "Please generate a response that adheres closely to the format and style of the reference output."
    
    structured_prompt = "With @input as 'message' from user message\n\n"
    
    variables = ModelTemplateVariable.where(model_template_id: model_template.id)
    ap_table_types = variables.pluck(:ap_table_type).uniq.compact
    variable_ids = variables.ids

    ap_table_types.each do |att|
      current_vars = variables.select { |v| v.ap_table_type == att }

      filenames = []

      current_vars.each do |v|
        variable_ids = variable_ids - [v.id]
        rules = rules.gsub("<@V#{v.id}>", "@#{att}_items")

        variable_reference_url = v.variable_reference_url
        if variable_reference_url.present?
          variable_reference_filename = url_to_filename(variable_reference_url)
          filenames << "'#{variable_reference_filename}'"
        end
      end

      given_data_text += "@#{att}_items as '#{att}_data' from user message"

      if !filenames.empty?
        str_filenames = filenames.join(', ')
        given_data_text += " with file references: #{str_filenames}"
      end

      given_data_text += "\n"
    end

    variable_ids.each do |vid|
      current_var = variables.find { |v| v.id == vid }

      value = ''
      if current_var.description.present?
        value = "#{value} (#{current_var.description})".strip
      else
        value = "#{value} (#{current_var.name})".strip
      end

      variable_reference_url = current_var.variable_reference_url
      if variable_reference_url.present?
        variable_reference_filename = url_to_filename(variable_reference_url)
        value += " with file reference: '#{variable_reference_filename}'"
      end

      rules = rules.gsub("<@V#{current_var.id}>", value)
    end

    rules = rules.gsub('<@VInput>', '@input')
    rules = rules.gsub('<@Vinput>', '@input')

    structured_prompt += given_data_text + "\n"

    if reference_output.present?
      structured_prompt += reference_output_text + reference_output + "\n\n"
    end

    if reference_output_filename.present?
      structured_prompt += " with file reference: '#{reference_output_filename}'\n\n"
    end

    structured_prompt += rules_text + rules + "\n\n" + final_output_text

    result = {
      structured_prompt: structured_prompt
    }
  end

  private

  def template_category_ownership(organization_user, template_category)
    organization_user.organization_id == template_category.organization_id
  end

  def template_ownership(organization_user, template, organization)
    if organization.tier == 'superuser'
      template.organization_id.nil? || organization_user.organization_id == template.organization_id
    else
      organization_user.organization_id == template.organization_id
    end
  end

  def url_to_filename(url)
    uri = URI.parse(url)
    File.basename(uri.path)
  end
end
