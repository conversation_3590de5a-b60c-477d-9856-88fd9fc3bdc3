# frozen_string_literal: true

module V1
  class AccountPlanTableItems::ApMissingInformationItemsController < ApiController
    authorize_auth_token! :all

    def index
      account_plan_id = params[:account_plan_id]
      result = service.index(account_plan_id, query_params)

      render_json_array result.ap_missing_information_items,
                        use: :format
    end

    def show
      account_plan_id = params[:account_plan_id]
      result = service.show(account_plan_id, params[:id])

      render_json result.ap_missing_information_item,
                  use: :format
    end

    def create
      account_plan_id = params[:account_plan_id]
      input = ::V1::AccountPlanTableItems::ApMissingInformationItemCreationInput.new(request_body)
      validate! input, capture_failure: true

      result = service.create(account_plan_id, input.output)

      render_json result.ap_missing_information_item,
                  use: :format,
                  status: :created
    end

    def update
      account_plan_id = params[:account_plan_id]
      input = ::V1::AccountPlanTableItems::ApMissingInformationItemUpdateInput.new(request_body)
      validate! input, capture_failure: true

      result = service.update(account_plan_id, params[:id], input.output)

      render_json result.ap_missing_information_item,
                  use: :format,
                  status: :ok
    end

    def destroy
      account_plan_id = params[:account_plan_id]
      service.destroy(account_plan_id, params[:id])

      render_empty_json({}, status: :ok)
    end

    def generate
      account_plan_id = params[:account_plan_id]
      input = ::V1::AccountPlanTableItems::ApMissingInformationItemGenerateInput.new(request_body)
      validate! input, capture_failure: true
      service.generate(account_plan_id, input.output)

      render_empty_json({}, status: :ok)
    end

    private

    def default_output
      ::V1::AccountPlanTableItems::ApMissingInformationItemOutput
    end

    def service
      @service ||= ::AccountPlanTableItems::ApMissingInformationItemService.new(current_user_data)
    end
  end
end
