RSpec.describe ::V1::TemplateCategoriesController, type: :request do
  let(:user) do
    User.create(
      name: 'Test',
      email: '<EMAIL>',
      password: '12345678'
    )
  end
  let(:organization) do
    Organization.create(name: 'Test')
  end
  let(:organization_user) do
    OrganizationUser.create(
      organization_id: organization.id,
      user_id: user.id
    )
  end

  def create_template_category(params, user)
    user_token = JsonWebToken.encode({ user_id: user.id })

    headers = {
      "CONTENT_TYPE" => "application/json",
      "Authorization" => "bearer #{user_token}"
    }

    post "/v1/template_categories", headers: headers, params: params.to_json
  end

  describe "POST template category" do
    before do
      # init data
      user
      organization
      organization_user
    end

    let!(:params) do
      {
        name: 'New Cat'
      }
    end

    it "return created with auto assign organization when data valid" do
      create_template_category(params, user)
      expect(response.status).to eq 201
      
      response_body = JSON.parse(response.body).with_indifferent_access
      response_data = response_body[:data]

      expect(response_data).to have_key(:id)
      expect(response_data[:name]).to eq params[:name]
      expect(response_data[:organization_id]).to eq organization.id
    end

    context "when user isn't in organization" do
      before do
        organization_user.discard
      end
      
      it "return unauthorized" do
        create_template_category(params, user)
        expect(response.status).to eq 403
      end
    end
  end
end