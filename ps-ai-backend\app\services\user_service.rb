# frozen_string_literal: true

class UserService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
  end

  def show(id, query_params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner', 'super_admin'], organization_user)

    user = User.find_by(id: id)
    exist! user.present?, on_error: 'User not found'

    organization_id = query_params.delete(:organization_id) || organization_user.organization_id
    if organization_user.organization_id != organization_id
      verify_organization_tier(['superuser'], organization_user.organization)
      verify_roles(['owner'], organization_user)
    end

    org_user = OrganizationUser.find_by(user_id: user.id, organization_id: organization_id)
    exist! org_user.present?, on_error: 'User not found'

    OpenStruct.new(
      user: user,
      organization_user: org_user,
      organization: org_user.organization
    )
  end

  def update(id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)

    to_be_updated_user = User.find_by(id: id)
    exist! to_be_updated_user.present?, on_error: 'User not found'

    to_be_updated_organization_user = organization_user

    organization_id = params.delete(:organization_id) || organization_user.organization_id
    if organization_user.organization_id != organization_id
      verify_organization_tier(['superuser'], organization_user.organization)
      verify_roles(['owner'], organization_user)
    end

    ActiveRecord::Base.transaction do
      if to_be_updated_user.id == organization_user.user_id
        status = params.delete(:status)

        if params[:password]
          assert! to_be_updated_user.authenticate(params[:current_password]), on_error: 'Invalid current password. Try again'
        end
        
        to_be_updated_organization_user = OrganizationUser.find_by(user_id: id, organization_id: organization_id)
        exist! to_be_updated_organization_user.present?, on_error: 'User not found'

        to_be_updated_user.update!(params.except(:current_password))

        # can only update to archived
        if status.present? && status == 'archived'
          to_be_updated_organization_user.update(status: status)
        end
      else
        # can't update target user's password
        params.delete(:password)
        params.delete(:current_password)

        status = params.delete(:status)

        # only owner (Super Admin) & super_admin  (Organization Admins) can edit other user data
        verify_roles(['owner', 'super_admin'], organization_user)

        to_be_updated_organization_user = OrganizationUser.find_by(user_id: id, organization_id: organization_id)
        exist! to_be_updated_organization_user.present?, on_error: 'User not found'

        authorize! (Organization.role_hierarchy[to_be_updated_organization_user.role&.name] || Organization.min_role_subs) > 
                      (Organization.role_hierarchy[organization_user.role&.name] || Organization.max_role_subs),
                  on_error: 'Cannot update with higher role than your role'

        to_be_updated_user.update!(params)
        
        if status.present?
          to_be_updated_organization_user.update(status: status)
        end
      end
    end

    OpenStruct.new(
      user: to_be_updated_user,
      organization_user: to_be_updated_organization_user,
      organization: to_be_updated_organization_user.organization
    )
  end

  def destroy(id, query_params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner', 'super_admin'], organization_user)

    organization_id = query_params.delete(:organization_id) || organization_user.organization_id
    if organization_user.organization_id != organization_id
      verify_organization_tier(['superuser'], organization_user.organization)
      verify_roles(['owner'], organization_user)
    end

    to_be_deleted_user = User.find_by(id: id)
    exist! to_be_deleted_user.present?, on_error: 'User not found'

    to_be_deleted_organization_user = OrganizationUser.find_by(user_id: id, organization_id: organization_id)
    exist! to_be_deleted_organization_user.present?, on_error: 'User not found'

    authorize! to_be_deleted_organization_user.user_id == organization_user.user_id || (Organization.role_hierarchy[to_be_deleted_organization_user.role&.name] || Organization.min_role_subs) > 
                 (Organization.role_hierarchy[organization_user.role&.name] || Organization.max_role_subs),
               on_error: 'Cannot delete with higher role than your role'

    ActiveRecord::Base.transaction do
      to_be_deleted_organization_user.discard!
    end
  end
end
