# frozen_string_literal: true

class IndustryService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
  end

  def create(params)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    Industry.create!(params)
  end

  def update(id, params)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    industry = Industry.find(id)

    industry.update(params)

    industry
  end

  def index(query)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    
    industries = ::Industries.new

    ap_active_filter = query.delete(:ap_active)
    filter = query.slice(:id, :search, :page, :per_page)
    filter[:disable_pagination] = true # force disable pagination
    filter[:sort_column] = 'name'
    filter[:sort_direction] = 'asc'

    if ap_active_filter
      # get by @organization_user.organization_id just to double make sure its from current login user's organization 
      apg_active_ids = AccountPlan.where(organization_id: @organization_user.organization_id, status: 'active').pluck(:account_plan_group_id).compact.uniq
      industry_ids = AccountPlanGroup.where(id: apg_active_ids).pluck(:industry_id).compact.uniq
      filter[:id] = industry_ids
    end

    industries.filter(filter)
  end

  def destroy(id)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    industry = Industry.find(id)

    industry.discard
  end
end
