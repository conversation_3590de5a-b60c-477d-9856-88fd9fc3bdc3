import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { UserBaseParams } from "../../types/user";

type DeleteUserPayload = UserBaseParams;

export const deleteUser = ({
  userId,
  params,
}: {
  userId: number;
  params?: DeleteUserPayload;
}): ApiResponse => {
  return api.delete(API_ROUTES.USERS_DETAIL(userId), { params });
};

type UseDeleteUserOptions = {
  mutationConfig?: MutationConfig<typeof deleteUser>;
};

export const useDeleteUser = ({ mutationConfig }: UseDeleteUserOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.USER_MANAGEMENTS],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteUser,
  });
};
