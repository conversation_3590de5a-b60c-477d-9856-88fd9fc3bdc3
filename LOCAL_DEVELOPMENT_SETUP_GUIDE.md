# Complete Local Development Setup Guide

## 🎯 Overview

This guide will help you set up a complete local development environment for the PS-AI application, allowing you to develop both frontend and backend locally without depending on the hosted Fly.io backend.

## 📋 Current Architecture vs Target Architecture

### **Current (Hosted Backend Dependency)**
```
Frontend (Local) → Backend (Fly.io) → Database (Fly.io)
     ↓                    ↓                ↓
localhost:3000    psai-api-wispy-resonance-3660    perceptionselling-prod-db
```

### **Target (Fully Local Development)**
```
Frontend (Local) → Backend (Local) → Database (Local)
     ↓                    ↓                ↓
localhost:3000      localhost:8000      localhost:5432
```

## 🛠️ Technology Stack Requirements

### **Backend Requirements**
- **Ruby**: 3.1.2
- **Rails**: 7.0.4+ (API-only)
- **Database**: PostgreSQL
- **Background Jobs**: Sidekiq + Redis
- **Authentication**: JWT tokens
- **File Storage**: Local (development) / AWS S3 (production)

### **Frontend Requirements**
- **Node.js**: Latest LTS
- **Next.js**: React framework
- **TypeScript**: Type safety

## 📦 Prerequisites Installation

### **1. Install Ruby 3.1.2**

#### **Windows (using RubyInstaller)**
```bash
# Download and install Ruby 3.1.2 from rubyinstaller.org
# Or use Chocolatey
choco install ruby --version=3.1.2
```

#### **Using rbenv (recommended)**
```bash
# Install rbenv first, then:
rbenv install 3.1.2
rbenv local 3.1.2
ruby --version  # Should show 3.1.2
```

### **2. Install PostgreSQL**

#### **Option A: Native Installation**
```bash
# Download from postgresql.org and install
# Default port: 5432
# Remember your postgres user password
```

#### **Option B: Docker (Recommended)**
```bash
# Run PostgreSQL in Docker
docker run --name postgres-psai \
  -e POSTGRES_PASSWORD=password \
  -e POSTGRES_DB=psai_api_development \
  -p 5432:5432 \
  -d postgres:14

# Verify it's running
docker ps
```

### **3. Install Redis**

#### **Option A: Native Installation**
```bash
# Download from redis.io
# Or use Chocolatey on Windows
choco install redis-64
```

#### **Option B: Docker (Recommended)**
```bash
# Run Redis in Docker
docker run --name redis-psai \
  -p 6379:6379 \
  -d redis:7-alpine

# Verify it's running
docker ps
```

### **4. Install Node.js**
```bash
# Download from nodejs.org (LTS version)
# Or use nvm
nvm install --lts
nvm use --lts
```

## 🔧 Backend Setup

### **Step 1: Navigate to Backend Directory**
```bash
cd ps-ai-backend
```

### **Step 2: Install Ruby Dependencies**
```bash
# Install bundler
gem install bundler

# Install all gems
bundle install
```

### **Step 3: Create Environment Configuration**
```bash
# Copy example environment file
cp .env-example .env.local
```

### **Step 4: Configure Environment Variables**
Edit `.env.local` with the following configuration:

```bash
# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/psai_api_development

# CORS Configuration (Allow local frontend)
ALLOWED_CORS_ORIGIN=http://localhost:3000 http://127.0.0.1:3000

# Frontend URL
WEBAPP=http://localhost:3000

# Session Secret (generate with: ruby -e "require 'securerandom'; puts SecureRandom.hex(32)")
SESSION_COOKIE_SECRET=your-generated-32-character-hex-string-here

# Email Configuration (Development - uses letter_opener)
MAILER_SMTP_SERVER=localhost
MAILER_SMTP_PORT=1025
MAILER_EMAIL_ADDRESS=dev@localhost
CUSTOMER_SERVICE_EMAIL_ADDRESS=support@localhost

# File Storage (Local development)
# S3 settings can be left empty for local development

# Optional: External APIs (can be left empty for basic development)
EXCHANGERATE_HOSTNAME=https://v6.exchangerate-api.com/v6
EXCHANGERATE_ACCESS_KEY=your-key-here
EXCHANGERATE_BASE_CURRENCY=USD

# Optional: OpenAI (if using AI features)
BACKEND_GLOBAL_OPENAI_ASSISTANT_ID=asst_your-assistant-id

# Webhook Access Key (generate random string)
WEBHOOK_ACCESS_KEY=your-webhook-access-key
```

### **Step 5: Database Setup**
```bash
# Create databases
rails db:create

# Run migrations
rails db:migrate

# Run seeds (if any)
rails db:seed
```

### **Step 6: Create Test Data**
Since the seeds file is empty, create test data manually:

```bash
# Open Rails console
rails console

# Create test organization
org = Organization.create!(
  name: 'Local Development Org',
  unique_id: 'LocalDev',
  code: 'DEV001'
)

# Create test user
user = User.create!(
  name: 'Test Developer',
  email: '<EMAIL>',
  password: 'password123'
)

# Link user to organization
org_user = OrganizationUser.create!(
  user: user,
  organization: org,
  role: 'owner'
)

puts "Created test user: #{user.email}"
puts "Organization ID: #{org.unique_id}"
puts "Password: password123"

# Exit console
exit
```

### **Step 7: Start Backend Server**
```bash
# Start Rails server on port 8000 (to match frontend expectation)
rails server -p 8000

# You should see:
# => Booting Puma
# => Rails 7.0.x application starting in development
# => Run `bin/rails server --help` for more startup options
# Puma starting in single mode...
# * Listening on http://127.0.0.1:8000
```

### **Step 8: Start Background Jobs (Optional)**
In a new terminal:
```bash
cd ps-ai-backend
bundle exec sidekiq
```

## 🎨 Frontend Setup

### **Step 1: Navigate to Frontend Directory**
```bash
cd ps-ai-web
```

### **Step 2: Install Dependencies**
```bash
npm install
```

### **Step 3: Configure Environment**
Edit `.env.local` to point to your local backend:

```bash
# Backend API URL - Point to local backend
NEXT_PUBLIC_API_URL=http://localhost:8000/v1

# Development environment flag
NODE_ENV=development

# Optional: Sentry error tracking (can be left empty)
SENTRY_AUTH_TOKEN=
```

### **Step 4: Start Frontend Server**
```bash
npm run dev

# You should see:
# ▲ Next.js 14.x.x
# - Local:        http://localhost:3000
# - Environments: .env.local
# ✓ Ready in 2.1s
```

## 🧪 Testing the Setup

### **Step 1: Verify Backend is Running**
Open browser and go to: `http://localhost:8000/version`

You should see a JSON response with version information.

### **Step 2: Test API Endpoints**
```bash
# Test auth endpoint
curl -X GET http://localhost:8000/v1/auth

# Should return 401 Unauthorized (expected for unauthenticated request)
```

### **Step 3: Test Frontend Login**
1. Open browser and go to: `http://localhost:3000`
2. Use the test credentials you created:
   - **Organization ID**: `LocalDev`
   - **Email**: `<EMAIL>`
   - **Password**: `password123`
3. Login should work and redirect to dashboard

## 🔄 Development Workflow

### **Daily Development Routine**

#### **Starting Development Session**
```bash
# Terminal 1: Start PostgreSQL (if not using Docker)
# Or ensure Docker containers are running:
docker start postgres-psai redis-psai

# Terminal 2: Backend
cd ps-ai-backend
rails server -p 8000

# Terminal 3: Frontend
cd ps-ai-web
npm run dev

# Terminal 4: Background Jobs (if needed)
cd ps-ai-backend
bundle exec sidekiq
```

#### **Making Changes**
1. **Backend Changes**: Edit Ruby files, server auto-reloads
2. **Frontend Changes**: Edit React/TypeScript files, hot reload active
3. **Database Changes**: Create migrations with `rails generate migration`
4. **API Testing**: Use browser dev tools or Postman

#### **Database Operations**
```bash
# View database
rails db

# Reset database (careful!)
rails db:drop db:create db:migrate db:seed

# Create migration
rails generate migration AddColumnToTable column:type

# Run migrations
rails db:migrate
```

## 🎯 Advantages of Local Development

### **1. Complete Control**
- Modify backend logic and see changes immediately
- Full access to database and logs
- No network latency

### **2. Faster Development**
- Instant feedback on changes
- No deployment delays
- Offline development capability

### **3. Safe Testing**
- No risk of affecting production data
- Can reset database anytime
- Test destructive operations safely

### **4. Enhanced Debugging**
- Full stack traces and logs
- Database query inspection
- Step-through debugging

## ⚠️ Troubleshooting

### **Common Issues and Solutions**

#### **Backend Won't Start**
```bash
# Check Ruby version
ruby --version

# Check if PostgreSQL is running
pg_isready -h localhost -p 5432

# Check if Redis is running
redis-cli ping

# Check for missing gems
bundle install

# Check database connection
rails db:version
```

#### **Frontend Can't Connect to Backend**
```bash
# Verify backend is running on port 8000
curl http://localhost:8000/version

# Check .env.local file
cat .env.local

# Verify CORS settings in backend
# Check ps-ai-backend/config/initializers/cors.rb
```

#### **Database Connection Issues**
```bash
# Test PostgreSQL connection
psql -h localhost -p 5432 -U postgres -d psai_api_development

# Check database.yml configuration
cat config/database.yml

# Recreate database
rails db:drop db:create db:migrate
```

#### **Authentication Issues**
```bash
# Verify JWT secret is set
echo $SESSION_COOKIE_SECRET

# Check credentials file
cat config/credentials.yml

# Test user creation in Rails console
rails console
User.create!(name: 'Test', email: '<EMAIL>', password: 'password123')
```

## 🔐 Security Considerations

### **Development vs Production**
- **Local Development**: Uses local database, relaxed CORS
- **Production**: Uses hosted database, strict CORS, SSL required

### **Environment Separation**
- Never use production credentials in local development
- Use separate API keys for external services
- Keep .env.local in .gitignore

## 📚 Additional Resources

### **Useful Commands**
```bash
# Backend
rails routes                    # View all API routes
rails console                   # Interactive Ruby console
rails db:migrate:status         # Check migration status
bundle exec rspec               # Run tests

# Frontend
npm run build                   # Build for production
npm run type-check             # TypeScript checking
npm run lint                   # Code linting

# Database
psql -h localhost -U postgres   # Connect to PostgreSQL
redis-cli                      # Connect to Redis
```

### **File Locations**
- **Backend Config**: `ps-ai-backend/config/`
- **Frontend Config**: `ps-ai-web/.env.local`
- **Database Schema**: `ps-ai-backend/db/schema.rb`
- **API Routes**: `ps-ai-backend/config/routes.rb`
- **Frontend API Client**: `ps-ai-web/src/lib/api-client.ts`

## 🎉 Success Indicators

When everything is working correctly, you should see:
- ✅ Backend running on `http://localhost:8000`
- ✅ Frontend running on `http://localhost:3000`
- ✅ Successful login with test credentials
- ✅ API calls in Network tab show 200 status
- ✅ No console errors in browser
- ✅ Database queries visible in Rails logs

---

**🎯 Key Takeaway**: This setup gives you complete control over both frontend and backend development, allowing you to make enhancements efficiently without depending on the hosted Fly.io backend. You can now develop, test, and debug your entire application stack locally!
