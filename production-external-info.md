1. VERCEL - handles the ps-ai-web:

Vercel production URL:

URL:
https://app.perceptionselling.ai/

Project ID
Used when interacting with the Vercel API.

prj_FuRzrafgJkLBUjmlJT561dYR8Edv


ENVIRONMENT VARIABLES: 

NEXT_PUBLIC_API_URL
All Pre-Production Environments

https://psai-api.fly.dev/v1


NEXT_PUBLIC_API_URL
Production

https://api.perceptionselling.ai/v1








2. Fly.io (for backend) - handles the ps-ai-backend:

# Production Infrastructure Guide - Complete Fly.io Setup

## 🏗️ Production Architecture Overview

```
Frontend (Vercel) → Backend API (Fly.io) → PostgreSQL Database (Fly.io)
     ↓                    ↓                        ↓
app.perceptionselling.ai  psai-api-wispy-resonance-3660  perceptionselling-prod-db
```

## 🚀 Backend Application Details

### **Application Information**
- **App Name**: `psai-api-wispy-resonance-3660`
- **Hostname**: `https://psai-api-wispy-resonance-3660.fly.dev`
- **Production URL**: `https://api.perceptionselling.ai`
- **API Base Path**: `/v1`
- **Full API URL**: `https://psai-api-wispy-resonance-3660.fly.dev/v1`

### **Technology Stack**
- **Language**: Ruby 3.1.2
- **Framework**: Ruby on Rails (API-only)
- **Background Jobs**: Sidekiq
- **Cache/Queue**: Redis
- **Database**: PostgreSQL
- **Container**: Docker
- **Hosting**: Fly.io

### **Application Structure**
```
/rails/
├── app/
│   ├── auth/           # Authentication & authorization
│   ├── controllers/    # API endpoints & request handling
│   ├── jobs/          # Background job processing (Sidekiq)
│   ├── mailers/       # Email functionality
│   ├── models/        # Database models & business logic
│   ├── repositories/  # Data access layer
│   ├── services/      # Business logic & external integrations
│   └── views/         # JSON response formatting
├── config/
│   ├── database.yml   # Database configuration
│   ├── routes.rb      # API routing
│   └── environments/  # Environment-specific configs
├── db/
│   ├── migrate/       # Database migrations
│   └── seeds.rb       # Sample data
├── Dockerfile         # Container configuration
├── Gemfile           # Ruby dependencies
└── fly.toml          # Fly.io deployment config
```

### **Process Groups**
- **app**: 2 machines (API servers)
- **worker**: 2 machines (Background job processors)

### **Machine Configuration**
- **Size**: shared-1x-cpu@2048MB
- **Region**: Distributed (scalable to multiple regions)

## 🗄️ Database Details

### **Database Application**
- **App Name**: `perceptionselling-prod-db`
- **Type**: Unmanaged Fly Postgres
- **Hostname (Internal)**: `postgresql://perceptionselling-prod-db.flycast`
- **Hostname (External)**: `perceptionselling-prod-db.fly.dev`
- **Port**: `5432`
- **Machine Size**: performance-1x-cpu@2048MB
- **Region**: sin (Singapore)

### **Database Configuration**
- **External Access**: ✅ Enabled
- **SSL/TLS**: Required (PG_TLS handler)
- **IPv6 Address**: `fdaa:16:b188:0:1::5`
- **Machine ID**: `21781973f03e89`

### **Available Databases**
```
DATABASE NAME                    USERS
postgres                        flypgadmin, postgres, psai_api_wispy_resonance_3660, repmgr
psai-prod                       flypgadmin, postgres, psai_api_wispy_resonance_3660, repmgr
psai_api_wispy_resonance_3660   flypgadmin, postgres, psai_api_wispy_resonance_3660, repmgr
repmgr                          flypgadmin, postgres, psai_api_wispy_resonance_3660, repmgr
```

### **Database Services**
```
PROTOCOL    PORTS           HANDLERS    FORCE HTTPS
TCP         5432 => 5432    [PG_TLS]    False
TCP         5433 => 5433    [PG_TLS]    False
```

## 🔐 Access Methods

### **Backend SSH Access**
```bash
# Connect to backend application
fly ssh console -a psai-api-wispy-resonance-3660

# Specify machine if needed
fly ssh console -a psai-api-wispy-resonance-3660 -s 9185517b12ee08
```

### **Database Access**

#### **Method 1: Via Fly CLI (Recommended)**
```bash
# Connect to database directly
fly postgres connect -a perceptionselling-prod-db

# Connect to specific database
fly postgres connect -a perceptionselling-prod-db --database psai-prod
```

#### **Method 2: External Connection (psql)**
```bash
# External connection string format
psql "sslmode=require host=perceptionselling-prod-db.fly.dev dbname=psai-prod user=USERNAME"

# Full connection string
postgresql://USERNAME:<EMAIL>:5432/psai-prod?sslmode=require
```

#### **Method 3: Database Management Tools**
- **Host**: `perceptionselling-prod-db.fly.dev`
- **Port**: `5432`
- **Database**: `psai-prod`
- **SSL Mode**: `require`
- **Username**: (Get from backend secrets)
- **Password**: (Get from backend secrets)

## 🔑 Environment Variables & Secrets

### **Backend Application Secrets**
```bash
# View all backend secrets
fly secrets list -a psai-api-wispy-resonance-3660
```

### **Key Environment Variables**
```
PSAI_API_DATABASE_DBNAME        # Database name
PSAI_API_DATABASE_HOSTNAME      # Database host
PSAI_API_DATABASE_PASSWORD      # Database password
PSAI_API_DATABASE_USERNAME      # Database username
PSAI_API_DATABASE_PORT          # Database port
RAILS_MASTER_KEY               # Rails encryption key
REDIS_URL                      # Redis connection
S3_ACCESS_KEY_ID               # AWS S3 access
S3_SECRET_ACCESS_KEY           # AWS S3 secret
S3_BUCKET                      # S3 bucket name
S3_REGION                      # S3 region
SENTRY_DSN                     # Error tracking
MAILER_SMTP_SERVER             # Email configuration
SIDEKIQ_USERNAME               # Background jobs auth
SIDEKIQ_PASSWORD               # Background jobs auth
SESSION_COOKIE_SECRET          # Session security
WEBHOOK_ACCESS_KEY             # Webhook authentication
```

### **Database Secrets**
```bash
# View database secrets
fly secrets list -a perceptionselling-prod-db
```

```
OPERATOR_PASSWORD              # Database operator password
SU_PASSWORD                    # Superuser password
REPL_PASSWORD                  # Replication password
```

## 🌐 Production URLs & Endpoints

### **Frontend Applications**
- **Production**: `https://app.perceptionselling.ai/`
- **Vercel Project ID**: `prj_FuRzrafgJkLBUjmlJT561dYR8Edv`

### **Backend API Endpoints**
- **Direct Fly.io**: `https://psai-api-wispy-resonance-3660.fly.dev/v1`
- **Production Domain**: `https://api.perceptionselling.ai/v1`
- **Pre-Production**: `https://psai-api.fly.dev/v1`

### **Key API Routes**
```
POST /v1/login                 # User authentication
GET  /v1/auth                  # Auth verification
GET  /v1/organizations         # Organization data
GET  /v1/account_plans         # Account planning
GET  /v1/users                 # User management
POST /v1/s3/uploads/presign    # File uploads
```

## 🛠️ Management Commands

### **Application Management**
```bash
# List all apps
fly apps list

# View app status
fly status -a psai-api-wispy-resonance-3660
fly status -a perceptionselling-prod-db

# View logs
fly logs -a psai-api-wispy-resonance-3660
fly logs -a perceptionselling-prod-db

# Scale application
fly scale count 4 -a psai-api-wispy-resonance-3660

# Deploy updates
fly deploy -a psai-api-wispy-resonance-3660
```

### **Database Management**
```bash
# List databases
fly postgres db list -a perceptionselling-prod-db

# Create database backup
fly postgres backup -a perceptionselling-prod-db

# Scale database
fly machine clone 21781973f03e89

# Monitor database
fly postgres monitor -a perceptionselling-prod-db
```

## 🔒 Security & Access Control

### **Database Users & Permissions**
- **flypgadmin**: Administrative access
- **postgres**: Superuser access
- **psai_api_wispy_resonance_3660**: Application user
- **repmgr**: Replication manager

### **Network Security**
- **Internal Network**: `fdaa:16:b188::/48`
- **External Access**: Secured with TLS
- **Authentication**: Required for all connections
- **SSL Mode**: Required for external connections

## 📊 Monitoring & Maintenance

### **Health Checks**
```bash
# Check application health
curl https://psai-api-wispy-resonance-3660.fly.dev/v1/health

# Check database connectivity
fly postgres connect -a perceptionselling-prod-db -c "SELECT 1;"
```

### **Performance Monitoring**
- **Application Metrics**: Available via Fly.io dashboard
- **Database Metrics**: PostgreSQL built-in monitoring
- **Error Tracking**: Sentry integration
- **Logs**: Centralized via Fly.io logging

---

## ⚠️ Important Notes

1. **Unmanaged Postgres**: Your database is unmanaged - you're responsible for maintenance
2. **Production Data**: Always backup before making changes
3. **SSL Required**: All external database connections must use SSL
4. **Secrets Management**: Never expose secrets in code or logs
5. **Access Control**: Limit database access to necessary users only

This guide contains all the essential information for accessing and managing your production infrastructure on Fly.io.












# Database Access Quick Reference

## 🎯 Quick Database Connection

### **Primary Database**: `psai-prod`
```bash
# Fastest way to connect
fly postgres connect -a perceptionselling-prod-db --database psai-prod
```

### **External Connection String**
```bash
# Get actual credentials first
fly secrets list -a psai-api-wispy-resonance-3660

# Then use this format
postgresql://USERNAME:<EMAIL>:5432/psai-prod?sslmode=require
```

## 🔑 Getting Database Credentials

### **Method 1: From Backend Secrets**
```bash
fly secrets list -a psai-api-wispy-resonance-3660
```
Look for:
- `PSAI_API_DATABASE_USERNAME`
- `PSAI_API_DATABASE_PASSWORD`
- `PSAI_API_DATABASE_DBNAME`
- `PSAI_API_DATABASE_HOSTNAME`

### **Method 2: From Database Secrets**
```bash
fly secrets list -a perceptionselling-prod-db
```

## 🛠️ Common Database Operations

### **Connect and Explore**
```sql
-- List all databases
\l

-- Connect to psai-prod database
\c psai-prod

-- List all tables
\dt

-- Describe a table
\d table_name

-- Show table sizes
SELECT schemaname,tablename,pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size 
FROM pg_tables 
WHERE schemaname NOT IN ('information_schema','pg_catalog') 
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### **User Management**
```sql
-- List all users
\du

-- Check current user
SELECT current_user;

-- Check user permissions
\dp
```

## 📊 Database Information

| Property | Value |
|----------|-------|
| **Host** | `perceptionselling-prod-db.fly.dev` |
| **Port** | `5432` |
| **Primary DB** | `psai-prod` |
| **SSL Mode** | `require` |
| **Machine Size** | performance-1x-cpu@2048MB |
| **Region** | sin (Singapore) |

## 🔒 Security Notes

- ✅ **SSL Required**: All external connections must use SSL
- ✅ **Authentication Required**: Username/password needed
- ✅ **Network Secured**: TLS encryption in transit
- ⚠️ **Production Data**: Handle with extreme care
- ⚠️ **Backup First**: Always backup before modifications

## 🚨 Emergency Access

If you can't connect normally:
```bash
# SSH into backend and access database from there
fly ssh console -a psai-api-wispy-resonance-3660
rails console
# Then use ActiveRecord to query database
```

## 📱 GUI Database Tools

Configure your favorite database tool with:
- **Host**: `perceptionselling-prod-db.fly.dev`
- **Port**: `5432`
- **Database**: `psai-prod`
- **SSL Mode**: `Require`
- **Username**: (from secrets)
- **Password**: (from secrets)

Popular tools:
- pgAdmin
- DBeaver
- TablePlus
- DataGrip