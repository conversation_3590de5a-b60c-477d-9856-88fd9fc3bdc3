# frozen_string_literal: true

module V1
  class ChatOutput < ApiOutput
    def format
      {
        id: @object.id,
        external_id: @object.external_id,
        name: @object.name.to_s.first(65),
        chat_type: @object.chat_type,
        workspace_id: @object.workspace_id
      }
    end

    def full_format
      format.merge(
        model: @object.model.model,
        expert_prompt: expert_prompt,
        source_model_template: source_model_template,
        compiled_prompt: @object.compiled_prompt.to_s,
        placeholder: model_template&.placeholder.to_s,
        credits: model_template&.max_tokens,
        verified: model_template&.verified,
        category: model_template&.category,
        messages: messages_output
      )
    end

    def model_template
      @object&.model_templates&.first
    end

    def source_model_template
      return {} unless @object.source_model_template_id

      source = ModelTemplate.find(@object.source_model_template_id)

      {
        id: source.id,
        name: source.name,
        prompt: source.prompt,
        placeholder: source.placeholder
      }
    end

    def model_template
      return unless @object.source_model_template_id

      ModelTemplate.find(@object.source_model_template_id)
    end

    def messages_output
      @object.messages.where(status_on_thread: 'present').map do |message|
        ::V1::MessageOutput.new(message).format
      end
    end

    def expert_prompt
      return @object.expert_prompt if @object.expert_prompt.to_s.present?

      model_template&.test_prompt_chat&.compiled_prompt
    end
  end
end
