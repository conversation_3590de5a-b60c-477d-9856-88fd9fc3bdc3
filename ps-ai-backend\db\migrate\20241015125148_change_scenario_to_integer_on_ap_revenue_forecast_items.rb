class ChangeScenarioToIntegerOnApRevenueForecastItems < ActiveRecord::Migration[7.0]
  def change
    ::AccountPlanTableItems::ApRevenueForecastItem.unscoped.all.update_all(
      low_scenario: nil, realistic_scenario: nil, high_scenario: nil
    )

    change_column :ap_revenue_forecast_items, :low_scenario, 'integer USING CAST(low_scenario AS integer)'
    change_column :ap_revenue_forecast_items, :realistic_scenario, 'integer USING CAST(realistic_scenario AS integer)'
    change_column :ap_revenue_forecast_items, :high_scenario, 'integer USING CAST(high_scenario AS integer)'
  end
end
