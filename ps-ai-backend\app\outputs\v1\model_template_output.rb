# frozen_string_literal: true

module V1
  class ModelTemplateOutput < ApiOutput
    def format
      {
        id: @object.id,
        name: @object.name,
        description: @object.description,
        max_tokens: @object.max_tokens,
        temperature: @object.temperature,
        instruction: @object.instruction,
        rules: @object.rules,
        model: @object.model,
        status: @object.status,
        reference_output: @object.reference_output,
        reference_output_file: url_output(@object.reference_output_url),
        template_category: template_category_output,
        inputs: @object.inputs,
        organization_id: @object.organization_id,
        rating: 5, # TODO
        updated_at: @object.updated_at,
        last_updated_by: last_updated_by_output,
        account_plan_group: account_plan_group_output,
        variables: variables_output
      }
    end

    def variables_output
      return [] if variables.blank?

      current_variables = variables&.select { |v| v.model_template_id == @object.id }

      return [] if current_variables.blank?

      current_variables&.map { |v| ModelTemplateVariableOutput.new(v) }
    end

    def template_category_output
      return if @object.template_category_id.nil?

      current_category = template_categories.find do |tc|
        tc.id == @object.template_category_id
      end

      return if current_category.nil?

      ::V1::TemplateCategoryOutput.new(current_category).format
    end

    def url_output(url)
      return unless url

      uri = URI.parse(url)
      filename = File.basename(uri.path)
      {
        filename: filename,
        url: url
      }
    end

    def last_updated_by_output
      return if @object.last_updated_by_organization_user_id.nil? || organization_users.blank?

      org_user = organization_users.find  do |ou|
        ou.id == @object.last_updated_by_organization_user_id
      end

      return if org_user.nil?
      
      ::V1::UserOutput.new(org_user.user).name_format
    end

    def account_plan_group_output
      return if account_plan_groups.blank?

      curr_ap_group = account_plan_groups.find { |apg| apg.id == @object.account_plan_group_id }

      return if curr_ap_group.blank?

      {
        id: curr_ap_group.id,
        account_plan_unique_id: curr_ap_group.account_plan_unique_id
      }
    end

    # UNUSED
    # def input_sets_output
    #   return [] if input_sets.blank?

    #   input_sets.select { |is| is.model_template_id == @object.id }.map do |is|
    #     curr_vars = variables.select { |v| v.model_template_id == @object.id && v.input_type == is.input_type }
    #                          .map { |v| ModelTemplateVariableOutput.new(v).format }

    #     {
    #       id: is.id,
    #       rules: is.rules,
    #       reference_output: is.reference_output,
    #       reference_output_url: is.reference_output_url,
    #       input_type: is.input_type,
    #       variables: curr_vars
    #     }
    #   end
    # end

    def current_user
      @options[:current_user]
    end

    def variables
      @options[:variables]
    end

    def template_categories
      @options[:template_categories]
    end

    def organization_users
      @options[:organization_users]
    end

    def account_plan_groups
      @options[:account_plan_groups]
    end

    # UNUSED
    # def input_sets
    #   @options[:input_sets]
    # end
  end
end
