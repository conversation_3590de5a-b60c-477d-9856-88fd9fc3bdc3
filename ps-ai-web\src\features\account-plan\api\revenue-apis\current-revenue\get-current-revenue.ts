import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APCurrentRevenue } from "@/features/account-plan/types/revenue-types";

export const getCurrentRevenueDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APCurrentRevenue> => {
  return api.get(
    API_ROUTES.ACCOUNT_PLANS_CURRENT_REVENUE_DETAIL(accountId, id)
  );
};

export const getCurrentRevenueDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_CURRENT_REVENUE,
      id,
    ],
    queryFn: () => getCurrentRevenueDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseCurrentRevenueDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getCurrentRevenueDetail>;
  options?: Partial<ReturnType<typeof getCurrentRevenueDetailQueryOptions>>;
};

export const useCurrentRevenueDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseCurrentRevenueDetailOptions) => {
  const currentRevenueDetailQuery = useQuery({
    ...getCurrentRevenueDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...currentRevenueDetailQuery,
    currentRevenueDetail: currentRevenueDetailQuery.data?.data,
  };
};
