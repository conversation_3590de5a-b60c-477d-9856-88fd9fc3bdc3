# frozen_string_literal: true

class AccountPlanTableItems::ApInsightAndPerspectiveItemService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
    @user_data = user_data
  end

  def create(account_plan_id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    if params.has_key?(:ap_stakeholder_mapping_item_id)
      stakeholder_mapping = ::AccountPlanTableItems::ApStakeholderMappingItem.find_by(id: params[:ap_stakeholder_mapping_item_id])
      verify_ap_stakeholder_mapping_to_account_plan(stakeholder_mapping, account_plan)
    end

    ap_item = ::AccountPlanTableItems::ApInsightAndPerspectiveItem.new

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: 'position',
        table_type: 'insight_and_perspective',
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item = ::AccountPlanTableItems::ApInsightAndPerspectiveItem.create!(params)
    end

    OpenStruct.new(
      ap_insight_and_perspective_item: ap_item
    )
  end

  def update(account_plan_id, id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    if params.has_key?(:ap_stakeholder_mapping_item_id)
      stakeholder_mapping = ::AccountPlanTableItems::ApStakeholderMappingItem.find_by(id: params[:ap_stakeholder_mapping_item_id])
      verify_ap_stakeholder_mapping_to_account_plan(stakeholder_mapping, account_plan)
    end

    ap_item = ::AccountPlanTableItems::ApInsightAndPerspectiveItem.find(id)

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: 'position',
        table_type: 'insight_and_perspective',
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item.update!(params)
    end

    OpenStruct.new(
      ap_insight_and_perspective_item: ap_item
    )
  end

  def index(account_plan_id, query_params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_table = ApTable.find_by(
      table_category: 'position',
      table_type: 'insight_and_perspective',
      account_plan_id: account_plan_id
    )

    ap_item_reposity = ::AccountPlanTableItems::ApInsightAndPerspectiveItems.new

    filter = query_params.slice(
      :search, :page, :per_page, :disable_pagination
    )
    filter = filter.merge(
      ap_table_id: ap_table&.id || -1,
      exist_stakeholder: true
    )

    filtered_items = ap_item_reposity.filter(filter)

    OpenStruct.new(
      ap_insight_and_perspective_items: filtered_items
    )
  end

  def show(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApInsightAndPerspectiveItem.find(id)
    
    OpenStruct.new(
      ap_insight_and_perspective_item: ap_item
    ) 
  end

  def destroy(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApInsightAndPerspectiveItem.find(id)

    ActiveRecord::Base.transaction do
      ap_item.discard!
    end
  end

  def generate(account_plan_id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_table = ApTable.find_by(
      table_category: 'position',
      table_type: 'insight_and_perspective',
      account_plan_id: account_plan_id
    )

    exist! ap_table.present?, on_error: 'Table is not created'

    ActiveRecord::Base.transaction do
      category_str = TemplateCategory.ap_table_map_to_categories[ap_table.table_type]
      template_category_ids = TemplateCategory.where(name: category_str).ids
      authorize! template_category_ids.present?, on_error: 'Template invalid, please contact administrator!'

      if @organization.tier == 'premium'
        active_template = ModelTemplate
        .where(
          organization_id: @organization.id,
          status: 'active',
          template_category_id: template_category_ids
        )
        .last
      end

      active_template ||= ModelTemplate
        .where(
          organization_id: nil,
          status: 'active',
          template_category_id: template_category_ids
        )
        .last
      authorize! active_template.present?, on_error: 'Template invalid, please contact administrator!'

      # get model
      used_model = Model.find_by(model_template_id: active_template.id, model: active_template.model)
      authorize! used_model.present?, on_error: 'Template invalid, please contact administrator!'

      json_format = {
        data: [
          {
            description: String
          }
        ]
      }.to_json
  
      query = account_plan_consideration_llm_query(account_plan)
      message = "'message' = with given data, " \
                "generate few business insights and perspectives with each 'data'.'description' following closely to the format and style of Reference Output from instruction. " \
                "Generate response using json format that can be directly parsed: #{json_format}"
      final_query = query + message

      ai_params = {
        query: final_query,
        account_plan_id: account_plan.id,
        ap_table_id: ap_table.id,
        model_id: used_model.id,
        chat_type: 'ap_table_generated_items'
      }

      openai_service = OpenaiService.new(@user_data)
      openai_service.generate_ap_table_response(ai_params)
    end
  end
end
