"use client";

import { IconEdit, IconTrashXFilled } from "@tabler/icons-react";
import _ from "lodash";
import React, { ReactNode, useState } from "react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import Link from "next/link";
import { toast } from "sonner";
import { capitalize } from "@/lib/utils";
import { isRequestError } from "@/lib/api-client";

function GridActions({ children }: { children: ReactNode }) {
  return (
    <div className="flex h-full w-full items-center gap-2 pb-1">{children}</div>
  );
}

function GridEditButton({ href }: { href: string }) {
  return (
    <Button variant="icon" size="icon">
      <Link href={href} className="h-fit">
        <IconEdit className="size-icon-res-base" />
      </Link>
    </Button>
  );
}

function GridDeleteButton({
  onDelete,
  isLoading,
  itemName,
  description,
}: {
  onDelete: () => Promise<void>;
  isLoading?: boolean;
  description?: React.ReactNode;
  itemName: string;
}) {
  const [openModal, setOpenModal] = useState(false);

  const onCloseModal = () => setOpenModal(false);

  const onDeleteHandle = async () => {
    try {
      await onDelete();
    } catch (e) {
      if (isRequestError(e)) {
        const errorMessage = e.response?.data.errors[0].message ?? "";
        toast.error(errorMessage);
      } else {
        toast.error(`An error occured upon ${itemName} deletion`);
      }
    } finally {
      onCloseModal();
    }
  };

  return (
    <Dialog open={openModal} onOpenChange={setOpenModal}>
      <DialogTrigger asChild>
        <Button variant="icon" size="icon" onClick={(e) => e.stopPropagation()}>
          <IconTrashXFilled className="size-icon-res-base text-red-500" />
        </Button>
      </DialogTrigger>
      <DialogContent onClick={(e) => e.stopPropagation()}>
        <DialogHeader>
          <DialogTitle>Delete {capitalize(itemName)}</DialogTitle>
          <DialogDescription>
            {!!description
              ? description
              : `Are you sure want to delete this ${itemName}?`}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="ghost" onClick={onCloseModal}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            type="submit"
            onClick={onDeleteHandle}
            isLoading={isLoading}
          >
            Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export { GridActions, GridEditButton, GridDeleteButton };
