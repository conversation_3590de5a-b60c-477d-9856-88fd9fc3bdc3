class CreateAccountPlans < ActiveRecord::Migration[7.0]
  def change
    create_table :account_plans do |t|
      t.string :name
      t.string :version
      t.string :status
      t.string :priority
      t.datetime :plan_date, :index => true
      t.datetime :review_date, :index => true
      t.datetime :next_review_date, :index => true
      t.string :considerations, :array => true, default: []
      t.text :account_addressable_area
      t.bigint :owner_organization_user_id, :index => true, :null => true
      t.references :organization
      t.datetime :discarded_at, :index => true

      t.timestamps
    end
  end
end
