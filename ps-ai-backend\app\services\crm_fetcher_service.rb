class CrmFetcherService
  def initialize(organization, crm_type)
    @organization = organization
    @crm_type = crm_type
  end

  def fetch_contacts
    integration = @organization.organization_crms.find_by(crm_type: @crm_type)
    raise 'No CRM integration' unless integration

    client = build_faraday_client(integration)
    fetch_method = "fetch_#{integration.crm_type}_contacts"
    raise 'Unsupported CRM type' unless respond_to?(fetch_method, true)

    contacts = send(fetch_method, client)
    contacts
  end

  private

  def build_faraday_client(integration)
    base_url = integration.instance_url.presence || default_base_url(integration.crm_type)

    Faraday.new(url: base_url) do |conn|
      conn.request :authorization, 'Bearer', integration.access_token
      conn.response :json
      conn.adapter Faraday.default_adapter
    end
  end

  def default_base_url(crm_type)
    case crm_type
    when 'hubspot' then 'https://api.hubapi.com'
    when 'salesforce' then raise 'Missing instance_url for Salesforce'
    when 'microsoft_dynamics' then raise 'Missing instance_url for Dynamics'
    end
  end

  def fetch_hubspot_contacts(client)
    response = client.get('/crm/v3/objects/contacts?limit=10&properties=firstname,lastname,jobtitle,city')
    raise StandardError, 'Failed to fetch HubSpot contacts' unless response.success?
    response.body['results']
  end

  def fetch_salesforce_contacts(client)
    response = client.get('/services/data/v63.0/query', q: 'SELECT FirstName, LastName, Title, MailingCity FROM Contact LIMIT 10')
    raise StandardError, 'Failed to fetch Salesforce contacts' unless response.success?
    response.body['records']
  end

  def fetch_microsoft_dynamics_contacts(client)
    response = client.get('/api/data/v9.2/contacts?$select=firstname,lastname,jobtitle,address1_city')
    response.body['value']
  end
end