# frozen_string_literal: true

class AccountPlans < ::ApplicationRepository
  def default_scope
    ::AccountPlan.all
  end

  def filter_by_organization_id(organization_id)
    @scope.where(organization_id: organization_id)
  end

  def filter_by_search(search)
    @scope.joins('LEFT JOIN account_plan_groups ON account_plan_groups.id = account_plans.account_plan_group_id')
          .where("(COALESCE(version, '') || ' ' || COALESCE(account_plan_groups.account_plan_unique_id, '')) ilike ?", "%#{search}%")
  end

  def filter_by_status(status)
    @scope.where(status: status)
  end
end
