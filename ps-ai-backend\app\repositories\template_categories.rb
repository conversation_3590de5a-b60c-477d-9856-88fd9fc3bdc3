# frozen_string_literal: true

class TemplateCategories < ::ApplicationRepository
  def default_scope
    ::TemplateCategory.all
  end

  def filter_by_organization_id(organization_id)
    @scope.where(organization_id: organization_id)
  end

  def filter_by_organization_id_with_general_category(organization_id)
    @scope.where("general_category = true OR (organization_id = ? AND general_category = false)", "#{organization_id}")
  end

  def filter_by_search(search)
    @scope.where("name ilike ?", "%#{search}%")
  end
end
