# frozen_string_literal: true

module V1
  class LlmConsiderationOutput < ApiOutput
    def format
      {
        id: @object.id,
        organization_id: @object.organization_id,
        usage_on: @object.usage_on,
        result: result_output,
        updated_at: @object.updated_at
      }
    end

    def result_output
      return [] if @object.result.blank?

      return @object.result
    end
  end
end
