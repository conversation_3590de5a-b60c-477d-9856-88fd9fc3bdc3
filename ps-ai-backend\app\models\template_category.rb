# frozen_string_literal: true

class TemplateCategory < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  belongs_to :organization, optional: true

  def self.global_categories
    [
      'Circumstantial Analysis (Macro)',
      'Circumstantial Analysis (Industry)',
      'Circumstantial Analysis (Business)',
      # 'Information Needed',
      'Insights and Perspective',
      # 'News',
      # 'Strategic Considerations',
      # 'Company Template'
    ]
  end

  def self.populate_global_categories
    global_categories.map do |name|
      tc = TemplateCategory.find_or_create_by(name: name)
      tc.update(validated: true, general_category: true, organization_id: nil)
      tc
    end 
  end

  def self.ap_table_map_to_categories
    {
      'missing_information' => ['Information Needed'],
      'action_plan' => ['Strategic Considerations'],
      'insight_and_perspective' => ['Insights and Perspective'],
      'ca_macro' => ['Circumstantial Analysis (Macro)'],
      'ca_industry' => ['Circumstantial Analysis (Industry)'],
      'ca_business' => ['Circumstantial Analysis (Business)'],
      'circumstantial_analysis' => {
        'macro' => 'Circumstantial Analysis (Macro)',
        'industry' => 'Circumstantial Analysis (Industry)',
        'business' => 'Circumstantial Analysis (Business)'
      } #  grouped
    }
  end
end
