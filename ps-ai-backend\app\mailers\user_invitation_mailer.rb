class UserInvitationMailer < ApplicationMailer
  default from: <PERSON>N<PERSON>['MAILER_EMAIL_ADDRESS']

  def send_email
    @user_invitation = params[:user_invitation]
    @organization = @user_invitation.organization
    @base_app = ENV['WEBAPP']
    @accept_invite_link = "#{@base_app}/accept-invitation?invite_code=#{@user_invitation.invitation_code}"
    @organization_unique_id = @organization.unique_id
    
    mail(to: @user_invitation.email, subject: "You've Been Invited to Join #{@organization.name} on PerceptionSelling.ai")
  end
end
