import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APActiveTopAction } from "@/features/account-plan/types/strategy-types";

type ActiveTopActionListParams = BaseParams;

export const getActiveTopActionList = ({
  params,
}: {
  params?: ActiveTopActionListParams;
}): ApiResponse<APActiveTopAction> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_ACTIVE_TOP_ACTION, {
    params,
  });
};

export const getActiveTopActionListQueryOptions = (
  params?: ActiveTopActionListParams
) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.ACCOUNT_PLANS_ACTIVE_TOP_ACTION],
    queryFn: () => getActiveTopActionList({ params }),
  });
};

type UseActiveTopActionListOptions = {
  params?: ActiveTopActionListParams;
  queryConfig?: QueryConfig<typeof getActiveTopActionList>;
  options?: Partial<ReturnType<typeof getActiveTopActionListQueryOptions>>;
};

export const useActiveTopActionList = ({
  params,
  queryConfig,
  options,
}: UseActiveTopActionListOptions) => {
  const activeTopActionListQuery = useQuery({
    ...getActiveTopActionListQueryOptions(params),
    ...queryConfig,
    ...options,
  });

  return {
    ...activeTopActionListQuery,
    activeTopActionList: activeTopActionListQuery.data?.data,
  };
};
