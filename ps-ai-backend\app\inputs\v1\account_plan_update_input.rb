# frozen_string_literal: true

module V1
  class AccountPlanUpdateInput < ::ApplicationInput
    optional(:account_plan_group_id)
    optional(:owner_user_id)
    optional(:name)
    optional(:status)
    optional(:priority)
    optional(:review_date)
    optional(:next_review_date)
    optional(:currency)
    optional(:company)
    optional(:location)
    optional(:industry)
    optional(:function)
    optional(:version)
    optional(:account_addressable_area)
    optional(:model_template_id)
    optional(:generate_analysis)
  end
end
