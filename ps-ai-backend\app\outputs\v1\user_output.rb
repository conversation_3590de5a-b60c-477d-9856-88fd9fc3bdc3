# frozen_string_literal: true

module V1
  class UserOutput < ApiOutput
    def format
      {
        id: @object.id,
        first_name: @object.name,
        last_name: @object.last_name,
        photo_url: @object.photo_url,
        email: @object.email,
        contact: @object.contact,
        last_login_at: @object.last_login_at,
        role: role_output,
        created_at: @object.created_at,
        status: status_output,
        **maybe(:organization, &:organization_output)
      }
    end

    def name_format
      {
        first_name: @object.name,
        last_name: @object.last_name
      }
    end

    def show_organization?
      @options[:show_organization] || false
    end

    def organization_output
      return if organization.blank?

      ::V1::OrganizationOutput.new(organization).format
    end

    def role_output
      return if organization_user.blank?

      organization_user.role&.name
    end

    def status_output
      return if organization_user.blank?

      organization_user.status
    end

    def organization_user
      @options[:organization_user]
    end

    def organization
      @options[:organization]
    end
  end
end
