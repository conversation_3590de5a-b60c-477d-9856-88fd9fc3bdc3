# frozen_string_literal: true

module V1
  class UserManagementOutput < ApiOutput
    def format
      {
        user_id: @object.user_id,
        first_name: @object.user&.name,
        last_name: @object.user&.last_name,
        email: @object.user&.email,
        contact: @object.user&.contact,
        role: @object.role,
        prompt_tuning_permissions: @object.prompt_tuning_permissions,
        country_code: @object.country_code,
        organization: organization_output,
        org_employee_id: org_employee_output, # DEPRECATED, can be taken out
        organization_identifier_id: @object.organization_identifier_id,
        team_leader: team_leader_output,
        last_login_at: @object.user&.last_login_at,
        status: @object.status,
        organization_unique_id: @object.organization&.unique_id
      }
    end

    def create_invite_format
      {
        email: @object.email,
        invitation_status: @object.invitation_status,
        invitation_expiry_date: @object.invitation_expiry_date
      }
    end

    def invitation_format
      {
        email: @object.email,
        invitation_status: @object.invitation_status,
        invitation_expiry_date: @object.invitation_expiry_date,
        invitation_code: @object.invitation_code,
        organization: organization_output,
        invited_by: invited_by_output
      }
      
    end

    def create_change_password_request_format
      {
        email: @object.email,
        request_status: @object.request_status,
        request_expiry_date: @object.request_expiry_date
      }
    end

    def roles_format
      {
        user_id: @object.user_id,
        roles: @object.roles
      }
    end

    def team_leader_output
      leader_user = @object.team_leader_organization_user&.user

      return unless leader_user.present?

      {
        id: leader_user.id,
        first_name: leader_user.name,
        last_name: leader_user.last_name,
      }
    end

    def invited_by_output
      invited_by = @object.invited_by_organization_user&.user

      return unless invited_by.present?

      {
        id: invited_by.id,
        first_name: invited_by.name,
        last_name: invited_by.last_name,
      }
    end

    def organization_output
      return if @object.organization_id.nil?

      {
        id: @object.organization_id,
        name: @object.organization&.name,
      }
    end

    def org_employee_output
      return if @object.organization_id.nil?

      "#{@object.organization.organization_code}#{@object.organization_identifier_id}"
    end
  end
end
