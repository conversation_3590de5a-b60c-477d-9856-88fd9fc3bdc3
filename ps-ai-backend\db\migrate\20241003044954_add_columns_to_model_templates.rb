class AddColumnsToModelTemplates < ActiveRecord::Migration[7.0]
  def change
    add_column :model_templates, :model, :string, :default => 'gpt-4o-mini', :null => false
    add_column :model_templates, :status, :string, :default => 'active', :null => false
    add_column :model_templates, :last_updated_by_organization_user_id, :bigint, :null => true

    add_index :model_templates, :last_updated_by_organization_user_id
    add_index :model_templates, :creator_organization_user_id
  end
end
