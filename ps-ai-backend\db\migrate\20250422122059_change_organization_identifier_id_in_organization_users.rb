class ChangeOrganizationIdentifierIdInOrganizationUsers < ActiveRecord::Migration[7.0]
  def up
    change_column :organization_users, :organization_identifier_id, :string, :null => true, :default => "", using: "organization_identifier_id::varchar"
  end

  def down
    change_column_default :organization_users, :organization_identifier_id, from: "", to: nil
    change_column :organization_users, :organization_identifier_id, :integer, :null => false, using: "NULLIF(organization_identifier_id, '')::integer"
  end
end
