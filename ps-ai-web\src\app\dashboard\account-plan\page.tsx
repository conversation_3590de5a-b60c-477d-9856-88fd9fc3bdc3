"use client";

import React, { useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { ColDef } from "ag-grid-community";
import dayjs from "dayjs";
import { useDebounce } from "@uidotdev/usehooks";

import { Input } from "@/components/ui/input/";
import { PATH } from "@/constants/path";
import { Grid } from "@/components/ui/grid";
import {
  AccountPlanData,
  AccountPlanGroupsData,
  AccountPlanStatus,
} from "@/features/account-plan/types";
import { GridActions } from "@/components/ui/grid/grid-actions";
import { useScalingDimension } from "@/lib/hooks/use-scaling-dimension";
import { useAccountPlanGroupsList } from "@/features/account-plan/api/account-plan-group/get-account-plan-group-list";
import { formatDate, getFullName } from "@/lib/utils";
import SearchAccountPlan from "@/features/account-plan/components/search-account-plan";
import { useIsHydrated } from "@/lib/hooks/use-hydrated";
import DeleteAccountPlanButton from "@/features/account-plan/components/account-plan-dashboard/delete-ap-button";
import EditAccountPlanVersionButton from "@/features/account-plan/components/account-plan-dashboard/edit-ap-button";
import CreateAccountPlanButton from "@/features/account-plan/components/account-plan-dashboard/create-ap-button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getCurrencyAbbreviations } from "@/constants/currencies";

type AccountPlanTableData = Omit<AccountPlanGroupsData, "account_plans"> &
  Pick<
    Partial<AccountPlanData>,
    "last_updated_by" | "review_date" | "next_review_date" | "version"
  > & { active_plan_id?: number };

function AccountPlan() {
  const { getWidth, getHeight } = useScalingDimension();
  const router = useRouter();
  const { isHydrated } = useIsHydrated();

  const [uniqueId, setUniqueId] = useState("");
  const [company, setCompany] = useState("");

  const debouncedUniqueId = useDebounce(uniqueId, 300);
  const debouncedCompany = useDebounce(company, 300);

  const { accountPlanGroupsList, isFetching: isFetchingAccountPlanGroups } =
    useAccountPlanGroupsList({
      params: {
        disable_pagination: true,
        ...(!!debouncedUniqueId && {
          account_plan_unique_id: debouncedUniqueId,
        }),
        ...(!!debouncedCompany && { company: debouncedCompany }),
      },
    });

  const accountPlanData: Array<AccountPlanTableData> = useMemo(() => {
    const apData = accountPlanGroupsList.map(({ account_plans, ...group }) => {
      const activePlan = account_plans.find(
        (plan) => plan.status === AccountPlanStatus.ACTIVE
      );

      return {
        ...group,
        last_updated_by: activePlan?.last_updated_by ?? {
          first_name: "",
          last_name: "",
        },
        review_date: activePlan?.review_date ?? null,
        next_review_date: activePlan?.next_review_date ?? null,
        version: activePlan?.version ?? null,
        active_plan_id: activePlan?.id,
      };
    });

    return apData;
  }, [accountPlanGroupsList]);

  const accountPlanColumns: ColDef<AccountPlanTableData>[] = useMemo(() => {
    return [
      {
        field: "account_plan_unique_id",
        headerName: "Account ID",
        maxWidth: getWidth(150),
      },
      { field: "company", maxWidth: getWidth(200) },
      {
        field: "industry.name",
        headerName: "Industry",
        maxWidth: getWidth(150),
      },
      {
        field: "account_addressable_area",
        headerName: "Account Addressable Area",
      },
      {
        field: "location",
        maxWidth: getWidth(120),
      },
      {
        field: "currency",
        maxWidth: getWidth(150),
        valueFormatter: (params) =>
          getCurrencyAbbreviations(params.data?.currency),
      },
      {
        field: "version",
        maxWidth: getWidth(150),
      },
      {
        field: "last_updated_by",
        headerName: "Updated By",
        maxWidth: getWidth(150),
        valueFormatter: (params) =>
          getFullName(
            params.data?.last_updated_by?.first_name,
            params.data?.last_updated_by?.last_name
          ),
      },
      {
        field: "review_date",
        headerName: "Last Review",
        maxWidth: getWidth(135),
        sortable: true,
        valueFormatter: (params) =>
          formatDate(params.data?.review_date, "DD/MM/YYYY"),
      },
      {
        field: "next_review_date",
        headerName: "Next Review",
        maxWidth: getWidth(135),
        sortable: true,
        valueFormatter: (params) => {
          if (!params.data?.next_review_date || !params.data?.review_date)
            return "-";

          const nextReviewDate = dayjs(params.data?.review_date).add(
            params.data?.next_review_date,
            "month"
          );

          return formatDate(nextReviewDate?.toISOString(), "DD/MM/YYYY");
        },
      },
      {
        field: "id",
        headerName: "Action",
        maxWidth: getWidth(100),
        cellRenderer: (...args: { value: number }[]) => {
          return (
            <GridActions>
              <EditAccountPlanVersionButton accountGroupId={args[0].value} />
              <DeleteAccountPlanButton accountGroupId={args[0].value} />
            </GridActions>
          );
        },
      },
    ];
  }, [getWidth]);

  if (!isHydrated) return null;

  return (
    <div className="w-full">
      <div className="mb-res-y-base flex justify-between">
        <h1 className="text-3xl font-bold text-primary-500">Account Plans</h1>
        <div className="flex items-center gap-res-x-sm">
          <SearchAccountPlan />
          <CreateAccountPlanButton />
        </div>
      </div>

      <section className="flex items-end justify-between text-primary-500">
        <div className="flex gap-res-x-lg">
          <div>
            <Input
              className="mt-res-y-xs rounded-xl"
              placeholder="Search Account ID"
              value={uniqueId}
              onChange={(e) => setUniqueId(e.target.value)}
            />
          </div>
          <div>
            <Input
              className="mt-res-y-xs rounded-xl"
              placeholder="Search Company"
              value={company}
              onChange={(e) => setCompany(e.target.value)}
            />
          </div>
        </div>
      </section>

      <section className="mt-res-y-base grid gap-8 rounded-xl bg-white p-res-x-base text-primary-500">
        <div>
          <Grid<AccountPlanTableData>
            height="70vh"
            defaultColDef={{
              flex: 1,
              wrapText: true,
              wrapHeaderText: true,
              autoHeight: true,
              valueFormatter: (params) => params.value || "-",
              sortable: false,
            }}
            onRowClicked={(e) => {
              if (
                !e.data?.active_plan_id ||
                (e.eventPath?.[0] as Element) instanceof SVGElement ||
                (e.eventPath?.[0] as Element) instanceof HTMLButtonElement
              )
                return;

              router.push(
                PATH.DASHBOARD_ACCOUNT_PLAN_EDIT(e.data?.active_plan_id)
              );
            }}
            columnDefs={accountPlanColumns}
            rowData={accountPlanData}
            headerHeight={getHeight(80)}
            rowHeight={getHeight(60)}
            loading={isFetchingAccountPlanGroups}
            overlayNoRowsTemplate="No account plan found"
            autoSizeStrategy={{
              type: "fitGridWidth",
            }}
            className="overflow-hidden rounded-lg"
          />
        </div>
      </section>
    </div>
  );
}

export default AccountPlan;
