class ChangeValueToIntegerOnApTableRevenues < ActiveRecord::Migration[7.0]
  def change
    ::AccountPlanTableItems::ApHistoricRevenueItem.unscoped.all.update_all(value: nil)
    ::AccountPlanTableItems::ApCurrentRevenueItem.unscoped.all.update_all(value: nil)
    ::AccountPlanTableItems::ApCurrentOpportunityItem.unscoped.all.update_all(value: nil)
    ::AccountPlanTableItems::ApPotentialOpportunityItem.unscoped.all.update_all(value: nil)

    change_column :ap_historic_revenue_items, :value, 'integer USING CAST(value AS integer)'
    change_column :ap_current_revenue_items, :value, 'integer USING CAST(value AS integer)'
    change_column :ap_current_opportunity_items, :value, 'integer USING CAST(value AS integer)'
    change_column :ap_potential_opportunity_items, :value, 'integer USING CAST(value AS integer)'
  end
end
