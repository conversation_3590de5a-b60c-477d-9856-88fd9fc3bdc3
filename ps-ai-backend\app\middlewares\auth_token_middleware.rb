# frozen_string_literal: true

class AuthTokenMiddleware < ApplicationMiddleware
  def initialize(app, permissions, required: true)
    super(app)
    @permissions = Array(permissions)
    @required = required
  end

  def call(env)
    request = ActionDispatch::Request.new(env)

    user_data = capture_error do
      (AuthorizeApiRequest.new(request.headers).call)[:user_data]
    end

    env['psai.auth_token'] = bearer_auth(request)

    return error(*@error) if @error && !user_data && (@permissions.exclude? :public)

    Current.user = if user_data
      user_data
    else
      {
        user: User.new,
        organization_user: OrganizationUser.new,
        organization: Organization.new,
      }
    end

    # Honeybadger.context(user)
    # Sentry.set_user(id: user.id, email: user.email) if user

    super
  end

  private

  def bearer_auth(request)
    request.authorization.to_s.match(/\A\W*bearer\W+(.*)/i).to_a.second
  end

  def capture_error
    yield
  rescue ExceptionHandler::Unauthorized => e
    @error ||= [403, e.message]
    nil
  rescue StandardError => _e
    @error ||= [401, 'Request not authenticated']
    nil
  end
end
