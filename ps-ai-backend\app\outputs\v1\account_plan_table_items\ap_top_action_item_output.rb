# frozen_string_literal: true

module V1
  class AccountPlanTableItems::ApTopActionItemOutput < ApiOutput
    def format
      {
        id: @object.id,
        description: @object.description,
        action_target: @object.action_target,
        action_date: @object.action_date,
        order: @object.order,
        how: @object.how,
        completed: @object.completed,
        ap_table_id: @object.ap_table_id
      }
    end
  end
end
