class CreateAccountPlanGroups < ActiveRecord::Migration[7.0]
  def change
    create_table :account_plan_groups do |t|
      t.string :account_plan_unique_id
      t.integer :latest_version_number, :default => 0, :null => false
      t.references :organization
      t.datetime :discarded_at, :index => true

      t.timestamps
    end

    add_index :account_plan_groups, [:organization_id, :account_plan_unique_id], name: 'plan_unique_id_per_org_index'
  end
end
