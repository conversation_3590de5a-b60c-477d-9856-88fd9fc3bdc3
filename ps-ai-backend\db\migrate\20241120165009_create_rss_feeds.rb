class CreateRssFeeds < ActiveRecord::Migration[7.0]
  def change
    create_table :rss_feeds do |t|
      t.references :rss
      t.string :url
      t.string :thumbnail
      t.string :title
      t.text :description
      t.datetime :date_published
      t.jsonb :authors, :array => true
      t.string :status, :default => 'new_feed', :index => true
      t.datetime :discarded_at, :index => true

      t.timestamps
    end
  end
end
