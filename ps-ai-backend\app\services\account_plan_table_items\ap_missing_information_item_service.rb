# frozen_string_literal: true
include ActionView::Helpers::NumberHelper

class AccountPlanTableItems::ApMissingInformationItemService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
    @user_data = user_data
  end

  def create(account_plan_id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApMissingInformationItem.new

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: "strategy",
        table_type: "missing_information",
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item = ::AccountPlanTableItems::ApMissingInformationItem.create!(params)
    end

    OpenStruct.new(
      ap_missing_information_item: ap_item
    )
  end

  def update(account_plan_id, id, params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApMissingInformationItem.find(id)

    ActiveRecord::Base.transaction do
      ap_table = ApTable.find_or_create_by(
        table_category: "strategy",
        table_type: "missing_information",
        account_plan_id: account_plan_id
      )
      params[:ap_table_id] = ap_table.id

      ap_item.update!(params)
    end

    OpenStruct.new(
      ap_missing_information_item: ap_item
    )
  end

  def index(account_plan_id, query_params)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_table = ApTable.find_by(
      table_category: "strategy",
      table_type: "missing_information",
      account_plan_id: account_plan_id
    )

    ap_item_reposity = ::AccountPlanTableItems::ApMissingInformationItems.new

    filter = query_params.slice(
      :search, :page, :per_page, :disable_pagination
    )
    filter = filter.merge(
      ap_table_id: ap_table&.id || -1
    )

    filtered_items = ap_item_reposity.filter(filter)

    OpenStruct.new(
      ap_missing_information_items: filtered_items
    )
  end

  def show(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApMissingInformationItem.find(id)
    
    OpenStruct.new(
      ap_missing_information_item: ap_item
    ) 
  end

  def destroy(account_plan_id, id)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_item = ::AccountPlanTableItems::ApMissingInformationItem.find(id)

    ActiveRecord::Base.transaction do
      ap_item.discard!
    end
  end

  def generate(account_plan_id, request_body)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    account_plan = AccountPlan.find(account_plan_id)

    verify_account_plan_ownership(account_plan, organization_user)

    ap_table = ApTable.find_by(
      table_category: "strategy",
      table_type: "missing_information",
      account_plan_id: account_plan_id
    )

    exist! ap_table.present?, on_error: "Table is not created"

    ActiveRecord::Base.transaction do
      ::AccountPlanTableItems::ApMissingInformationItem.where(ap_table_id: ap_table.id).discard_all
      
      timezone = request_body[:tz] || "+0"

      desc = generate_missing_information(account_plan, timezone)

      if !desc.blank?
        create_params = {
          ap_table_id: ap_table.id,
          description: desc
        }

        ::AccountPlanTableItems::ApMissingInformationItem.create!(create_params)
      end
    end
  end

  # Why like this?
  # because this feature change its specification every day :(
  def generate_missing_information(account_plan, timezone)
    ap_tables = ApTable.where(account_plan_id: account_plan.id)
    account_plan_group = account_plan.account_plan_group

    missing_information = ""

    ap_tables.each do |at|
      if at.table_type == "stakeholder_mapping"
        items = ::AccountPlanTableItems::ApStakeholderMappingItem.where(ap_table_id: at.id).order(:id)

        if items.size == 0
          missing_information += "# Stakeholder Mapping\n" + \
                                 "- There is no available information\n\n"
          next
        end

        overall_missing_count = 0
        fully_empty_rows_count = 0
        empty_rows = ""
        items.each_with_index do |item, idx|
          if item.name.blank? && item.job_title.blank? && item.location.blank? && item.influence.blank? && item.role.blank? && item.perception.blank? && item.advocacy.blank?
            fully_empty_rows_count += 1
            next
          end

          missing_list = []
          missing_count = 0

          if overall_missing_count > 2
            break
          end

          if item.name.blank?
            max_listed_count = 0
            overall_missing_count += 1
            
            empty_rows += "- The Name of the Stakeholder "

            if item.job_title.present? && max_listed_count < 2
              empty_rows += "for #{item.job_title}"
              max_listed_count += 1
            end
  
            if item.location.present? && max_listed_count < 2
              empty_rows += "in #{item.location}"
              max_listed_count += 1
            end
  
            if item.influence.present? && max_listed_count < 2
              empty_rows += "with Level of #{item.influence}"
              max_listed_count += 1
            end
  
            if item.role.present? && max_listed_count < 2
              empty_rows += "whose #{item.role.join("/")}"
              max_listed_count += 1
            end
  
            if item.perception.present? && max_listed_count < 2
              empty_rows += "is with the #{item.perception} of"
              max_listed_count += 1
            end
  
            if item.advocacy.present? && max_listed_count < 2
              empty_rows += "and #{item.advocacy}"
              max_listed_count += 1
            end

            empty_rows += "\n"
          else
            if item.job_title.blank?
              missing_list << "Job Title"
            end
  
            if item.location.blank?
              missing_list << "Location"
            end
  
            if item.influence.blank?
              missing_list << "Influence"
            end
  
            if item.role.blank?
              missing_list << "Role(s)"
            end
  
            if item.perception.blank?
              missing_list << "Perception"
            end
  
            if item.advocacy.blank?
              missing_list << "Advocacy"
            end

            if missing_list.size > 0
              overall_missing_count += 1
              if missing_list.size == 1
                empty_rows += "- #{item.name}'s #{missing_list[0]}\n"
              else
                t = missing_list[..(missing_list.size - 2)]
                t_rest = missing_list[-1]
                empty_rows += "- #{item.name}'s #{t.join(", ")} and #{t_rest}\n"
              end
            end
          end
        end

        if fully_empty_rows_count == items.size
          missing_information += "# Stakeholder Mapping\n" + \
                                 "- There is no available information\n\n"
          next
        end

        if overall_missing_count > 2
          missing_information += "# Stakeholder Mapping\n" + \
                                 "- There is more than two rows of missing information\n\n"
        elsif overall_missing_count > 0
          missing_information += "# Stakeholder Mapping\n" + empty_rows + "\n"
        end
      end

      if at.table_type == "wallet_share"
        items = ::AccountPlanTableItems::ApWalletShareItem.where(ap_table_id: at.id)
        types = ::AccountPlanTableItems::ApWalletShareItem.item_types.values

        if items.size == 0
          missing_information += "# Wallet Size Analysis\n" + \
                                 "- Wallet Size\n" + \
                                 "- Existing\n" + \
                                 "- Competition\n" + \
                                 "- Available\n\n"
          next
        end

        # Check for not created items
        created_types = items.pluck(:item_type)
        not_created_types = types - created_types
        if not_created_types.any?
          missing_information += "# Wallet Size Analysis\n"

          not_created_types = not_created_types.map do |type|
            if type == "ours"
              empty_rows += "- The Total Value and Service and Product Description of Existing\n"
            elsif type == "addressable"
              empty_rows += "- The Total Value of Wallet Size\n"
            elsif type == "competition"
              empty_rows += "- The Total Value and Service and Product Description of Competition\n"
            elsif type == "available"
              empty_rows += "- The Total Value of Available\n"
            end
          end
        end

        empty_rows = ""
        missing_types = []
        items.each_with_index do |item, idx|
          if item.item_type.nil?
            next
          elsif (item.description.blank? && item.shared_type_analysis.to_i > 0) && ["available", "addressable"].include?(item.item_type)
            next
          elsif ["available"].include?(item.item_type)
            wallet_size = items.find { |i| i.item_type == "addressable" }
            existing = items.find { |i| i.item_type == "ours" }
            competition = items.find { |i| i.item_type == "competition" }

            available_val = wallet_size&.shared_type_analysis.to_i - existing&.shared_type_analysis.to_i - competition&.shared_type_analysis.to_i

            if available_val <= 0
              empty_rows += "- The Total Value of Available\n"
            end
          elsif (item.description.present? && item.shared_type_analysis.to_i > 0)
            next
          else
            formatted_type = ""
            if item.item_type == "ours"
              formatted_type = "Existing"
            elsif item.item_type == "addressable"
              formatted_type = "Wallet Size"
            else
              formatted_type = item.item_type.titleize
            end

            if (item.description.blank? && item.shared_type_analysis.to_i == 0)
              empty_rows += "- The Total Value and Service and Product Description of #{formatted_type}\n"
            elsif item.description.blank?
              empty_rows += "- The Service and Product Description of #{formatted_type}\n"
            elsif item.shared_type_analysis.to_i == 0
              empty_rows += "- The Total Value of #{formatted_type}\n"
            end
          end
        end

        if !empty_rows.blank? && not_created_types.any?
          missing_information += empty_rows + "\n"
        elsif !empty_rows.blank? && !not_created_types.any?
          missing_information += "# Wallet Size Analysis\n" + empty_rows + "\n"
        elsif empty_rows.blank? && not_created_types.any?
          missing_information += "\n"
        end
      end

      if at.table_type == "svot"
        items = ::AccountPlanTableItems::ApSvotItem.where(ap_table_id: at.id)
        types = ::AccountPlanTableItems::ApSvotItem.item_types.values

        if items.size == 0
          missing_information += "# S.V.O.T Analysis\n" + \
                                 "- Strengths\n" + \
                                 "- Vulnerabilities\n" + \
                                 "- Opportunities\n" + \
                                 "- Threats\n\n"
          next
        end

        created_types = items.pluck(:item_type)
        not_created_types = (types - created_types).uniq
        if not_created_types.any?
          missing_information += "# S.V.O.T Analysis\n"

          not_created_types = not_created_types.map do |type|
            if type == "strength"
              "Strengths"
            elsif type == "vulnerability"
              "Vulnerabilities"
            elsif type == "opportunity"
              "Opportunities"
            elsif type == "threat"
              "Threats"
            else
              type
            end
          end

          not_created_types.each do |t|
            missing_information += "- #{t}\n"
          end
        end

        empty_rows = ""
        missing_types = []
        items.each_with_index do |item, idx|
          if item.item_type.nil?
            next
          elsif item.description.blank?
            missing_types << item.item_type
          end
        end

        missing_types = missing_types.uniq
        missing_types = missing_types.map do |x|
          if x == "strength"
            "Strengths"
          elsif x == "vulnerability"
            "Vulnerabilities"
          elsif x == "opportunity"
            "Opportunities"
          elsif x == "threat"
            "Threats"
          end
        end

        if missing_types.size > 0
          missing_types.each do |t|
            empty_rows += "- #{t}\n"
          end
        end

        if !empty_rows.blank? && not_created_types.any?
          missing_information += empty_rows + "\n"
        elsif !empty_rows.blank? && !not_created_types.any?
          missing_information += "# S.V.O.T Analysis\n" + empty_rows + "\n"
        elsif empty_rows.blank? && not_created_types.any?
          missing_information += "\n"
        end
      end

      if at.table_type == "historic_revenue"
        items = ::AccountPlanTableItems::ApHistoricRevenueItem.where(ap_table_id: at.id).order(:id)

        if items.size == 0
          missing_information += "# Historic Revenue\n" + \
                                  "- There is no available information\n\n"
          next
        end

        empty_rows = ""
        fully_empty_rows_count = 0
        overall_missing_count = 0
        items.each_with_index do |item, idx|
          if item.product_service_name.blank?
            if item.time_month.blank? && item.value.to_i == 0
              fully_empty_rows_count += 1
              next
            elsif item.value.to_i == 0
              empty_rows += "- The Total Value and Service and Product Description in the Past #{item.time_month} Months\n"
              overall_missing_count += 1
            elsif item.time_month.blank?
              empty_rows += "- Service and Product Description and the Period of Past Month for #{convert_currency_text(account_plan_group.currency)} #{number_with_delimiter(item.value)}\n"
              overall_missing_count += 1
            else
              empty_rows += "- Service and Product Description for #{convert_currency_text(account_plan_group.currency)} #{number_with_delimiter(item.value)} in the Past #{item.time_month} Months\n"
              overall_missing_count += 1
            end
          else
            if item.time_month.blank? && item.value.to_i == 0
              empty_rows += "- The Total Value and the Period of Past Month for #{item.product_service_name}\n"
              overall_missing_count += 1
            elsif item.value.to_i == 0
              empty_rows += "- The Total Value for #{item.product_service_name}\n"
              overall_missing_count += 1
            elsif item.time_month.blank?
              empty_rows += "- The Past Date for #{item.product_service_name}\n"
              overall_missing_count += 1
            end
          end
        end

        if fully_empty_rows_count == items.size
          missing_information += "# Historic Revenue\n" + \
                                 "- There is no available information\n\n"
          next
        end

        if overall_missing_count > 2
          missing_information += "# Historic Revenue\n" + \
                                 "- There is more than two rows of missing information\n\n"
        elsif overall_missing_count > 0
          missing_information += "# Historic Revenue\n" + empty_rows + "\n"
        end
      end

      if at.table_type == "current_revenue"
        items = ::AccountPlanTableItems::ApCurrentRevenueItem.where(ap_table_id: at.id).order(:id)

        if items.size == 0
          missing_information += "# Existing Revenue\n" + \
                                 "- There is no available information\n\n"
          next
        end

        empty_rows = ""
        fully_empty_rows_count = 0
        overall_missing_count = 0
        items.each_with_index do |item, idx|
          if item.product_service_name.blank?
            if item.renewal_date.blank? && item.value.to_i == 0
              fully_empty_rows_count += 1
              next
            elsif item.value.to_i == 0
              empty_rows += "- The Total Value and Service and Product Description that will be renewed on #{convert_timestamp_to_utc_offset(item.renewal_date, timezone).strftime("%d %B %Y")}\n"
              overall_missing_count += 1
            elsif item.renewal_date.blank?
              empty_rows += "- Service and Product Description and the Renewal Date for #{convert_currency_text(account_plan_group.currency)} #{number_with_delimiter(item.value)}\n"
              overall_missing_count += 1
            else
              empty_rows += "- Service and Product Description for #{convert_currency_text(account_plan_group.currency)} #{number_with_delimiter(item.value)} that will be renewed on #{convert_timestamp_to_utc_offset(item.renewal_date, timezone).strftime("%d %B %Y")}\n"
              overall_missing_count += 1
            end
          else
            if item.renewal_date.blank? && item.value.to_i == 0
              empty_rows += "- The Total Value and the Renewal Date for #{item.product_service_name}\n"
              overall_missing_count += 1
            elsif item.value.to_i == 0
              empty_rows += "- The Total Value for #{item.product_service_name}\n"
              overall_missing_count += 1
            elsif item.renewal_date.blank?
              empty_rows += "- The Renewal Date for #{item.product_service_name}\n"
              overall_missing_count += 1
            end
          end
        end

        if fully_empty_rows_count == items.size
          missing_information += "# Existing Revenue\n" + \
                                 "- There is no available information\n\n"
          next
        end

        if overall_missing_count > 2
          missing_information += "# Existing Revenue\n" + \
                                 "- There is more than two rows of missing information\n\n"
        elsif overall_missing_count > 0
          missing_information += "# Existing Revenue\n" + empty_rows + "\n"
        end
      end

      if at.table_type == "current_opportunity"
        items = ::AccountPlanTableItems::ApCurrentOpportunityItem.where(ap_table_id: at.id).order(:id)

        if items.size == 0
          missing_information += "# Current Opportunities in Play\n" + \
                                 "- There is no available information\n\n"
          next
        end

        empty_rows = ""
        fully_empty_rows_count = 0
        overall_missing_count = 0
        items.each_with_index do |item, idx|
          if item.product_service_name.blank?
            if item.close_date.blank? && item.value.to_i == 0
              fully_empty_rows_count += 1
              next
            elsif item.value.to_i == 0
              empty_rows += "- The Total Value and Service and Product Description Closing on #{convert_timestamp_to_utc_offset(item.close_date, timezone).strftime("%d %B %Y")}\n"
              overall_missing_count += 1
            elsif item.close_date.blank?
              empty_rows += "- Service and Product Description and the Closing Date for #{convert_currency_text(account_plan_group.currency)} #{number_with_delimiter(item.value)}\n"
              overall_missing_count += 1
            else
              empty_rows += "- Service and Product Description for #{convert_currency_text(account_plan_group.currency)} #{number_with_delimiter(item.value)} Closing on #{convert_timestamp_to_utc_offset(item.close_date, timezone).strftime("%d %B %Y")}\n"
              overall_missing_count += 1
            end
          else
            if item.close_date.blank? && item.value.to_i == 0
              empty_rows += "- The Total Value and the Closing Date for #{item.product_service_name}\n"
              overall_missing_count += 1
            elsif item.value.to_i == 0
              empty_rows += "- The Total Value for #{item.product_service_name}\n"
              overall_missing_count += 1
            elsif item.close_date.blank?
              empty_rows += "- The Closing Date for #{item.product_service_name}\n"
              overall_missing_count += 1
            end
          end
        end

        if fully_empty_rows_count == items.size
          missing_information += "# Current Opportunities in Play\n" + \
                                 "- There is no available information\n\n"
          next
        end

        if overall_missing_count > 2
          missing_information += "# Current Opportunities in Play\n" + \
                                 "- There is more than two rows of missing information\n\n"
        elsif overall_missing_count > 0
          missing_information += "# Current Opportunities in Play\n" + empty_rows + "\n"
        end
      end

      if at.table_type == "potential_opportunity"
        items = ::AccountPlanTableItems::ApPotentialOpportunityItem.where(ap_table_id: at.id).order(:id)

        if items.size == 0
          missing_information += "# Potential Cross/Up-sell Opportunities\n" + \
                                 "- There is no available information\n\n"
          next
        end

        empty_rows = ""
        fully_empty_rows_count = 0
        overall_missing_count = 0
        items.each_with_index do |item, idx|
          if item.product_service_name.blank?
            if item.close_date.blank? && item.value.to_i == 0
              fully_empty_rows_count += 1
              next
            elsif item.value.to_i == 0
              empty_rows += "- The Total Value and Service and Product Description Closing on #{convert_timestamp_to_utc_offset(item.close_date, timezone).strftime("%d %B %Y")}\n"
              overall_missing_count += 1
            elsif item.close_date.blank?
              empty_rows += "- Service and Product Description and the Closing Date for #{convert_currency_text(account_plan_group.currency)} #{number_with_delimiter(item.value)}\n"
              overall_missing_count += 1
            else
              empty_rows += "- Service and Product Description for #{convert_currency_text(account_plan_group.currency)} #{number_with_delimiter(item.value)} Closing on #{convert_timestamp_to_utc_offset(item.close_date, timezone).strftime("%d %B %Y")}\n"
              overall_missing_count += 1
            end
          else
            if item.close_date.blank? && item.value.to_i == 0
              empty_rows += "- The Total Value and the Closing Date for #{item.product_service_name}\n"
              overall_missing_count += 1
            elsif item.value.to_i == 0
              empty_rows += "- The Total Value for #{item.product_service_name}\n"
              overall_missing_count += 1
            elsif item.close_date.blank?
              empty_rows += "- The Closing Date for #{item.product_service_name}\n"
              overall_missing_count += 1
            end
          end      
        end

        if fully_empty_rows_count == items.size
          missing_information += "# Potential Cross/Up-sell Opportunities\n" + \
                                 "- There is no available information\n\n"
          next
        end

        if overall_missing_count > 2
          missing_information += "# Potential Cross/Up-sell Opportunities\n" + \
                                 "- There is more than two rows of missing information\n\n"
        elsif overall_missing_count > 0
          missing_information += "# Potential Cross/Up-sell Opportunities\n" + empty_rows + "\n"
        end
      end

      if at.table_type == "revenue_forecast"
        items = ::AccountPlanTableItems::ApRevenueForecastItem.where(ap_table_id: at.id).order(:id)

        if items.size == 0
          missing_information += "# Forecast Revenue\n" + \
                                 "- There is no available information\n\n"
          next
        end

        empty_rows = ""
        empty_timespan_count = 0
        fully_empty_rows_count = 0
        empty_lows = []
        empty_realistics = []
        empty_highs = []
        items.each_with_index do |item, idx|
          # if all empty, just assume this row does not exist.
          if item.timespan.blank? && item.low_scenario.blank? && item.realistic_scenario.blank? && item.high_scenario.blank?
            fully_empty_rows_count += 1
            next
          end

          if item.timespan.blank?
            empty_timespan_count += 1
            next
          end

          if item.low_scenario.to_i == 0
            empty_lows << item.timespan
          end

          if item.realistic_scenario.to_i == 0
            empty_realistics << item.timespan
          end

          if item.high_scenario.to_i == 0
            empty_highs << item.timespan
          end
        end

        if fully_empty_rows_count == items.size
          missing_information += "# Forecast Revenue\n" + \
                                 "- There is no available information\n\n"
          next
        end

        if empty_lows.present?
          if empty_lows.size == 1
            empty_rows += "- Low for #{empty_lows[0]}-month\n"
          else
            t = empty_lows[..(empty_lows.size - 2)].map { |value| "#{value}-month" }
            t_rest = empty_lows[-1]
            empty_rows += "- Low for #{t.join(", ")} and #{t_rest}-month\n"
          end
        end

        if empty_realistics.present?
          if empty_realistics.size == 1
            empty_rows += "- Realistic for #{empty_realistics[0]}-month\n"
          else
            t = empty_realistics[..(empty_realistics.size - 2)].map { |value| "#{value}-month" }
            t_rest = empty_realistics[-1]
            empty_rows += "- Realistic for #{t.join(", ")} and #{t_rest}-month\n"
          end
        end

        if empty_highs.present?
          if empty_highs.size == 1
            empty_rows += "- High for #{empty_highs[0]}-month\n"
          else
            t = empty_highs[..(empty_highs.size - 2)].map { |value| "#{value}-month" }
            t_rest = empty_highs[-1]
            empty_rows += "- High for #{t.join(", ")} and #{t_rest}-month\n"
          end
        end

        if empty_timespan_count > 0
          empty_rows += "- Forecast projections for other additional future time periods\n"
        end

        if !empty_rows.blank?
          missing_information += "# Forecast Revenue\n" + empty_rows + "\n"
        end
      end

      if at.table_type == "targeted_perception_development"
        items = ::AccountPlanTableItems::ApTargetedPerceptionDevelopmentItem
        .left_joins(:ap_stakeholder_mapping_item)
        .where(ap_table_id: at.id)

        empty_rows = ""
        overall_missing_count = 0
        items.each_with_index do |item, idx|
          missing_col = []

          if item.ap_stakeholder_mapping_item.blank? || item.ap_stakeholder_mapping_item&.name.blank?
            # all 3 cols missing
            if item.action.blank? && item.result.blank? && item.leverage.blank?
              overall_missing_count += 1
              next
            end

            # all 3 cols present
            if item.action.present? && item.result.present? && item.leverage.present?
              empty_rows += "- We are missing the Stakeholder's Name for\n" + \
                            "  - #{item.action}\n" + \
                            "  - #{item.result}\n" + \
                            "  - #{item.leverage}\n"
              next
            end

            # 1 col missing
            if item.action.blank? && item.result.present? && item.leverage.present?
              empty_rows += "- We are missing the Stakeholder's Name and the Perception Change for\n" + \
                            "  - #{item.result}\n" + \
                            "  - #{item.leverage}\n"
              next
            end

            if item.action.present? && item.result.blank? && item.leverage.present?
              empty_rows += "- We are missing the Stakeholder's Name and the Result Created for\n" + \
                            "  - #{item.action}\n" + \
                            "  - #{item.leverage}\n"
              next
            end

            if item.action.present? && item.result.present? && item.leverage.blank?
              empty_rows += "- We are missing the Stakeholder's Name and the Leveraged Strength for\n" + \
                            "  - #{item.action}\n" + \
                            "  - #{item.result}\n"
              next
            end

            # 2 cols missing
            if item.action.present? && item.result.blank? && item.leverage.blank?
              empty_rows += "- We are missing the Stakeholder's Name, Result Created and the Leveraged Strength for\n" + \
                            "  - #{item.action}\n"
              next
            end

            if item.action.blank? && item.result.present? && item.leverage.blank?
              empty_rows += "- We are missing the Stakeholder's Name, Perception Change and the Leveraged Strength for\n" + \
                            "  - #{item.result}\n"
              next
            end

            if item.action.blank? && item.result.blank? && item.leverage.present?
              empty_rows += "- We are missing the Stakeholder's Name, Perception Change and the Result Created for\n" + \
                            "  - #{item.leverage}\n"
              next
            end
          else
            if item.action.blank?
              missing_col << 'Perception Change'
              # empty_rows += "- Perception for #{item.ap_stakeholder_mapping_item&.name.blank? ? "No Name" : item.ap_stakeholder_mapping_item&.name}\n"
            end
  
            if item.result.blank?
              missing_col << 'Result Created'
              # empty_rows += "- Result for #{item.ap_stakeholder_mapping_item&.name.blank? ? "No Name" : item.ap_stakeholder_mapping_item&.name}\n"
            end
  
            if item.leverage.blank?
              missing_col << "Leveraged Strength"
              # empty_rows += "- Leveraged Strength for #{item.ap_stakeholder_mapping_item&.name.blank? ? "No Name" : item.ap_stakeholder_mapping_item&.name}\n"
            end
  
            if missing_col.size > 0
              empty_rows += "- We are missing #{item.ap_stakeholder_mapping_item.name}'s\n"
              missing_col.each do |missing|
                empty_rows += "  - #{missing}\n"
              end
            end
          end
        end
        
        if overall_missing_count == 2
          missing_information += "# Strategic Perception Development\n" + \
                                 "- There is no available information\n\n"
        elsif overall_missing_count < 2 && empty_rows.present?
          missing_information += "# Strategic Perception Development\n" + empty_rows + "\n"
        end
      end

      if at.table_type == "top_action"
        items = ::AccountPlanTableItems::ApTopActionItem.where(ap_table_id: at.id).order("ap_top_action_items.order ASC NULLS FIRST")

        empty_rows = ""
        items.each_with_index do |item, idx|
          missing_col = []

          if item.action_target.blank?
            missing_col << "Owner"
          end

          if item.description.blank?
            missing_col << "What will you do?"
          end

          if item.how.blank?
            missing_col << "How will you do it?"
          end

          if item.action_date.blank?
            missing_col << "Due Date"
          end
          
          if missing_col.size > 0
            empty_rows += "# Action #{idx + 1}\n"
            missing_col.each do |missing|
              empty_rows += "- #{missing}\n"
            end
            # if missing_col.size == 1
            #   empty_rows += "- #{missing_col[0]} for #{item.action_target}\n"
            # else
            #   t = missing_col[..(missing_col.size - 2)]
            #   t_rest = missing_col[-1]
            #   empty_rows += "- #{t.join(", ")} and/or #{t_rest} for #{item.action_target}\n"
            # end
          end
        end

        if !empty_rows.blank?
          missing_information += "# Strategic Actions\n" + empty_rows + "\n"
        end
      end

      if at.table_type == "client_meeting_schedule"
        items = ::AccountPlanTableItems::ApClientMeetingScheduleItem.includes(:ap_stakeholder_mapping_item).where(ap_table_id: at.id).order(:id)

        empty_rows = ""
        fully_empty_rows_count = 0
        overall_missing_count = 0
        
        items.each_with_index do |item, idx|
          missing_col = :none

          if item.ap_stakeholder_mapping_item.blank? || item.ap_stakeholder_mapping_item&.name.blank?
            missing_col = :stakeholder
          end

          if item.meeting_date.blank? && missing_col == :none
            missing_col = :date
          elsif item.meeting_date.blank? && missing_col == :stakeholder
            missing_col = :both
            fully_empty_rows_count += 1
          end

          # if missing_col == :both
            # empty_rows += "- Who are you meeting with? On what date are you meeting?\n"
          if missing_col == :stakeholder
            empty_rows += "- On #{convert_timestamp_to_utc_offset(item.meeting_date, timezone).strftime("%d %B %Y")}, Who are you meeting with?\n"
            overall_missing_count += 1
          elsif missing_col == :date
            empty_rows += "- On what date are you meeting with #{item.ap_stakeholder_mapping_item&.name}?\n"
            overall_missing_count += 1
          end
        end

        if fully_empty_rows_count == items.size
          missing_information += "# Client Meeting Schedule\n" + \
                                 "- There is no available information\n\n"
          next
        end

        if overall_missing_count > 2
          missing_information += "# Client Meeting Schedule\n" + \
                                 "- There is more than two rows of missing information\n\n"
        elsif overall_missing_count > 0
          missing_information += "# Client Meeting Schedule\n" + empty_rows + "\n"
        end
      end
    end

    return missing_information
  end

  def convert_timestamp_to_utc_offset(timestamp, utc_offset_str)
    # Parse the UTC offset string (e.g., "UTC+8" => 8, "UTC-7" => -7)
    offset_hours = utc_offset_str.gsub('UTC', '').to_i
    
    # Convert the timestamp to the specified offset
    timestamp.in_time_zone(ActiveSupport::TimeZone[offset_hours * 3600])
  end

  def convert_currency_text(currency_text)
    return AccountPlanGroup.currency_text_to_sym[currency_text] || currency_text
  end
end
