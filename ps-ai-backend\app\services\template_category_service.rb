# frozen_string_literal: true

class TemplateCategoryService < ::AppService
  def initialize(user_data)
    @user = user_data[:user]
    @organization_user = user_data[:organization_user]
    @organization = user_data[:organization]
  end

  def create(params)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    verify_roles(['owner'], organization_user)

    params[:organization_id] = organization_user.organization_id
    params[:validated] = true
    params[:general_category] = false

    TemplateCategory.create!(params)
  end

  def index(query)
    verify_organization_tier(['superuser'], @organization)
    organization_user = verify_user_organization(@user, @organization_user, @organization)
    
    template_categories = ::TemplateCategories.new

    filter = query.slice(:search, :page, :per_page)
    filter = filter.merge(
      organization_id_with_general_category: organization_user.organization_id
    )

    template_categories.filter(filter)
  end
end
