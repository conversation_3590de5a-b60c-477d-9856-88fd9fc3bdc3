.table-style-default {
  .ag-checked::after {
    @apply text-secondary-500;
  }

  .ag-root-wrapper {
    @apply rounded-none border-0 font-inter;
  }

  .ag-header-cell-comp-wrapper {
    @apply !justify-center;
  }

  .ag-header {
    @apply rounded-xl;
  }

  .ag-header-cell {
    @apply h-[44px] border bg-gradient text-sm font-bold text-white hover:!bg-gradient;
  }

  .ag-header-cell-resize::after {
    @apply bg-transparent;
  }

  .ag-header-cell-label {
    @apply w-full justify-center text-center leading-6;
  }

  .ag-row {
    @apply border-b-neutral-100 text-primary-500;
  }

  .ag-row-even {
    @apply !bg-neutral-100;
  }

  .ag-row-odd {
    @apply bg-neutral-200;
  }

  .ag-row-focus {
    @apply bg-inherit;
  }

  .ag-row-selected {
    @apply bg-primary-50;
  }
  .ag-row-selected::before {
    @apply bg-inherit;
  }
  .ag-row-hover.ag-row-selected::before {
    @apply bg-none;
  }
  .ag-row-hover::before {
    @apply bg-primary-100;
  }

  .ag-cell {
    @apply !flex items-center border-white text-sm;
  }

  .ag-checkbox-input-wrapper {
    @apply shadow-none;
  }

  .ag-icon::before {
    color: white;
  }
}

.table-style-alt-1 {
  .ag-checked::after {
    @apply text-secondary-500;
  }

  .ag-root-wrapper {
    @apply rounded-none border-0 font-inter;
  }

  .ag-header-cell {
    @apply h-[44px] border-r border-neutral-300 bg-primary-100 text-3xl font-bold text-black hover:!bg-primary-100;
  }

  [col-id="ag-Grid-ControlsColumn"] {
    @apply flex items-center justify-center;
  }

  .ag-selection-checkbox {
    @apply mr-0 !h-fit;
  }

  .ag-header-select-all {
    @apply mr-0 !h-fit;
  }

  .ag-header-cell-comp-wrapper {
    @apply !justify-center;
  }

  .ag-header-cell-label {
    @apply leading-9;
  }

  .ag-row {
    @apply border-b-neutral-300;
  }

  .ag-cell {
    @apply flex items-center border-r-neutral-300 bg-white py-res-y-xl text-3xl leading-9;
  }

  .ag-cell-span {
    @apply border-b-neutral-300;
  }

  .ag-cell:first-child {
    @apply border-l-neutral-300;
  }

  .ag-cell-focus {
    @apply border-r-neutral-300;
  }

  .ag-cell-wrapper {
    @apply w-full;
  }

  .ag-cell-inline-editing {
    @apply !h-full !py-0;
  }

  .ag-cell-edit-wrapper {
    @apply !h-full;
  }

  .ag-row-focus {
    @apply bg-inherit;
  }

  .ag-row-inline-editing {
    @apply z-[unset];
  }

  .ag-row-selected {
    @apply bg-primary-50;
  }

  .ag-row-selected::before {
    @apply bg-inherit;
  }

  .ag-row-hover.ag-row-selected::before {
    @apply bg-none;
  }

  .ag-row-hover::before {
    @apply bg-primary-100;
  }

  .ag-header-cell-resize::after {
    @apply bg-transparent;
  }

  .ag-checkbox-input-wrapper {
    @apply shadow-none;
  }

  .ag-body-vertical-scroll-viewport {
    @apply overflow-y-auto;
  }
}

.table-style-alt-1-secondary {
  .ag-header-cell {
    @apply !bg-neutral-200 hover:!bg-neutral-200;
  }
}
