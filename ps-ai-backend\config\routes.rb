require_relative 'routes_helpers'
require 'sidekiq/web'
require 'sidekiq/cron/web'

Rails.application.routes.draw do
  extend RoutesHelpers

  namespace :v1 do
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html
    get 's3/uploads/presign', to: 's3#upload_s3'

    resources :template_categories, only: [:create, :index]
    resources :model_templates, only: [:create, :index, :show, :update, :destroy]
    resources :model_template_variables, only: [:create, :index, :update, :destroy]
    get 'account_plans/active_top_actions', to: 'account_plans#active_top_actions'
    resources :account_plans, only: [:create, :index, :show, :update, :destroy] do
      
      scope module: :account_plan_table_items do
        resources :crm_contacts, only: [:index], path: 'crm_contacts'

        resources :ap_stakeholder_mapping_items,
                  path: 'stakeholder_mapping',
                  only: [:create, :index, :show, :update, :destroy]
          collection do
            post 'import', to: 'ap_stakeholder_mapping_items#import'
          end
                  

        resources :ap_wallet_share_items,
                  path: 'wallet_share',
                  only: [:create, :index, :show, :update, :destroy]

        resources :ap_circumstantial_analysis_items,
                  path: 'circumstantial_analysis',
                  only: [:create, :index, :show, :update, :destroy]
        post 'circumstantial_analysis/generate', to: 'ap_circumstantial_analysis_items#generate'

        resources :ap_svot_items,
                  path: 'svot',
                  only: [:create, :index, :show, :update, :destroy]

        resources :ap_insight_and_perspective_items,
                  path: 'insight_and_perspective',
                  only: [:create, :index, :show, :update, :destroy]
        post 'insight_and_perspective/generate', to: 'ap_insight_and_perspective_items#generate'

        resources :ap_historic_revenue_items,
                  path: 'historic_revenue',
                  only: [:create, :index, :show, :update, :destroy]

        resources :ap_current_revenue_items,
                  path: 'current_revenue',
                  only: [:create, :index, :show, :update, :destroy]

        resources :ap_current_opportunity_items,
                  path: 'current_opportunity',
                  only: [:create, :index, :show, :update, :destroy]

        resources :ap_potential_opportunity_items,
                  path: 'potential_opportunity',
                  only: [:create, :index, :show, :update, :destroy]

        resources :ap_revenue_forecast_items,
                  path: 'revenue_forecast',
                  only: [:create, :index, :show, :update, :destroy]

        resources :ap_missing_information_items,
                  path: 'missing_information',
                  only: [:create, :index, :show, :update, :destroy]
        post 'missing_information/generate', to: 'ap_missing_information_items#generate'

        resources :ap_targeted_perception_development_items,
                  path: 'targeted_perception_development',
                  only: [:create, :index, :show, :update, :destroy]

        resources :ap_action_plan_items,
                  path: 'action_plan',
                  only: [:create, :index, :show, :update, :destroy]
        post 'action_plan/generate', to: 'ap_action_plan_items#generate'

        resources :ap_top_action_items,
                  path: 'top_action',
                  only: [:create, :index, :show, :update, :destroy]
                  
        resources :ap_client_meeting_schedule_items,
                  path: 'client_meeting_schedule',
                  only: [:create, :index, :show, :update, :destroy]
      end
    end

    resources :account_plan_groups, only: [:create, :index, :show, :update, :destroy]
    # resources :llm_considerations, only: [:index]
    resources :rsses, only: [:index]
    post 'rsses/webhook/:access_key', to: 'rsses#handle_webhook'

    resources :industries, only: [:create, :index, :update, :destroy]
    resources :organizations, only: [:create, :index, :show, :update, :destroy] do
      post 'crms', to: 'organizations#create_crm'
      put 'crms/:id', to: 'organizations#update_crm'
    end

    resources :user_managements, only: [:index]
    post 'user_managements/invite', to: 'user_managements#create_invitation'
    post 'user_managements/accept_invitation', to: 'user_managements#accept_invitation'
    get 'user_managements/invitation_code_details', to: 'user_managements#detail_invitation'
    put 'user_managements/user_permissions/:user_id', to: 'user_managements#update_user_permissions'
    get 'user_managements/user_permissions/:user_id', to: 'user_managements#get_user_permissions'
    post 'user_managements/change_password_request', to: 'user_managements#change_password_request'
    post 'user_managements/change_password', to: 'user_managements#change_password'

    resources :users, only: [:show, :update, :destroy]

    get 'utilities/currency_exchange_rates', to: 'utilities#currency_exchange_rates'

    post 'login', to: 'auth#authenticate'
    get 'auth', to: 'auth#show'
  end

  mount Sidekiq::Web => "/sidekiq"

  # Defines the root path route ("/")
  #################
  # INSTRUMENTATION ENDPOINTS
  #################

  get 'version' => proc { |_env|
    rack_json(version: PsaiApi::VERSION, up_since: PsaiApi::UP_SINCE)
  }
end
