import _ from "lodash";

import { PdfTable, PdfTableCell, PdfTableRow, PdfTableTitle } from "..";
import { AccountPlanAnalysisPDFProps } from "../../download-analysis";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { AccountPlanTableType } from "@/features/account-plan/types";

export const StrategicPerceptionPDFTable = ({
  data,
}: {
  data?: AccountPlanAnalysisPDFProps["targetedPerceptionDevelopmentList"];
}) => {
  return (
    <PdfTable>
      <PdfTableTitle>
        {getAccountPlanTableName(
          AccountPlanTableType.TARGETED_PERCEPTION_DEVELOPMENT
        )}
      </PdfTableTitle>

      {data?.map((row, idx) => (
        <PdfTableRow key={idx}>
          <PdfTableCell style={{ flex: 0.6 }}>
            {row.ap_stakeholder_mapping_item?.name}
          </PdfTableCell>
          <PdfTableCell>{row.action}</PdfTableCell>
          <PdfTableCell>{row.result}</PdfTableCell>
          <PdfTableCell>{row.leverage}</PdfTableCell>
        </PdfTableRow>
      ))}
    </PdfTable>
  );
};
