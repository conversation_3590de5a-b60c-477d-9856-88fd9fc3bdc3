"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/orderedmap";
exports.ids = ["vendor-chunks/orderedmap"];
exports.modules = {

/***/ "(ssr)/./node_modules/orderedmap/dist/index.js":
/*!***********************************************!*\
  !*** ./node_modules/orderedmap/dist/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// ::- Persistent data structure representing an ordered mapping from\n// strings to values, with some convenient update methods.\nfunction OrderedMap(content) {\n  this.content = content;\n}\n\nOrderedMap.prototype = {\n  constructor: OrderedMap,\n\n  find: function(key) {\n    for (var i = 0; i < this.content.length; i += 2)\n      if (this.content[i] === key) return i\n    return -1\n  },\n\n  // :: (string) → ?any\n  // Retrieve the value stored under `key`, or return undefined when\n  // no such key exists.\n  get: function(key) {\n    var found = this.find(key);\n    return found == -1 ? undefined : this.content[found + 1]\n  },\n\n  // :: (string, any, ?string) → OrderedMap\n  // Create a new map by replacing the value of `key` with a new\n  // value, or adding a binding to the end of the map. If `newKey` is\n  // given, the key of the binding will be replaced with that key.\n  update: function(key, value, newKey) {\n    var self = newKey && newKey != key ? this.remove(newKey) : this;\n    var found = self.find(key), content = self.content.slice();\n    if (found == -1) {\n      content.push(newKey || key, value);\n    } else {\n      content[found + 1] = value;\n      if (newKey) content[found] = newKey;\n    }\n    return new OrderedMap(content)\n  },\n\n  // :: (string) → OrderedMap\n  // Return a map with the given key removed, if it existed.\n  remove: function(key) {\n    var found = this.find(key);\n    if (found == -1) return this\n    var content = this.content.slice();\n    content.splice(found, 2);\n    return new OrderedMap(content)\n  },\n\n  // :: (string, any) → OrderedMap\n  // Add a new key to the start of the map.\n  addToStart: function(key, value) {\n    return new OrderedMap([key, value].concat(this.remove(key).content))\n  },\n\n  // :: (string, any) → OrderedMap\n  // Add a new key to the end of the map.\n  addToEnd: function(key, value) {\n    var content = this.remove(key).content.slice();\n    content.push(key, value);\n    return new OrderedMap(content)\n  },\n\n  // :: (string, string, any) → OrderedMap\n  // Add a key after the given key. If `place` is not found, the new\n  // key is added to the end.\n  addBefore: function(place, key, value) {\n    var without = this.remove(key), content = without.content.slice();\n    var found = without.find(place);\n    content.splice(found == -1 ? content.length : found, 0, key, value);\n    return new OrderedMap(content)\n  },\n\n  // :: ((key: string, value: any))\n  // Call the given function for each key/value pair in the map, in\n  // order.\n  forEach: function(f) {\n    for (var i = 0; i < this.content.length; i += 2)\n      f(this.content[i], this.content[i + 1]);\n  },\n\n  // :: (union<Object, OrderedMap>) → OrderedMap\n  // Create a new map by prepending the keys in this map that don't\n  // appear in `map` before the keys in `map`.\n  prepend: function(map) {\n    map = OrderedMap.from(map);\n    if (!map.size) return this\n    return new OrderedMap(map.content.concat(this.subtract(map).content))\n  },\n\n  // :: (union<Object, OrderedMap>) → OrderedMap\n  // Create a new map by appending the keys in this map that don't\n  // appear in `map` after the keys in `map`.\n  append: function(map) {\n    map = OrderedMap.from(map);\n    if (!map.size) return this\n    return new OrderedMap(this.subtract(map).content.concat(map.content))\n  },\n\n  // :: (union<Object, OrderedMap>) → OrderedMap\n  // Create a map containing all the keys in this map that don't\n  // appear in `map`.\n  subtract: function(map) {\n    var result = this;\n    map = OrderedMap.from(map);\n    for (var i = 0; i < map.content.length; i += 2)\n      result = result.remove(map.content[i]);\n    return result\n  },\n\n  // :: () → Object\n  // Turn ordered map into a plain object.\n  toObject: function() {\n    var result = {};\n    this.forEach(function(key, value) { result[key] = value; });\n    return result\n  },\n\n  // :: number\n  // The amount of keys in this map.\n  get size() {\n    return this.content.length >> 1\n  }\n};\n\n// :: (?union<Object, OrderedMap>) → OrderedMap\n// Return a map with the given content. If null, create an empty\n// map. If given an ordered map, return that map itself. If given an\n// object, create a map from the object's properties.\nOrderedMap.from = function(value) {\n  if (value instanceof OrderedMap) return value\n  var content = [];\n  if (value) for (var prop in value) content.push(prop, value[prop]);\n  return new OrderedMap(content)\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OrderedMap);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3JkZXJlZG1hcC9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxvQkFBb0IseUJBQXlCO0FBQzdDO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQix5QkFBeUI7QUFDN0M7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHdCQUF3QjtBQUM1QztBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QyxzQkFBc0I7QUFDOUQ7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxpRUFBZSxVQUFVLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcy1haS13ZWIvLi9ub2RlX21vZHVsZXMvb3JkZXJlZG1hcC9kaXN0L2luZGV4LmpzPzRiZGYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gOjotIFBlcnNpc3RlbnQgZGF0YSBzdHJ1Y3R1cmUgcmVwcmVzZW50aW5nIGFuIG9yZGVyZWQgbWFwcGluZyBmcm9tXG4vLyBzdHJpbmdzIHRvIHZhbHVlcywgd2l0aCBzb21lIGNvbnZlbmllbnQgdXBkYXRlIG1ldGhvZHMuXG5mdW5jdGlvbiBPcmRlcmVkTWFwKGNvbnRlbnQpIHtcbiAgdGhpcy5jb250ZW50ID0gY29udGVudDtcbn1cblxuT3JkZXJlZE1hcC5wcm90b3R5cGUgPSB7XG4gIGNvbnN0cnVjdG9yOiBPcmRlcmVkTWFwLFxuXG4gIGZpbmQ6IGZ1bmN0aW9uKGtleSkge1xuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgdGhpcy5jb250ZW50Lmxlbmd0aDsgaSArPSAyKVxuICAgICAgaWYgKHRoaXMuY29udGVudFtpXSA9PT0ga2V5KSByZXR1cm4gaVxuICAgIHJldHVybiAtMVxuICB9LFxuXG4gIC8vIDo6IChzdHJpbmcpIOKGkiA/YW55XG4gIC8vIFJldHJpZXZlIHRoZSB2YWx1ZSBzdG9yZWQgdW5kZXIgYGtleWAsIG9yIHJldHVybiB1bmRlZmluZWQgd2hlblxuICAvLyBubyBzdWNoIGtleSBleGlzdHMuXG4gIGdldDogZnVuY3Rpb24oa2V5KSB7XG4gICAgdmFyIGZvdW5kID0gdGhpcy5maW5kKGtleSk7XG4gICAgcmV0dXJuIGZvdW5kID09IC0xID8gdW5kZWZpbmVkIDogdGhpcy5jb250ZW50W2ZvdW5kICsgMV1cbiAgfSxcblxuICAvLyA6OiAoc3RyaW5nLCBhbnksID9zdHJpbmcpIOKGkiBPcmRlcmVkTWFwXG4gIC8vIENyZWF0ZSBhIG5ldyBtYXAgYnkgcmVwbGFjaW5nIHRoZSB2YWx1ZSBvZiBga2V5YCB3aXRoIGEgbmV3XG4gIC8vIHZhbHVlLCBvciBhZGRpbmcgYSBiaW5kaW5nIHRvIHRoZSBlbmQgb2YgdGhlIG1hcC4gSWYgYG5ld0tleWAgaXNcbiAgLy8gZ2l2ZW4sIHRoZSBrZXkgb2YgdGhlIGJpbmRpbmcgd2lsbCBiZSByZXBsYWNlZCB3aXRoIHRoYXQga2V5LlxuICB1cGRhdGU6IGZ1bmN0aW9uKGtleSwgdmFsdWUsIG5ld0tleSkge1xuICAgIHZhciBzZWxmID0gbmV3S2V5ICYmIG5ld0tleSAhPSBrZXkgPyB0aGlzLnJlbW92ZShuZXdLZXkpIDogdGhpcztcbiAgICB2YXIgZm91bmQgPSBzZWxmLmZpbmQoa2V5KSwgY29udGVudCA9IHNlbGYuY29udGVudC5zbGljZSgpO1xuICAgIGlmIChmb3VuZCA9PSAtMSkge1xuICAgICAgY29udGVudC5wdXNoKG5ld0tleSB8fCBrZXksIHZhbHVlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgY29udGVudFtmb3VuZCArIDFdID0gdmFsdWU7XG4gICAgICBpZiAobmV3S2V5KSBjb250ZW50W2ZvdW5kXSA9IG5ld0tleTtcbiAgICB9XG4gICAgcmV0dXJuIG5ldyBPcmRlcmVkTWFwKGNvbnRlbnQpXG4gIH0sXG5cbiAgLy8gOjogKHN0cmluZykg4oaSIE9yZGVyZWRNYXBcbiAgLy8gUmV0dXJuIGEgbWFwIHdpdGggdGhlIGdpdmVuIGtleSByZW1vdmVkLCBpZiBpdCBleGlzdGVkLlxuICByZW1vdmU6IGZ1bmN0aW9uKGtleSkge1xuICAgIHZhciBmb3VuZCA9IHRoaXMuZmluZChrZXkpO1xuICAgIGlmIChmb3VuZCA9PSAtMSkgcmV0dXJuIHRoaXNcbiAgICB2YXIgY29udGVudCA9IHRoaXMuY29udGVudC5zbGljZSgpO1xuICAgIGNvbnRlbnQuc3BsaWNlKGZvdW5kLCAyKTtcbiAgICByZXR1cm4gbmV3IE9yZGVyZWRNYXAoY29udGVudClcbiAgfSxcblxuICAvLyA6OiAoc3RyaW5nLCBhbnkpIOKGkiBPcmRlcmVkTWFwXG4gIC8vIEFkZCBhIG5ldyBrZXkgdG8gdGhlIHN0YXJ0IG9mIHRoZSBtYXAuXG4gIGFkZFRvU3RhcnQ6IGZ1bmN0aW9uKGtleSwgdmFsdWUpIHtcbiAgICByZXR1cm4gbmV3IE9yZGVyZWRNYXAoW2tleSwgdmFsdWVdLmNvbmNhdCh0aGlzLnJlbW92ZShrZXkpLmNvbnRlbnQpKVxuICB9LFxuXG4gIC8vIDo6IChzdHJpbmcsIGFueSkg4oaSIE9yZGVyZWRNYXBcbiAgLy8gQWRkIGEgbmV3IGtleSB0byB0aGUgZW5kIG9mIHRoZSBtYXAuXG4gIGFkZFRvRW5kOiBmdW5jdGlvbihrZXksIHZhbHVlKSB7XG4gICAgdmFyIGNvbnRlbnQgPSB0aGlzLnJlbW92ZShrZXkpLmNvbnRlbnQuc2xpY2UoKTtcbiAgICBjb250ZW50LnB1c2goa2V5LCB2YWx1ZSk7XG4gICAgcmV0dXJuIG5ldyBPcmRlcmVkTWFwKGNvbnRlbnQpXG4gIH0sXG5cbiAgLy8gOjogKHN0cmluZywgc3RyaW5nLCBhbnkpIOKGkiBPcmRlcmVkTWFwXG4gIC8vIEFkZCBhIGtleSBhZnRlciB0aGUgZ2l2ZW4ga2V5LiBJZiBgcGxhY2VgIGlzIG5vdCBmb3VuZCwgdGhlIG5ld1xuICAvLyBrZXkgaXMgYWRkZWQgdG8gdGhlIGVuZC5cbiAgYWRkQmVmb3JlOiBmdW5jdGlvbihwbGFjZSwga2V5LCB2YWx1ZSkge1xuICAgIHZhciB3aXRob3V0ID0gdGhpcy5yZW1vdmUoa2V5KSwgY29udGVudCA9IHdpdGhvdXQuY29udGVudC5zbGljZSgpO1xuICAgIHZhciBmb3VuZCA9IHdpdGhvdXQuZmluZChwbGFjZSk7XG4gICAgY29udGVudC5zcGxpY2UoZm91bmQgPT0gLTEgPyBjb250ZW50Lmxlbmd0aCA6IGZvdW5kLCAwLCBrZXksIHZhbHVlKTtcbiAgICByZXR1cm4gbmV3IE9yZGVyZWRNYXAoY29udGVudClcbiAgfSxcblxuICAvLyA6OiAoKGtleTogc3RyaW5nLCB2YWx1ZTogYW55KSlcbiAgLy8gQ2FsbCB0aGUgZ2l2ZW4gZnVuY3Rpb24gZm9yIGVhY2gga2V5L3ZhbHVlIHBhaXIgaW4gdGhlIG1hcCwgaW5cbiAgLy8gb3JkZXIuXG4gIGZvckVhY2g6IGZ1bmN0aW9uKGYpIHtcbiAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRoaXMuY29udGVudC5sZW5ndGg7IGkgKz0gMilcbiAgICAgIGYodGhpcy5jb250ZW50W2ldLCB0aGlzLmNvbnRlbnRbaSArIDFdKTtcbiAgfSxcblxuICAvLyA6OiAodW5pb248T2JqZWN0LCBPcmRlcmVkTWFwPikg4oaSIE9yZGVyZWRNYXBcbiAgLy8gQ3JlYXRlIGEgbmV3IG1hcCBieSBwcmVwZW5kaW5nIHRoZSBrZXlzIGluIHRoaXMgbWFwIHRoYXQgZG9uJ3RcbiAgLy8gYXBwZWFyIGluIGBtYXBgIGJlZm9yZSB0aGUga2V5cyBpbiBgbWFwYC5cbiAgcHJlcGVuZDogZnVuY3Rpb24obWFwKSB7XG4gICAgbWFwID0gT3JkZXJlZE1hcC5mcm9tKG1hcCk7XG4gICAgaWYgKCFtYXAuc2l6ZSkgcmV0dXJuIHRoaXNcbiAgICByZXR1cm4gbmV3IE9yZGVyZWRNYXAobWFwLmNvbnRlbnQuY29uY2F0KHRoaXMuc3VidHJhY3QobWFwKS5jb250ZW50KSlcbiAgfSxcblxuICAvLyA6OiAodW5pb248T2JqZWN0LCBPcmRlcmVkTWFwPikg4oaSIE9yZGVyZWRNYXBcbiAgLy8gQ3JlYXRlIGEgbmV3IG1hcCBieSBhcHBlbmRpbmcgdGhlIGtleXMgaW4gdGhpcyBtYXAgdGhhdCBkb24ndFxuICAvLyBhcHBlYXIgaW4gYG1hcGAgYWZ0ZXIgdGhlIGtleXMgaW4gYG1hcGAuXG4gIGFwcGVuZDogZnVuY3Rpb24obWFwKSB7XG4gICAgbWFwID0gT3JkZXJlZE1hcC5mcm9tKG1hcCk7XG4gICAgaWYgKCFtYXAuc2l6ZSkgcmV0dXJuIHRoaXNcbiAgICByZXR1cm4gbmV3IE9yZGVyZWRNYXAodGhpcy5zdWJ0cmFjdChtYXApLmNvbnRlbnQuY29uY2F0KG1hcC5jb250ZW50KSlcbiAgfSxcblxuICAvLyA6OiAodW5pb248T2JqZWN0LCBPcmRlcmVkTWFwPikg4oaSIE9yZGVyZWRNYXBcbiAgLy8gQ3JlYXRlIGEgbWFwIGNvbnRhaW5pbmcgYWxsIHRoZSBrZXlzIGluIHRoaXMgbWFwIHRoYXQgZG9uJ3RcbiAgLy8gYXBwZWFyIGluIGBtYXBgLlxuICBzdWJ0cmFjdDogZnVuY3Rpb24obWFwKSB7XG4gICAgdmFyIHJlc3VsdCA9IHRoaXM7XG4gICAgbWFwID0gT3JkZXJlZE1hcC5mcm9tKG1hcCk7XG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBtYXAuY29udGVudC5sZW5ndGg7IGkgKz0gMilcbiAgICAgIHJlc3VsdCA9IHJlc3VsdC5yZW1vdmUobWFwLmNvbnRlbnRbaV0pO1xuICAgIHJldHVybiByZXN1bHRcbiAgfSxcblxuICAvLyA6OiAoKSDihpIgT2JqZWN0XG4gIC8vIFR1cm4gb3JkZXJlZCBtYXAgaW50byBhIHBsYWluIG9iamVjdC5cbiAgdG9PYmplY3Q6IGZ1bmN0aW9uKCkge1xuICAgIHZhciByZXN1bHQgPSB7fTtcbiAgICB0aGlzLmZvckVhY2goZnVuY3Rpb24oa2V5LCB2YWx1ZSkgeyByZXN1bHRba2V5XSA9IHZhbHVlOyB9KTtcbiAgICByZXR1cm4gcmVzdWx0XG4gIH0sXG5cbiAgLy8gOjogbnVtYmVyXG4gIC8vIFRoZSBhbW91bnQgb2Yga2V5cyBpbiB0aGlzIG1hcC5cbiAgZ2V0IHNpemUoKSB7XG4gICAgcmV0dXJuIHRoaXMuY29udGVudC5sZW5ndGggPj4gMVxuICB9XG59O1xuXG4vLyA6OiAoP3VuaW9uPE9iamVjdCwgT3JkZXJlZE1hcD4pIOKGkiBPcmRlcmVkTWFwXG4vLyBSZXR1cm4gYSBtYXAgd2l0aCB0aGUgZ2l2ZW4gY29udGVudC4gSWYgbnVsbCwgY3JlYXRlIGFuIGVtcHR5XG4vLyBtYXAuIElmIGdpdmVuIGFuIG9yZGVyZWQgbWFwLCByZXR1cm4gdGhhdCBtYXAgaXRzZWxmLiBJZiBnaXZlbiBhblxuLy8gb2JqZWN0LCBjcmVhdGUgYSBtYXAgZnJvbSB0aGUgb2JqZWN0J3MgcHJvcGVydGllcy5cbk9yZGVyZWRNYXAuZnJvbSA9IGZ1bmN0aW9uKHZhbHVlKSB7XG4gIGlmICh2YWx1ZSBpbnN0YW5jZW9mIE9yZGVyZWRNYXApIHJldHVybiB2YWx1ZVxuICB2YXIgY29udGVudCA9IFtdO1xuICBpZiAodmFsdWUpIGZvciAodmFyIHByb3AgaW4gdmFsdWUpIGNvbnRlbnQucHVzaChwcm9wLCB2YWx1ZVtwcm9wXSk7XG4gIHJldHVybiBuZXcgT3JkZXJlZE1hcChjb250ZW50KVxufTtcblxuZXhwb3J0IGRlZmF1bHQgT3JkZXJlZE1hcDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/orderedmap/dist/index.js\n");

/***/ })

};
;