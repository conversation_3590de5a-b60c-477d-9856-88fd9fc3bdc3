# frozen_string_literal: true

class Chats < ::ApplicationRepository
  sort_by :id, :desc

  def default_scope
    ::Chat.all
  end

  def filter_by_workspace_id(workspace_id)
    @scope.where(workspace_id: workspace_id)
  end

  def filter_by_chat_type(chat_type)
    @scope.where(chat_type: chat_type)
  end

  def filter_by_search(q)
    @scope.where('chats.name ILIKE ?', "%#{q}%")
  end

  def filter_by_user_id(user_id)
    @scope.joins(workspace: { workspaces_memberships: :membership}).where(memberships: { user_id: user_id})
  end
end
