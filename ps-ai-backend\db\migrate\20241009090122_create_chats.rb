class CreateChats < ActiveRecord::Migration[7.0]
  def change
    create_table :chats do |t|
      t.references :organization, :null => false
      t.references :model, :null => false
      t.references :account_plan, :null => true
      t.references :ap_table, :null => true
      t.bigint :creator_organization_user_id, :null => false
      t.string :openai_thread_id
      t.string :chat_type
      t.datetime :discarded_at, :index => true

      t.timestamps
    end
  end
end
