# frozen_string_literal: true

# Account Plan Table
class ApTable < ApplicationRecord
  include Discard::Model
  default_scope -> { kept }

  enum table_type: string_enum(
    'stakeholder_mapping', 'wallet_share', 'circumstantial_analysis',
    'svot', 'insight_and_perspective', 'historic_revenue',
    'current_revenue', 'current_opportunity', 'potential_opportunity',
    'revenue_forecast', 'missing_information', 'targeted_perception_development',
    'action_plan', 'top_action', 'client_meeting_schedule'
  )

  enum table_category: string_enum(
    'position', 'revenue', 'strategy'
  )

  belongs_to :account_plan
  
  # after_create :populate_items

  def self.positions_list
    [
      'stakeholder_mapping', 'wallet_share', 'circumstantial_analysis',
      'svot', 'insight_and_perspective'
    ]
  end
  
  def self.revenues_list
    [
      'historic_revenue', 'current_revenue', 'current_opportunity',
      'potential_opportunity', 'revenue_forecast'
    ]
  end

  def self.strategies_list
    [
      'missing_information', 'targeted_perception_development', 'action_plan',
      'top_action', 'client_meeting_schedule'
    ]
  end

  def self.ai_response_tables
    [
      # 'missing_information', THIS TABLE BECOME AUTO GENERATED USING SET OF RULES
      # 'targeted_perception_development', THIS TABLE BECOME USER INPUT
      'action_plan',
      # 'top_action', THIS TABLE BECOME USER INPUT
    ]
  end

  def populate_items
    if self.table_type == 'svot'
      self.populate_svot_items
    elsif self.table_type == 'targeted_perception_development'
      self.populate_targeted_perception_development_items
    end
  end

  def populate_svot_items
    ap_table_id = self.id

    if self.table_type != 'svot'
      return
    end

    ::AccountPlanTableItems::ApSvotItem.item_types.values.each do |t|
      curr = ::AccountPlanTableItems::ApSvotItem.find_by(ap_table_id: ap_table_id, item_type: t)

      if !curr.present?
        ::AccountPlanTableItems::ApSvotItem.create(ap_table_id: ap_table_id, item_type: t)
      end
    end
  end

  def populate_targeted_perception_development_items
    ap_table_id = self.id

    if self.table_type != 'targeted_perception_development'
      return
    end

    curr = ::AccountPlanTableItems::ApTargetedPerceptionDevelopmentItem.where(ap_table_id: ap_table_id).count

    if curr < 2
      (2 - curr).times do
        ::AccountPlanTableItems::ApTargetedPerceptionDevelopmentItem.create(ap_table_id: ap_table_id)
      end
    end
  end
end
