"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-dropcursor";
exports.ids = ["vendor-chunks/prosemirror-dropcursor"];
exports.modules = {

/***/ "(ssr)/./node_modules/prosemirror-dropcursor/dist/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/prosemirror-dropcursor/dist/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dropCursor: () => (/* binding */ dropCursor)\n/* harmony export */ });\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/prosemirror-transform/dist/index.js\");\n\n\n\n/**\nCreate a plugin that, when added to a ProseMirror instance,\ncauses a decoration to show up at the drop position when something\nis dragged over the editor.\n\nNodes may add a `disableDropCursor` property to their spec to\ncontrol the showing of a drop cursor inside them. This may be a\nboolean or a function, which will be called with a view and a\nposition, and should return a boolean.\n*/\nfunction dropCursor(options = {}) {\n    return new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        view(editorView) { return new DropCursorView(editorView, options); }\n    });\n}\nclass DropCursorView {\n    constructor(editorView, options) {\n        var _a;\n        this.editorView = editorView;\n        this.cursorPos = null;\n        this.element = null;\n        this.timeout = -1;\n        this.width = (_a = options.width) !== null && _a !== void 0 ? _a : 1;\n        this.color = options.color === false ? undefined : (options.color || \"black\");\n        this.class = options.class;\n        this.handlers = [\"dragover\", \"dragend\", \"drop\", \"dragleave\"].map(name => {\n            let handler = (e) => { this[name](e); };\n            editorView.dom.addEventListener(name, handler);\n            return { name, handler };\n        });\n    }\n    destroy() {\n        this.handlers.forEach(({ name, handler }) => this.editorView.dom.removeEventListener(name, handler));\n    }\n    update(editorView, prevState) {\n        if (this.cursorPos != null && prevState.doc != editorView.state.doc) {\n            if (this.cursorPos > editorView.state.doc.content.size)\n                this.setCursor(null);\n            else\n                this.updateOverlay();\n        }\n    }\n    setCursor(pos) {\n        if (pos == this.cursorPos)\n            return;\n        this.cursorPos = pos;\n        if (pos == null) {\n            this.element.parentNode.removeChild(this.element);\n            this.element = null;\n        }\n        else {\n            this.updateOverlay();\n        }\n    }\n    updateOverlay() {\n        let $pos = this.editorView.state.doc.resolve(this.cursorPos);\n        let isBlock = !$pos.parent.inlineContent, rect;\n        let editorDOM = this.editorView.dom, editorRect = editorDOM.getBoundingClientRect();\n        let scaleX = editorRect.width / editorDOM.offsetWidth, scaleY = editorRect.height / editorDOM.offsetHeight;\n        if (isBlock) {\n            let before = $pos.nodeBefore, after = $pos.nodeAfter;\n            if (before || after) {\n                let node = this.editorView.nodeDOM(this.cursorPos - (before ? before.nodeSize : 0));\n                if (node) {\n                    let nodeRect = node.getBoundingClientRect();\n                    let top = before ? nodeRect.bottom : nodeRect.top;\n                    if (before && after)\n                        top = (top + this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top) / 2;\n                    let halfWidth = (this.width / 2) * scaleY;\n                    rect = { left: nodeRect.left, right: nodeRect.right, top: top - halfWidth, bottom: top + halfWidth };\n                }\n            }\n        }\n        if (!rect) {\n            let coords = this.editorView.coordsAtPos(this.cursorPos);\n            let halfWidth = (this.width / 2) * scaleX;\n            rect = { left: coords.left - halfWidth, right: coords.left + halfWidth, top: coords.top, bottom: coords.bottom };\n        }\n        let parent = this.editorView.dom.offsetParent;\n        if (!this.element) {\n            this.element = parent.appendChild(document.createElement(\"div\"));\n            if (this.class)\n                this.element.className = this.class;\n            this.element.style.cssText = \"position: absolute; z-index: 50; pointer-events: none;\";\n            if (this.color) {\n                this.element.style.backgroundColor = this.color;\n            }\n        }\n        this.element.classList.toggle(\"prosemirror-dropcursor-block\", isBlock);\n        this.element.classList.toggle(\"prosemirror-dropcursor-inline\", !isBlock);\n        let parentLeft, parentTop;\n        if (!parent || parent == document.body && getComputedStyle(parent).position == \"static\") {\n            parentLeft = -pageXOffset;\n            parentTop = -pageYOffset;\n        }\n        else {\n            let rect = parent.getBoundingClientRect();\n            let parentScaleX = rect.width / parent.offsetWidth, parentScaleY = rect.height / parent.offsetHeight;\n            parentLeft = rect.left - parent.scrollLeft * parentScaleX;\n            parentTop = rect.top - parent.scrollTop * parentScaleY;\n        }\n        this.element.style.left = (rect.left - parentLeft) / scaleX + \"px\";\n        this.element.style.top = (rect.top - parentTop) / scaleY + \"px\";\n        this.element.style.width = (rect.right - rect.left) / scaleX + \"px\";\n        this.element.style.height = (rect.bottom - rect.top) / scaleY + \"px\";\n    }\n    scheduleRemoval(timeout) {\n        clearTimeout(this.timeout);\n        this.timeout = setTimeout(() => this.setCursor(null), timeout);\n    }\n    dragover(event) {\n        if (!this.editorView.editable)\n            return;\n        let pos = this.editorView.posAtCoords({ left: event.clientX, top: event.clientY });\n        let node = pos && pos.inside >= 0 && this.editorView.state.doc.nodeAt(pos.inside);\n        let disableDropCursor = node && node.type.spec.disableDropCursor;\n        let disabled = typeof disableDropCursor == \"function\"\n            ? disableDropCursor(this.editorView, pos, event)\n            : disableDropCursor;\n        if (pos && !disabled) {\n            let target = pos.pos;\n            if (this.editorView.dragging && this.editorView.dragging.slice) {\n                let point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.dropPoint)(this.editorView.state.doc, target, this.editorView.dragging.slice);\n                if (point != null)\n                    target = point;\n            }\n            this.setCursor(target);\n            this.scheduleRemoval(5000);\n        }\n    }\n    dragend() {\n        this.scheduleRemoval(20);\n    }\n    drop() {\n        this.scheduleRemoval(20);\n    }\n    dragleave(event) {\n        if (!this.editorView.dom.contains(event.relatedTarget))\n            this.setCursor(null);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/prosemirror-dropcursor/dist/index.js\n");

/***/ })

};
;