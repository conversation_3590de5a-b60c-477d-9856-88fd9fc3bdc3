"use client";

import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { IconHelpCircleFilled } from "@tabler/icons-react";
import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";

export { StakeholderMappingTable } from "./position-tables/stakeholder-mapping-table";
export { WalletShareTable } from "./position-tables/wallet-share-table";
export { CircumstantialAnalysisTable } from "./position-tables/circumstantial-analysis-table";
export { SvotTable } from "./position-tables/svot-table";
export { InsightsAndPerspectiveTable } from "./position-tables/insights-and-perspective-table";

export { HistoricRevenueTable } from "./revenue-tables/historic-revenue-table";
export { CurrentRevenueTable } from "./revenue-tables/current-revenue-table";
export { CurrentOpportunitiesTable } from "./revenue-tables/current-opportunities-table";
export { PotentialOpportunitiesTable } from "./revenue-tables/potential-opportunities-table";
export { RevenueForecastTable } from "./revenue-tables/revenue-forecast-table";

export { InformationNeededTable } from "./strategy-tables/information-needed-table";
export { StrategicPerceptionTable } from "./strategy-tables/strategic-perception-table";
export { StrategicConsiderationTable } from "./strategy-tables/strategic-considerations-table";
export { StrategicActionTable } from "./strategy-tables/strategic-action-table";
export { MeetingScheduleTable } from "./strategy-tables/meeting-schedule-table";

type AccountPlanInputProps = {};

type AccountPlanInputContextProps = {
  containerWidth: number | null;
} & AccountPlanInputProps;

const AccountPlanInputContext =
  createContext<AccountPlanInputContextProps | null>(null);

export function useAccountPlanInput() {
  const context = useContext(AccountPlanInputContext);

  if (!context) {
    throw new Error(
      "useAccountPlanInput must be used within a <AccountPlanTableContainer />"
    );
  }

  return context;
}

export const TableGroup = ({
  title,
  tooltip,
  children,
}: {
  title: string;
  tooltip: string;
  children: ReactNode;
}) => {
  return (
    <div className="h-full bg-white">
      <div className="mb-res-y-lg flex items-center justify-center gap-res-y-xs">
        <p className="text-center text-xl font-bold text-primary-500">
          {title}
        </p>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <IconHelpCircleFilled className="size-res-y-xl text-primary-500" />
            </TooltipTrigger>
            <TooltipContent
              className="w-[30vw] flex-wrap whitespace-normal px-res-x-sm py-res-y-sm text-base font-semibold"
              side="bottom"
              align="center"
            >
              {tooltip}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="flex h-full w-full flex-col gap-2">{children}</div>
    </div>
  );
};

export const AccountPlanTableContainer = ({
  children,
}: {
  children: ReactNode;
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] =
    useState<AccountPlanInputContextProps["containerWidth"]>(0);

  useEffect(() => {
    const container = containerRef.current;

    const changeWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current?.clientWidth);
      }
    };
    const resizeObserver = new ResizeObserver(() => {
      changeWidth();
    });

    if (container) {
      resizeObserver.observe(container);
    }

    return () => {
      if (container) {
        resizeObserver.unobserve(container);
      }
    };
  }, []);

  return (
    <AccountPlanInputContext.Provider value={{ containerWidth }}>
      <div ref={containerRef} className="h-full w-full">
        {children}
      </div>
    </AccountPlanInputContext.Provider>
  );
};
