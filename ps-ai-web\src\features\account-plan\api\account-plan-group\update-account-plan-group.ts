import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { AccountPlanGroupsData, AccountPlanGroupsPayload } from "../../types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const updateAccountPlanGroups = ({
  accountGroupId,
  data,
}: {
  accountGroupId: number;
  data?: AccountPlanGroupsPayload;
}): ApiResponse<AccountPlanGroupsData> => {
  return api.put(API_ROUTES.ACCOUNT_PLAN_GROUPS_DETAIL(accountGroupId), data);
};

type UseUpdateAccountPlanGroupsOptions = {
  mutationConfig?: MutationConfig<typeof updateAccountPlanGroups>;
};

export const useUpdateAccountPlanGroups = ({
  mutationConfig,
}: UseUpdateAccountPlanGroupsOptions) => {
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);

      if (invalidate) {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.ACCOUNT_PLANS_GROUPS],
        });
      }
    },
    ...restConfig,
    mutationFn: updateAccountPlanGroups,
  });
};
