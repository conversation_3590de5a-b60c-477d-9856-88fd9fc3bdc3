import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APClientMeetingSchedule } from "@/features/account-plan/types/strategy-types";

export const getClientMeetingScheduleDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APClientMeetingSchedule> => {
  return api.get(
    API_ROUTES.ACCOUNT_PLANS_CLIENT_MEETING_SCHEDULE_DETAIL(accountId, id)
  );
};

export const getClientMeetingScheduleDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_CLIENT_MEETING_SCHEDULE,
      id,
    ],
    queryFn: () => getClientMeetingScheduleDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseClientMeetingScheduleDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getClientMeetingScheduleDetail>;
  options?: Partial<
    ReturnType<typeof getClientMeetingScheduleDetailQueryOptions>
  >;
};

export const useClientMeetingScheduleDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseClientMeetingScheduleDetailOptions) => {
  const clientMeetingScheduleDetailQuery = useQuery({
    ...getClientMeetingScheduleDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...clientMeetingScheduleDetailQuery,
    clientMeetingScheduleDetail: clientMeetingScheduleDetailQuery.data?.data,
  };
};
