# frozen_string_literal: true

module V1
    class TemplateCategoriesController < ApiController
      authorize_auth_token! :all
  
      def create
        input = ::V1::TemplateCategoryCreationInput.new(request_body)
        validate! input, capture_failure: true
  
        template_category = service.create(input.output)
  
        render_json template_category, use: :format, status: :created
      end
  
      def index
        template_categories = service.index(query_params)
  
        render_json_array template_categories, use: :format, status: :ok
      end
  
      def destroy
        service.destroy(params[:id])
  
        render_empty_json({}, status: :ok)
      end
  
      private
  
      def default_output
        ::V1::TemplateCategoryOutput
      end
  
      def service
        @service ||= ::TemplateCategoryService.new(current_user_data)
      end
    end
  end
  